import { contextBridge } from 'electron'
import { CommonApi } from '@preload/common'
import { DatabaseApi } from '@preload/database'
import { StorageApi } from '@preload/storage'
import { ClipboardApi } from '@preload/clipboard'
import { windowAPI } from '@preload/window'

const SmartApi = {
  common: CommonApi,
  database: DatabaseApi,
  storage: StorageApi,
  clipboard: ClipboardApi,
  window: windowAPI,
}

contextBridge.exposeInMainWorld('smart', SmartApi)
