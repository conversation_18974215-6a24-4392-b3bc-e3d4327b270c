import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import {
  IPC_STORAGE_SET,
  IPC_STORAGE_GET,
  IPC_STORAGE_HAS,
  IPC_STORAGE_DELETE,
  IPC_STORAGE_CLEAR,
  IPC_STORAGE_RESET,
} from '@shared/ipc-common'

export const StorageApi: StorageApi = {
  set: (
    key: DotNotationKeyOf<SchemaType>,
    value: DotNotationValueOf<SchemaType, typeof key>
  ) => {
    return ipcRenderer.invoke(IPC_STORAGE_SET, key, value)
  },
  get: (key: DotNotationKeyOf<SchemaType>, defaultValue: any) => {
    return ipcRenderer.invoke(IPC_STORAGE_GET, key, defaultValue)
  },
  has: (key: DotNotationKeyOf<SchemaType>) => {
    return ipcRenderer.invoke(IPC_STORAGE_HAS, key)
  },
  delete: (key: DotNotation<PERSON>eyOf<SchemaType>) => {
    return ipcRenderer.invoke(IPC_STORAGE_DELETE, key)
  },
  clear: () => {
    return ipcRenderer.invoke(IPC_STORAGE_CLEAR)
  },
  reset: () => {
    return ipcRenderer.invoke(IPC_STORAGE_RESET)
  },
}
