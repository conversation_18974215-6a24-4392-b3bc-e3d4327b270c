import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import {
  IPC_DATABASE_PUT,
  IPC_DATABASE_GET,
  IPC_DATABASE_REMOVE,
  IPC_DATABASE_BULK_DOCS,
  IPC_DATABASE_ALL_DOCS
} from '@shared/ipc-common'

export const DatabaseApi = {
  put: (doc: DbDoc) => {
    return ipcRenderer.invoke(IPC_DATABASE_PUT, doc)
  },
  get: (id: string) => {
    return ipcRenderer.invoke(IPC_DATABASE_GET, id)
  },
  remove: (docOrId: DbDoc | string) => {
    return ipcRenderer.invoke(IPC_DATABASE_REMOVE, docOrId)
  },
  bulkDocs: (docs: DbDoc[]) => {
    return ipcRenderer.invoke(IPC_DATABASE_BULK_DOCS, docs)
  },
  allDocs: (idStartsWithOrIds?: string | string[]) => {
    return ipcRenderer.invoke(IPC_DATABASE_ALL_DOCS, idStartsWithOrIds)
  }
}
