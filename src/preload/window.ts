/**
 * 窗口管理预加载脚本
 * 为渲染进程提供窗口管理功能的安全接口
 */

import { contextBridge, ipcRenderer } from 'electron'
import {
  IPC_WINDOW_CREATE,
  IPC_WINDOW_GET_INFO,
  IPC_WINDOW_GET_ALL,
  IPC_WINDOW_GET_CURRENT,
  IPC_WINDOW_CLOSE,
  IPC_WINDOW_HIDE,
  IPC_WINDOW_SHOW,
  IPC_WINDOW_MINIMIZE,
  IPC_WINDOW_MAXIMIZE,
  IPC_WINDOW_RESTORE,
  IPC_WINDOW_FOCUS,
  IPC_WINDOW_SET_BOUNDS,
  IPC_WINDOW_GET_BOUNDS,
  IPC_WINDOW_SET_ALWAYS_ON_TOP,
  IPC_WINDOW_GET_STATUS
} from '@shared/ipc-common'


/**
 * 窗口管理API接口
 */
export interface WindowAPI {
  /**
   * 创建新窗口
   * @param options - 窗口创建选项
   * @returns 操作结果
   */
  createWindow(options: WindowCreateOptions): Promise<WindowOperationResult>

  /**
   * 获取窗口信息
   * @param windowId - 窗口ID
   * @returns 操作结果
   */
  getWindowInfo(windowId: number): Promise<WindowOperationResult>

  /**
   * 获取所有窗口信息
   * @returns 操作结果
   */
  getAllWindows(): Promise<WindowOperationResult>

  /**
   * 获取当前窗口信息
   * @returns 操作结果
   */
  getCurrentWindow(): Promise<WindowOperationResult>

  /**
   * 关闭指定窗口
   * @param windowId - 窗口ID
   * @returns 操作结果
   */
  closeWindow(windowId: number): Promise<WindowOperationResult>

  /**
   * 隐藏指定窗口
   * @param windowId - 窗口ID
   * @returns 操作结果
   */
  hideWindow(windowId: number): Promise<WindowOperationResult>

  /**
   * 显示指定窗口
   * @param windowId - 窗口ID
   * @returns 操作结果
   */
  showWindow(windowId: number): Promise<WindowOperationResult>

  /**
   * 最小化指定窗口
   * @param windowId - 窗口ID
   * @returns 操作结果
   */
  minimizeWindow(windowId: number): Promise<WindowOperationResult>

  /**
   * 最大化指定窗口
   * @param windowId - 窗口ID
   * @returns 操作结果
   */
  maximizeWindow(windowId: number): Promise<WindowOperationResult>

  /**
   * 恢复指定窗口
   * @param windowId - 窗口ID
   * @returns 操作结果
   */
  restoreWindow(windowId: number): Promise<WindowOperationResult>

  /**
   * 聚焦指定窗口
   * @param windowId - 窗口ID
   * @returns 操作结果
   */
  focusWindow(windowId: number): Promise<WindowOperationResult>

  /**
   * 设置窗口位置和大小
   * @param windowId - 窗口ID
   * @param bounds - 位置和大小
   * @returns 操作结果
   */
  setWindowBounds(windowId: number, bounds: WindowBounds): Promise<WindowOperationResult>

  /**
   * 获取窗口位置和大小
   * @param windowId - 窗口ID
   * @returns 操作结果
   */
  getWindowBounds(windowId: number): Promise<WindowOperationResult>

  /**
   * 设置窗口置顶
   * @param windowId - 窗口ID
   * @param flag - 是否置顶
   * @returns 操作结果
   */
  setAlwaysOnTop(windowId: number, flag: boolean): Promise<WindowOperationResult>

  /**
   * 获取窗口管理器状态
   * @returns 操作结果
   */
  getStatus(): Promise<WindowOperationResult>
}

/**
 * 窗口管理API实现
 */
const windowAPI: WindowAPI = {
  /**
   * 创建新窗口
   */
  async createWindow(options: WindowCreateOptions): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_CREATE, options)
    } catch (error) {
      console.error('创建窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 获取窗口信息
   */
  async getWindowInfo(windowId: number): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_GET_INFO, windowId)
    } catch (error) {
      console.error('获取窗口信息失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 获取所有窗口信息
   */
  async getAllWindows(): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_GET_ALL)
    } catch (error) {
      console.error('获取所有窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 获取当前窗口信息
   */
  async getCurrentWindow(): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_GET_CURRENT)
    } catch (error) {
      console.error('获取当前窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 关闭指定窗口
   */
  async closeWindow(windowId: number): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_CLOSE, windowId)
    } catch (error) {
      console.error('关闭窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 隐藏指定窗口
   */
  async hideWindow(windowId: number): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_HIDE, windowId)
    } catch (error) {
      console.error('隐藏窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 显示指定窗口
   */
  async showWindow(windowId: number): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_SHOW, windowId)
    } catch (error) {
      console.error('显示窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 最小化指定窗口
   */
  async minimizeWindow(windowId: number): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_MINIMIZE, windowId)
    } catch (error) {
      console.error('最小化窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 最大化指定窗口
   */
  async maximizeWindow(windowId: number): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_MAXIMIZE, windowId)
    } catch (error) {
      console.error('最大化窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 恢复指定窗口
   */
  async restoreWindow(windowId: number): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_RESTORE, windowId)
    } catch (error) {
      console.error('恢复窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 聚焦指定窗口
   */
  async focusWindow(windowId: number): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_FOCUS, windowId)
    } catch (error) {
      console.error('聚焦窗口失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 设置窗口位置和大小
   */
  async setWindowBounds(windowId: number, bounds: WindowBounds): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_SET_BOUNDS, windowId, bounds)
    } catch (error) {
      console.error('设置窗口位置和大小失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 获取窗口位置和大小
   */
  async getWindowBounds(windowId: number): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_GET_BOUNDS, windowId)
    } catch (error) {
      console.error('获取窗口位置和大小失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 设置窗口置顶
   */
  async setAlwaysOnTop(windowId: number, flag: boolean): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_SET_ALWAYS_ON_TOP, windowId, flag)
    } catch (error) {
      console.error('设置窗口置顶失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  },

  /**
   * 获取窗口管理器状态
   */
  async getStatus(): Promise<WindowOperationResult> {
    try {
      return await ipcRenderer.invoke(IPC_WINDOW_GET_STATUS)
    } catch (error) {
      console.error('获取窗口管理器状态失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }
}

// 暴露窗口管理API到渲染进程
contextBridge.exposeInMainWorld('windowAPI', windowAPI)

export { windowAPI }    