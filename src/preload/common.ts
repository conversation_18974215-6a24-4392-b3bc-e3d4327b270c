import { ipc<PERSON><PERSON><PERSON> } from 'electron'
// import { isMac, isLinux, isWindows } from '@main/common/utils/device'
// import { isDev, isProd } from '@main/common/utils/mode'
import {
  IPC_COMMON_DEBUG,
  IPC_COMMON_IS_DARK_THEME,
  IPC_COMMON_SAVE_FILE,
  IPC_COMMON_SELECT_FILE,
  IPC_COMMON_SET_THEME,
} from '@shared/ipc-common'

export const CommonApi: CommonApi = {
  debug: () => ipcRenderer.invoke(IPC_COMMON_DEBUG),
  isMac: () => true,
  isLinux: () => true,
  isWindows: () => true,
  isDev: () => true,
  isProd: () => true,
  // theme
  setTheme: (theme: Theme) => ipcRenderer.invoke(IPC_COMMON_SET_THEME, theme),
  isDarkTheme: () => ipc<PERSON>enderer.invoke(IPC_COMMON_IS_DARK_THEME),
  // file
  selectFile: (options: Electron.OpenDialogOptions) =>
    ipcRenderer.invoke(IPC_COMMON_SELECT_FILE, options),
  saveFile: (data: string | Buffer) =>
    ipcRenderer.invoke(IPC_COMMON_SAVE_FILE, data),
}
