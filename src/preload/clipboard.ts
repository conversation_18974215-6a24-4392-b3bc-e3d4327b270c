import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import {
  IPC_CLIPBOARD_HAS,
  IPC_CLIPBOARD_SET,
  IPC_CLIPBOARD_GET,
  IPC_CLIPBOARD_CLEAR,
  IPC_CLIPBOARD_WATCH_HANDLER,
} from '@shared/ipc-common'

export const ClipboardApi: ClipboardApi = {
  watchHandler: (callback: (data: ClipboardData) => void) => {
    const handler = (_event: any, data: ClipboardData) => {
      callback(data)
    }

    // 监听主进程发送的剪贴板变化事件
    ipcRenderer.on(IPC_CLIPBOARD_WATCH_HANDLER, handler)

    // 返回取消监听的函数
    return () => {
      ipcRenderer.removeListener(IPC_CLIPBOARD_WATCH_HANDLER, handler)
    }
  },
  has: () => {
    return ipcRenderer.invoke(IPC_CLIPBOARD_HAS)
  },
  set: (data: ClipboardData) => {
    return ipcRenderer.invoke(IPC_CLIPBOARD_SET, data)
  },
  get: () => {
    return ipcRenderer.invoke(IPC_CLIPBOARD_GET)
  },
  clear: () => {
    return ipcRenderer.invoke(IPC_CLIPBOARD_CLEAR)
  },
}
