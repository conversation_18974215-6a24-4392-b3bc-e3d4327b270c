// common
export const IPC_COMMON_DEBUG = 'common:debug'
export const IPC_COMMON_SET_THEME = 'common:set-theme'
export const IPC_COMMON_IS_DARK_THEME = 'common:is-dark-theme'

// common file
export const IPC_COMMON_SELECT_FILE = 'common:select-file'
export const IPC_COMMON_SAVE_FILE = 'common:save-file'

// database
export const IPC_DATABASE_PUT = 'database:put'
export const IPC_DATABASE_GET = 'database:get'
export const IPC_DATABASE_REMOVE = 'database:remove'
export const IPC_DATABASE_BULK_DOCS = 'database:bulk-docs'
export const IPC_DATABASE_ALL_DOCS = 'database:all-docs'
export const IPC_DATABASE_CLEAR = 'database:clear'
export const IPC_DATABASE_CLOSE = 'database:close'

// storage
export const IPC_STORAGE_SET = 'storage:set'
export const IPC_STORAGE_GET = 'storage:get'
export const IPC_STORAGE_HAS = 'storage:has'
export const IPC_STORAGE_DELETE = 'storage:delete'
export const IPC_STORAGE_RESET = 'storage:reset'
export const IPC_STORAGE_CLEAR = 'storage:clear'

// clipboard
export const IPC_CLIPBOARD_WATCH_HANDLER = 'clipboard:watch-handler'
export const IPC_CLIPBOARD_HAS = 'clipboard:has-data'
export const IPC_CLIPBOARD_SET = 'clipboard:set'
export const IPC_CLIPBOARD_GET = 'clipboard:get'
export const IPC_CLIPBOARD_CLEAR = 'clipboard:clear'

// window - 窗口管理相关
export const IPC_WINDOW_CREATE = 'window:create'
export const IPC_WINDOW_GET_INFO = 'window:get-info'
export const IPC_WINDOW_GET_ALL = 'window:get-all'
export const IPC_WINDOW_GET_CURRENT = 'window:get-current'
export const IPC_WINDOW_CLOSE = 'window:close'
export const IPC_WINDOW_HIDE = 'window:hide'
export const IPC_WINDOW_SHOW = 'window:show'
export const IPC_WINDOW_MINIMIZE = 'window:minimize'
export const IPC_WINDOW_MAXIMIZE = 'window:maximize'
export const IPC_WINDOW_RESTORE = 'window:restore'
export const IPC_WINDOW_FOCUS = 'window:focus'
export const IPC_WINDOW_SET_BOUNDS = 'window:set-bounds'
export const IPC_WINDOW_GET_BOUNDS = 'window:get-bounds'
export const IPC_WINDOW_SET_ALWAYS_ON_TOP = 'window:set-always-on-top'
export const IPC_WINDOW_GET_STATUS = 'window:get-status'
