/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.

// You should NOT make any changes in this file as it will be overwritten.

// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AppRouteImport } from './routes/app'
import { Route as IndexRouteImport } from './routes/index'
import { Route as SearchIndexRouteImport } from './routes/search/index'
import { Route as AppIndexRouteImport } from './routes/app/index'
import { Route as ToolsQuickToolbarRouteImport } from './routes/tools/quick-toolbar'
import { Route as ToolsQuickSearchRouteImport } from './routes/tools/quick-search'
import { Route as PluginsTranslateRouteImport } from './routes/plugins/translate'
import { Route as PluginsClipboardHistoryRouteImport } from './routes/plugins/clipboard-history'
import { Route as AppSettingsRouteImport } from './routes/app/settings'
import { Route as AppChatRouteImport } from './routes/app/chat'
import { Route as AppFlashMemoryIndexRouteImport } from './routes/app/flash-memory/index'
import { Route as AppCalendarIndexRouteImport } from './routes/app/calendar/index'
import { Route as AppSettingsShortcutsRouteImport } from './routes/app/settings/shortcuts'
import { Route as AppSettingsModelProvidersRouteImport } from './routes/app/settings/model-providers'
import { Route as AppSettingsDefaultModelsRouteImport } from './routes/app/settings/default-models'
import { Route as AppSettingsBaseRouteImport } from './routes/app/settings/base'
import { Route as AppSettingsAppearanceRouteImport } from './routes/app/settings/appearance'
import { Route as AppSettingsAiRouteImport } from './routes/app/settings/ai'

const AppRoute = AppRouteImport.update({
  id: '/app',
  path: '/app',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const SearchIndexRoute = SearchIndexRouteImport.update({
  id: '/search/',
  path: '/search/',
  getParentRoute: () => rootRouteImport,
} as any)
const AppIndexRoute = AppIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AppRoute,
} as any)
const ToolsQuickToolbarRoute = ToolsQuickToolbarRouteImport.update({
  id: '/tools/quick-toolbar',
  path: '/tools/quick-toolbar',
  getParentRoute: () => rootRouteImport,
} as any)
const ToolsQuickSearchRoute = ToolsQuickSearchRouteImport.update({
  id: '/tools/quick-search',
  path: '/tools/quick-search',
  getParentRoute: () => rootRouteImport,
} as any)
const PluginsTranslateRoute = PluginsTranslateRouteImport.update({
  id: '/plugins/translate',
  path: '/plugins/translate',
  getParentRoute: () => rootRouteImport,
} as any)
const PluginsClipboardHistoryRoute = PluginsClipboardHistoryRouteImport.update({
  id: '/plugins/clipboard-history',
  path: '/plugins/clipboard-history',
  getParentRoute: () => rootRouteImport,
} as any)
const AppSettingsRoute = AppSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => AppRoute,
} as any)
const AppChatRoute = AppChatRouteImport.update({
  id: '/chat',
  path: '/chat',
  getParentRoute: () => AppRoute,
} as any)
const AppFlashMemoryIndexRoute = AppFlashMemoryIndexRouteImport.update({
  id: '/flash-memory/',
  path: '/flash-memory/',
  getParentRoute: () => AppRoute,
} as any)
const AppCalendarIndexRoute = AppCalendarIndexRouteImport.update({
  id: '/calendar/',
  path: '/calendar/',
  getParentRoute: () => AppRoute,
} as any)
const AppSettingsShortcutsRoute = AppSettingsShortcutsRouteImport.update({
  id: '/shortcuts',
  path: '/shortcuts',
  getParentRoute: () => AppSettingsRoute,
} as any)
const AppSettingsModelProvidersRoute =
  AppSettingsModelProvidersRouteImport.update({
    id: '/model-providers',
    path: '/model-providers',
    getParentRoute: () => AppSettingsRoute,
  } as any)
const AppSettingsDefaultModelsRoute =
  AppSettingsDefaultModelsRouteImport.update({
    id: '/default-models',
    path: '/default-models',
    getParentRoute: () => AppSettingsRoute,
  } as any)
const AppSettingsBaseRoute = AppSettingsBaseRouteImport.update({
  id: '/base',
  path: '/base',
  getParentRoute: () => AppSettingsRoute,
} as any)
const AppSettingsAppearanceRoute = AppSettingsAppearanceRouteImport.update({
  id: '/appearance',
  path: '/appearance',
  getParentRoute: () => AppSettingsRoute,
} as any)
const AppSettingsAiRoute = AppSettingsAiRouteImport.update({
  id: '/ai',
  path: '/ai',
  getParentRoute: () => AppSettingsRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/app': typeof AppRouteWithChildren
  '/app/chat': typeof AppChatRoute
  '/app/settings': typeof AppSettingsRouteWithChildren
  '/plugins/clipboard-history': typeof PluginsClipboardHistoryRoute
  '/plugins/translate': typeof PluginsTranslateRoute
  '/tools/quick-search': typeof ToolsQuickSearchRoute
  '/tools/quick-toolbar': typeof ToolsQuickToolbarRoute
  '/app/': typeof AppIndexRoute
  '/search': typeof SearchIndexRoute
  '/app/settings/ai': typeof AppSettingsAiRoute
  '/app/settings/appearance': typeof AppSettingsAppearanceRoute
  '/app/settings/base': typeof AppSettingsBaseRoute
  '/app/settings/default-models': typeof AppSettingsDefaultModelsRoute
  '/app/settings/model-providers': typeof AppSettingsModelProvidersRoute
  '/app/settings/shortcuts': typeof AppSettingsShortcutsRoute
  '/app/calendar': typeof AppCalendarIndexRoute
  '/app/flash-memory': typeof AppFlashMemoryIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/app/chat': typeof AppChatRoute
  '/app/settings': typeof AppSettingsRouteWithChildren
  '/plugins/clipboard-history': typeof PluginsClipboardHistoryRoute
  '/plugins/translate': typeof PluginsTranslateRoute
  '/tools/quick-search': typeof ToolsQuickSearchRoute
  '/tools/quick-toolbar': typeof ToolsQuickToolbarRoute
  '/app': typeof AppIndexRoute
  '/search': typeof SearchIndexRoute
  '/app/settings/ai': typeof AppSettingsAiRoute
  '/app/settings/appearance': typeof AppSettingsAppearanceRoute
  '/app/settings/base': typeof AppSettingsBaseRoute
  '/app/settings/default-models': typeof AppSettingsDefaultModelsRoute
  '/app/settings/model-providers': typeof AppSettingsModelProvidersRoute
  '/app/settings/shortcuts': typeof AppSettingsShortcutsRoute
  '/app/calendar': typeof AppCalendarIndexRoute
  '/app/flash-memory': typeof AppFlashMemoryIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/app': typeof AppRouteWithChildren
  '/app/chat': typeof AppChatRoute
  '/app/settings': typeof AppSettingsRouteWithChildren
  '/plugins/clipboard-history': typeof PluginsClipboardHistoryRoute
  '/plugins/translate': typeof PluginsTranslateRoute
  '/tools/quick-search': typeof ToolsQuickSearchRoute
  '/tools/quick-toolbar': typeof ToolsQuickToolbarRoute
  '/app/': typeof AppIndexRoute
  '/search/': typeof SearchIndexRoute
  '/app/settings/ai': typeof AppSettingsAiRoute
  '/app/settings/appearance': typeof AppSettingsAppearanceRoute
  '/app/settings/base': typeof AppSettingsBaseRoute
  '/app/settings/default-models': typeof AppSettingsDefaultModelsRoute
  '/app/settings/model-providers': typeof AppSettingsModelProvidersRoute
  '/app/settings/shortcuts': typeof AppSettingsShortcutsRoute
  '/app/calendar/': typeof AppCalendarIndexRoute
  '/app/flash-memory/': typeof AppFlashMemoryIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/app'
    | '/app/chat'
    | '/app/settings'
    | '/plugins/clipboard-history'
    | '/plugins/translate'
    | '/tools/quick-search'
    | '/tools/quick-toolbar'
    | '/app/'
    | '/search'
    | '/app/settings/ai'
    | '/app/settings/appearance'
    | '/app/settings/base'
    | '/app/settings/default-models'
    | '/app/settings/model-providers'
    | '/app/settings/shortcuts'
    | '/app/calendar'
    | '/app/flash-memory'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/app/chat'
    | '/app/settings'
    | '/plugins/clipboard-history'
    | '/plugins/translate'
    | '/tools/quick-search'
    | '/tools/quick-toolbar'
    | '/app'
    | '/search'
    | '/app/settings/ai'
    | '/app/settings/appearance'
    | '/app/settings/base'
    | '/app/settings/default-models'
    | '/app/settings/model-providers'
    | '/app/settings/shortcuts'
    | '/app/calendar'
    | '/app/flash-memory'
  id:
    | '__root__'
    | '/'
    | '/app'
    | '/app/chat'
    | '/app/settings'
    | '/plugins/clipboard-history'
    | '/plugins/translate'
    | '/tools/quick-search'
    | '/tools/quick-toolbar'
    | '/app/'
    | '/search/'
    | '/app/settings/ai'
    | '/app/settings/appearance'
    | '/app/settings/base'
    | '/app/settings/default-models'
    | '/app/settings/model-providers'
    | '/app/settings/shortcuts'
    | '/app/calendar/'
    | '/app/flash-memory/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AppRoute: typeof AppRouteWithChildren
  PluginsClipboardHistoryRoute: typeof PluginsClipboardHistoryRoute
  PluginsTranslateRoute: typeof PluginsTranslateRoute
  ToolsQuickSearchRoute: typeof ToolsQuickSearchRoute
  ToolsQuickToolbarRoute: typeof ToolsQuickToolbarRoute
  SearchIndexRoute: typeof SearchIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/app': {
      id: '/app'
      path: '/app'
      fullPath: '/app'
      preLoaderRoute: typeof AppRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/search/': {
      id: '/search/'
      path: '/search'
      fullPath: '/search'
      preLoaderRoute: typeof SearchIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/app/': {
      id: '/app/'
      path: '/'
      fullPath: '/app/'
      preLoaderRoute: typeof AppIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/tools/quick-toolbar': {
      id: '/tools/quick-toolbar'
      path: '/tools/quick-toolbar'
      fullPath: '/tools/quick-toolbar'
      preLoaderRoute: typeof ToolsQuickToolbarRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/tools/quick-search': {
      id: '/tools/quick-search'
      path: '/tools/quick-search'
      fullPath: '/tools/quick-search'
      preLoaderRoute: typeof ToolsQuickSearchRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/plugins/translate': {
      id: '/plugins/translate'
      path: '/plugins/translate'
      fullPath: '/plugins/translate'
      preLoaderRoute: typeof PluginsTranslateRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/plugins/clipboard-history': {
      id: '/plugins/clipboard-history'
      path: '/plugins/clipboard-history'
      fullPath: '/plugins/clipboard-history'
      preLoaderRoute: typeof PluginsClipboardHistoryRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/app/settings': {
      id: '/app/settings'
      path: '/settings'
      fullPath: '/app/settings'
      preLoaderRoute: typeof AppSettingsRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/chat': {
      id: '/app/chat'
      path: '/chat'
      fullPath: '/app/chat'
      preLoaderRoute: typeof AppChatRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/flash-memory/': {
      id: '/app/flash-memory/'
      path: '/flash-memory'
      fullPath: '/app/flash-memory'
      preLoaderRoute: typeof AppFlashMemoryIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/calendar/': {
      id: '/app/calendar/'
      path: '/calendar'
      fullPath: '/app/calendar'
      preLoaderRoute: typeof AppCalendarIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/settings/shortcuts': {
      id: '/app/settings/shortcuts'
      path: '/shortcuts'
      fullPath: '/app/settings/shortcuts'
      preLoaderRoute: typeof AppSettingsShortcutsRouteImport
      parentRoute: typeof AppSettingsRoute
    }
    '/app/settings/model-providers': {
      id: '/app/settings/model-providers'
      path: '/model-providers'
      fullPath: '/app/settings/model-providers'
      preLoaderRoute: typeof AppSettingsModelProvidersRouteImport
      parentRoute: typeof AppSettingsRoute
    }
    '/app/settings/default-models': {
      id: '/app/settings/default-models'
      path: '/default-models'
      fullPath: '/app/settings/default-models'
      preLoaderRoute: typeof AppSettingsDefaultModelsRouteImport
      parentRoute: typeof AppSettingsRoute
    }
    '/app/settings/base': {
      id: '/app/settings/base'
      path: '/base'
      fullPath: '/app/settings/base'
      preLoaderRoute: typeof AppSettingsBaseRouteImport
      parentRoute: typeof AppSettingsRoute
    }
    '/app/settings/appearance': {
      id: '/app/settings/appearance'
      path: '/appearance'
      fullPath: '/app/settings/appearance'
      preLoaderRoute: typeof AppSettingsAppearanceRouteImport
      parentRoute: typeof AppSettingsRoute
    }
    '/app/settings/ai': {
      id: '/app/settings/ai'
      path: '/ai'
      fullPath: '/app/settings/ai'
      preLoaderRoute: typeof AppSettingsAiRouteImport
      parentRoute: typeof AppSettingsRoute
    }
  }
}

interface AppSettingsRouteChildren {
  AppSettingsAiRoute: typeof AppSettingsAiRoute
  AppSettingsAppearanceRoute: typeof AppSettingsAppearanceRoute
  AppSettingsBaseRoute: typeof AppSettingsBaseRoute
  AppSettingsDefaultModelsRoute: typeof AppSettingsDefaultModelsRoute
  AppSettingsModelProvidersRoute: typeof AppSettingsModelProvidersRoute
  AppSettingsShortcutsRoute: typeof AppSettingsShortcutsRoute
}

const AppSettingsRouteChildren: AppSettingsRouteChildren = {
  AppSettingsAiRoute: AppSettingsAiRoute,
  AppSettingsAppearanceRoute: AppSettingsAppearanceRoute,
  AppSettingsBaseRoute: AppSettingsBaseRoute,
  AppSettingsDefaultModelsRoute: AppSettingsDefaultModelsRoute,
  AppSettingsModelProvidersRoute: AppSettingsModelProvidersRoute,
  AppSettingsShortcutsRoute: AppSettingsShortcutsRoute,
}

const AppSettingsRouteWithChildren = AppSettingsRoute._addFileChildren(
  AppSettingsRouteChildren,
)

interface AppRouteChildren {
  AppChatRoute: typeof AppChatRoute
  AppSettingsRoute: typeof AppSettingsRouteWithChildren
  AppIndexRoute: typeof AppIndexRoute
  AppCalendarIndexRoute: typeof AppCalendarIndexRoute
  AppFlashMemoryIndexRoute: typeof AppFlashMemoryIndexRoute
}

const AppRouteChildren: AppRouteChildren = {
  AppChatRoute: AppChatRoute,
  AppSettingsRoute: AppSettingsRouteWithChildren,
  AppIndexRoute: AppIndexRoute,
  AppCalendarIndexRoute: AppCalendarIndexRoute,
  AppFlashMemoryIndexRoute: AppFlashMemoryIndexRoute,
}

const AppRouteWithChildren = AppRoute._addFileChildren(AppRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AppRoute: AppRouteWithChildren,
  PluginsClipboardHistoryRoute: PluginsClipboardHistoryRoute,
  PluginsTranslateRoute: PluginsTranslateRoute,
  ToolsQuickSearchRoute: ToolsQuickSearchRoute,
  ToolsQuickToolbarRoute: ToolsQuickToolbarRoute,
  SearchIndexRoute: SearchIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
