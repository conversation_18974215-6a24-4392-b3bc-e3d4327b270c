interface Chat {
  /** 对话ID */
  id: string
  /** 对话标题 */
  title: string
  /** 创建时间 */
  timestamp: number
  /** 消息列表 */
  messages: Message[]
}

// 添加 ChatHistory 类型别名
type ChatHistory = Chat

type MessageRole = 'user' | 'assistant'

interface Message {
  /** 消息ID */
  id: string
  /** 角色 */
  role: MessageRole
  /** 思考过程 */
  reasoningContent?: string
  /** 回答内容 */
  content: string
  /** 时间戳 */
  timestamp?: number
  /** 是否正在加载 */
  loading?: boolean
}
