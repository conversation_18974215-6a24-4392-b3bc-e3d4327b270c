import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

interface ModelProviderState {
  _id: string
  _rev: string
  _isInitializing: boolean
  /**
   * 服务商列表
   */
  providers: ModelProvider[]

  models: Model[]

  initialize: () => void
  /**
   * 添加服务商
   */
  addProvider: (provider: ModelProvider) => void
  /**
   * 删除服务商
   */
  removeProvider: (id: string) => void
  /**
   * 切换服务商启用状态
   */
  toggleEnableProvider: (id: string) => void
  /**
   * 更新服务商
   */
  updateProvider: (provider: ModelProvider) => void

  /**
   * 模型管理
   * @param providerId
   * @param model
   */
  addModel(providerId: string, model: Model): void
  /**
   * 删除模型
   */
  removeModel(providerId: string, modelId: string): void
  /**
   * 切换模型启用状态
   */
  toggleEnableModel(providerId: string, modelId: string): void
  /**
   * 更新模型
   */
  updateModel(providerId: string, model: Model): void
}

export const useModelProvider = create<ModelProviderState>()(
  subscribeWithSelector((set, get) => ({
    _id: 'model-providers',
    _rev: '',
    _isInitializing: false,
    providers: [],
    get models() {
      return get()
        .providers.filter(item => item.enabled)
        .reduce((acc, item) => {
          acc.push(...item.models.filter(m => m.enabled))
          return acc
        }, [] as Model[])
    },
    initialize() {
      set({ _isInitializing: true })
      smart.database.get(get()._id).then(res => {
        console.log('model-providers', res)
        set({ providers: res?.providers || [] })
        set({ _isInitializing: false })
      })
    },
    addProvider: (provider: ModelProvider) => {
      set(state => ({
        providers: [...state.providers, provider],
      }))
    },
    removeProvider: (id: string) => {
      set(state => ({
        providers: state.providers.filter(p => p.id !== id),
      }))
    },
    toggleEnableProvider: (id: string) => {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === id ? { ...p, enabled: !p.enabled } : p
        ),
      }))
    },
    updateProvider: (provider: ModelProvider) => {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === provider.id ? provider : p
        ),
      }))
    },
    addModel: (providerId: string, model: Model) => {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId ? { ...p, models: [...p.models, model] } : p
        ),
      }))
    },
    removeModel: (providerId: string, modelId: string) => {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId
            ? { ...p, models: p.models.filter(m => m.id !== modelId) }
            : p
        ),
      }))
    },
    toggleEnableModel: (providerId: string, modelId: string) => {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId
            ? {
                ...p,
                models: p.models.map(m =>
                  m.id === modelId ? { ...m, enabled: !m.enabled } : m
                ),
              }
            : p
        ),
      }))
    },
    updateModel: (providerId: string, model: Model) => {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId
            ? {
                ...p,
                models: p.models.map(m => (m.id === model.id ? model : m)),
              }
            : p
        ),
      }))
    },
  }))
)

useModelProvider.subscribe(
  state => state.providers,
  async providers => {
    const { _id, _rev, _isInitializing } = useModelProvider.getState()
    if (_isInitializing) {
      return
    }
    try {
      await smart.database.put({
        _id,
        _rev,
        providers,
      })
      console.log('模型服务商同步成功')
    } catch (error) {
      console.error('保存服务商失败:', error)
    }
  }
)
