import { createHashHistory, createRouter,RouterProvider } from "@tanstack/react-router"
import { withAppWrapper } from '@/components/provider'
import { routeTree } from './routeTree.gen'

const history = createHashHistory();
// Set up a Router instance
const router = createRouter({
  history,
  routeTree,
  defaultPreload: 'intent',
  defaultStaleTime: 5000,
  scrollRestoration: true,
})

// Register things for typesafety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

export default withAppWrapper(function App() {
  return (
    <RouterProvider router={router} />
  )
})