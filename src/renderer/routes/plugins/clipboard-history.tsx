import ClipboardHistory from '@/components/plugins/clipboard-history/clipboard-history'
import { createFileRoute } from '@tanstack/react-router'

export const Route = createFileRoute('/plugins/clipboard-history')({
  component: ClipboardHistoryPage,
})

function ClipboardHistoryPage() {
  const onItemSelect = (item: any) => {
    console.log('选中的剪切板项:', item)
  }

  return (
    <div className='p-6 max-w-4xl mx-auto'>
      <div className='mb-6'>
        <h1 className='text-2xl font-bold mb-2'>剪切板历史管理</h1>
        <p className='text-muted-foreground'>
          实时监听剪切板变化，支持文本、图片、文件、HTML等多种类型的历史记录管理。
        </p>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* 主要组件 */}
        <div className='lg:col-span-2'>
          <ClipboardHistory
            maxItems={50}
            onItemSelect={onItemSelect}
            className='h-[600px]'
          />
        </div>
      </div>
    </div>
  )
}
