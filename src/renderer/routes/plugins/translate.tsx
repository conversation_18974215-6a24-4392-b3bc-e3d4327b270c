import { TooltipWrapper } from '@/components/plugins/tooltip-wrapper'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { createFileRoute } from '@tanstack/react-router'
import {
  ArrowRight,
  ArrowUp,
  Copy,
  EqualIcon,
  Pin,
  RefreshCw,
  Volume2,
} from 'lucide-react'

export const Route = createFileRoute('/plugins/translate')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className='w-full p-3 font-sans flex flex-col h-full rounded-lg'>
      <div className='flex items-center justify-between mb-3 flex-shrink-0 relative drag'>
        <div className='flex items-center gap-2'>
          <Button variant='secondary' className='no-drag'>
            Model: DeepSeek R1
          </Button>
        </div>
        <EqualIcon className='w-4 h-4 text-muted-foreground absolute left-1/2 -translate-x-1/2 top-0' />
        <Button variant='ghost' size='icon' className='size-7 no-drag'>
          <Pin className='w-4 h-4' />
        </Button>
      </div>

      {/* Language Selector */}
      <div className='flex items-center justify-between mb-3 flex-shrink-0'>
        <div className='flex items-center gap-6'>
          <Button variant='secondary'>自动检测</Button>
          <ArrowRight className='w-4 h-4' />
          <Select>
            <SelectTrigger className='w-[140px]'>
              <SelectValue defaultValue='english' placeholder='请选择语言' />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value='english'>English</SelectItem>
                <SelectItem value='chinese'>中文</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Translation Result */}
      <div className='flex-grow bg-accent p-2 rounded-lg mb-2 text-sm overflow-y-auto'>
        <p>
          It provides first-class JavaScript packaging support and an extensible
          module ecosystem to start your application in the right way. The
          preferred installation method is via npm as a development dependency
          in your application. For more information on how to manage the
          Electron version in your application, see Electro...
        </p>
      </div>

      <div className='flex items-center space-x-2 mb-3 flex-shrink-0'>
        <TooltipWrapper content='朗读'>
          <Button variant='ghost' size='icon' className='size-7'>
            <Volume2 className='w-4 h-4' />
          </Button>
        </TooltipWrapper>
        <TooltipWrapper content='重新翻译'>
          <Button variant='ghost' size='icon' className='size-7'>
            <RefreshCw className='w-4 h-4' />
          </Button>
        </TooltipWrapper>
        <TooltipWrapper content='复制'>
          <Button variant='ghost' size='icon' className='size-7'>
            <Copy className='w-4 h-4' />
          </Button>
        </TooltipWrapper>
      </div>

      <div className='relative flex items-center flex-shrink-0 gap-2'>
        <Input
          className='focus:outline-none focus:ring-0 focus-visible:ring-0'
          placeholder='请输入要翻译的文本'
        />
        <Button variant='default'>
          <ArrowUp className='w-5 h-5' />
          <span>Enter</span>
        </Button>
      </div>
    </div>
  )
}
