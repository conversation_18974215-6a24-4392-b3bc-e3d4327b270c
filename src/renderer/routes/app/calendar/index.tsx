import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockIcon,
  PlusIcon,
  SearchIcon,
  MoreHorizontalIcon,
  SettingsIcon,
  UserIcon,
  MenuIcon,
  XIcon,
  MapPinIcon,
  UsersIcon,
  VideoIcon,
  AlignLeftIcon,
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Checkbox } from '@/components/ui/checkbox'
import { Calendar } from '@/components/ui/calendar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'

export const Route = createFileRoute('/app/calendar/')({  
  component: RouteComponent,
})

function RouteComponent() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [viewMode, setViewMode] = useState('week')
  const [isCreateEventOpen, setIsCreateEventOpen] = useState(false)
  const [eventTitle, setEventTitle] = useState('')
  const [eventDescription, setEventDescription] = useState('')
  const [eventLocation, setEventLocation] = useState('')
  const [eventStartTime, setEventStartTime] = useState('09:30')
  const [eventEndTime, setEventEndTime] = useState('10:30')
  const [selectedCalendar, setSelectedCalendar] = useState('李明')

  const formatDate = (date: Date) => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月`
  }

  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const timeSlots = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, '0')
    return `${hour}:00`
  })

  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  
  // 获取当前周的日期
  const getWeekDates = (date: Date) => {
    const week = []
    const startDate = new Date(date)
    const day = startDate.getDay()
    startDate.setDate(startDate.getDate() - day)
    
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(startDate)
      currentDate.setDate(startDate.getDate() + i)
      week.push(currentDate)
    }
    return week
  }

  const weekDates = getWeekDates(currentDate)

  const handleCreateEvent = () => {
    setIsCreateEventOpen(true)
  }

  const handleSaveEvent = () => {
    console.log('保存事件:', {
      title: eventTitle,
      description: eventDescription,
      location: eventLocation,
      startTime: eventStartTime,
      endTime: eventEndTime,
      calendar: selectedCalendar
    })
    setIsCreateEventOpen(false)
    setEventTitle('')
    setEventDescription('')
    setEventLocation('')
    setEventStartTime('09:30')
    setEventEndTime('10:30')
  }

  return (
    <div className="h-full flex flex-col bg-background">
      {/* 顶部工具栏 - 保持Google日历风格 */}
      <div className="border-b px-6 py-4 flex items-center justify-between bg-background">
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="icon" className="h-10 w-10">
              <MenuIcon className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-normal text-foreground">日历</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              size="icon"
              className="h-10 w-10"
              onClick={() => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1))}
            >
              <ChevronLeftIcon className="h-5 w-5" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon"
              className="h-10 w-10"
              onClick={() => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1))}
            >
              <ChevronRightIcon className="h-5 w-5" />
            </Button>
            <span className="text-xl font-normal text-foreground min-w-[140px]">
              {formatDate(currentDate)}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input 
              placeholder="搜索" 
              className="pl-10 w-80 h-10 rounded-full border-border"
            />
          </div>
          <Button variant="ghost" size="icon" className="h-10 w-10">
            <SettingsIcon className="h-5 w-5" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <MoreHorizontalIcon className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>设置</DropdownMenuItem>
              <DropdownMenuItem>帮助</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button className="gap-2 h-10 px-6 rounded-full" onClick={handleCreateEvent}>
            <PlusIcon className="h-4 w-4" />
            创建
          </Button>
          <Button variant="ghost" size="icon" className="h-10 w-10 rounded-full">
            <UserIcon className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* 左侧边栏 - 保持Google日历布局 */}
        <div className="w-80 border-r bg-background flex flex-col">
          {/* 小日历 */}
          <div className="p-6">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              month={currentDate}
              onMonthChange={setCurrentDate}
              className="rounded-lg border-0 shadow-none w-full"
              classNames={{
                months: "flex flex-col space-y-4 w-full",
                month: "space-y-4 w-full",
                caption: "flex justify-center pt-1 relative items-center mb-4",
                caption_label: "text-base font-medium",
                nav: "space-x-1 flex items-center",
                nav_button: "h-8 w-8 bg-transparent p-0 opacity-70 hover:opacity-100 hover:bg-accent rounded-full",
                nav_button_previous: "absolute left-1",
                nav_button_next: "absolute right-1",
                table: "w-full border-collapse space-y-1",
                head_row: "flex w-full",
                head_cell: "text-muted-foreground w-10 font-medium text-sm flex-1 text-center py-2",
                row: "flex w-full mt-1",
                cell: "text-center text-sm p-0 relative flex-1 focus-within:relative focus-within:z-20",
                day: "h-10 w-10 p-0 font-normal hover:bg-accent hover:text-accent-foreground rounded-full transition-colors flex items-center justify-center",
                day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                day_today: "bg-accent text-accent-foreground font-semibold",
                day_outside: "text-muted-foreground opacity-50",
                day_disabled: "text-muted-foreground opacity-50",
                day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                day_hidden: "invisible",
              }}
            />
          </div>

          {/* 我的日历 */}
          <div className="flex-1 px-6 pb-6">
            <h3 className="text-base font-medium text-foreground mb-4">我的日历</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Checkbox id="personal" defaultChecked className="h-4 w-4" />
                <label 
                  htmlFor="personal" 
                  className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  李明
                </label>
              </div>
              <div className="flex items-center space-x-3">
                <Checkbox id="tasks" defaultChecked className="h-4 w-4" />
                <label 
                  htmlFor="tasks" 
                  className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  任务
                </label>
              </div>
            </div>
            
            <h3 className="text-base font-medium text-foreground mb-4 mt-8">其他日历</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Checkbox id="holidays" className="h-4 w-4" />
                <label 
                  htmlFor="holidays" 
                  className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  中国的节假日
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 flex flex-col">
          {/* 周视图头部 */}
          <div className="border-b bg-background">
            <div className="grid grid-cols-8">
              <div className="p-4 border-r">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="w-full justify-start h-8">
                      周视图
                      <ChevronRightIcon className="ml-auto h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setViewMode('day')}>日视图</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setViewMode('week')}>周视图</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setViewMode('month')}>月视图</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              {weekDates.map((date, i) => (
                <div key={i} className="p-4 text-center border-r last:border-r-0">
                  <div className="text-xs text-muted-foreground mb-2">
                    {weekDays[date.getDay()]}
                  </div>
                  <div className={cn(
                    "text-lg font-normal",
                    isToday(date) && "bg-primary text-primary-foreground w-8 h-8 rounded-full flex items-center justify-center mx-auto font-medium"
                  )}>
                    {date.getDate()}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 时间网格 */}
          <ScrollArea className="flex-1">
            <div className="grid grid-cols-8">
              {/* 时间列 */}
              <div className="border-r bg-muted/20">
                {timeSlots.map((time, i) => (
                  <div key={time} className="h-16 border-b flex items-start justify-end pr-3 pt-2">
                    {i > 0 && (
                      <span className="text-xs text-muted-foreground">{time}</span>
                    )}
                  </div>
                ))}
              </div>
              
              {/* 日期列 */}
              {weekDates.map((date, dayIndex) => (
                <div key={dayIndex} className="border-r last:border-r-0 relative">
                  {timeSlots.map((time, timeIndex) => (
                    <div 
                      key={time} 
                      className="h-16 border-b hover:bg-accent/30 cursor-pointer relative transition-colors"
                    >
                      {/* 示例事件 - 使用Google日历风格 */}
                      {dayIndex === 1 && timeIndex === 9 && (
                        <Card className="absolute top-1 left-1 right-1 bg-blue-500 border-0 shadow-sm rounded-md">
                          <CardContent className="p-2">
                            <div className="text-xs font-medium text-white">上午工作</div>
                            <div className="text-xs text-blue-100">9:00 - 11:00</div>
                          </CardContent>
                        </Card>
                      )}
                      {dayIndex === 2 && timeIndex === 14 && (
                        <Card className="absolute top-1 left-1 right-1 bg-green-500 border-0 shadow-sm rounded-md">
                          <CardContent className="p-2">
                            <div className="text-xs font-medium text-white">团队会议</div>
                            <div className="text-xs text-green-100">14:00 - 15:00</div>
                          </CardContent>
                        </Card>
                      )}
                      {dayIndex === 4 && timeIndex === 16 && (
                        <Card className="absolute top-1 left-1 right-1 bg-red-500 border-0 shadow-sm rounded-md">
                          <CardContent className="p-2">
                            <div className="text-xs font-medium text-white">重要会议</div>
                            <div className="text-xs text-red-100">16:00 - 17:30</div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* 创建事件弹窗 - 完全按照Google日历风格 */}
      <Dialog open={isCreateEventOpen} onOpenChange={setIsCreateEventOpen}>
        <DialogContent className="max-w-lg p-0 gap-0 rounded-lg">
          <DialogHeader className="p-6 pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MenuIcon className="h-4 w-4" />
                </Button>
                <DialogTitle className="text-lg font-normal">添加标题</DialogTitle>
              </div>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-8 w-8"
                onClick={() => setIsCreateEventOpen(false)}
              >
                <XIcon className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>
          
          <div className="px-6">
            {/* 事件类型标签 */}
            <div className="flex gap-2 mb-6">
              <Button variant="outline" size="sm" className="text-blue-600 border-blue-200 bg-blue-50 rounded-full">
                活动
              </Button>
              <Button variant="ghost" size="sm" className="text-muted-foreground rounded-full">
                任务
              </Button>
              <Button variant="ghost" size="sm" className="text-muted-foreground rounded-full">
                预约安排
              </Button>
              <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
                新
              </Badge>
            </div>

            {/* 事件标题 */}
            <Input 
              placeholder="添加标题"
              value={eventTitle}
              onChange={(e) => setEventTitle(e.target.value)}
              className="border-0 border-b-2 border-blue-500 rounded-none px-0 text-lg font-normal focus-visible:ring-0 focus-visible:border-blue-600 mb-6"
            />

            {/* 时间设置 */}
            <div className="flex items-center gap-4 py-4 border-b">
              <ClockIcon className="h-5 w-5 text-muted-foreground" />
              <div className="flex-1">
                <div className="text-sm text-foreground mb-1">
                  7月 21日 (星期一) 上午9:30 – 上午10:30
                </div>
                <div className="text-xs text-muted-foreground">
                  时区：不重复
                </div>
              </div>
            </div>

            {/* 添加邀请对象 */}
            <div className="flex items-center gap-4 py-4 border-b">
              <UsersIcon className="h-5 w-5 text-muted-foreground" />
              <Input 
                placeholder="添加邀请对象"
                className="border-0 focus-visible:ring-0 text-sm"
              />
            </div>

            {/* 添加Google Meet */}
            <div className="flex items-center gap-4 py-4 border-b">
              <VideoIcon className="h-5 w-5 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">添加 Google Meet 视频会议</span>
            </div>

            {/* 添加地点 */}
            <div className="flex items-center gap-4 py-4 border-b">
              <MapPinIcon className="h-5 w-5 text-muted-foreground" />
              <Input 
                placeholder="添加地点"
                value={eventLocation}
                onChange={(e) => setEventLocation(e.target.value)}
                className="border-0 focus-visible:ring-0 text-sm"
              />
            </div>

            {/* 添加说明 */}
            <div className="flex items-start gap-4 py-4 border-b">
              <AlignLeftIcon className="h-5 w-5 text-muted-foreground mt-1" />
              <Textarea 
                placeholder="添加说明或Google云端硬盘附件"
                value={eventDescription}
                onChange={(e) => setEventDescription(e.target.value)}
                className="border-0 focus-visible:ring-0 resize-none min-h-[80px] text-sm"
              />
            </div>

            {/* 日历选择 */}
            <div className="flex items-center gap-4 py-4">
              <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium">{selectedCalendar}</div>
                <div className="text-xs text-muted-foreground">
                  忙碌 · 默认的公开范围 · 通知 30 分钟前
                </div>
              </div>
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="flex items-center justify-between p-6 border-t">
            <Button variant="ghost" className="text-blue-600 hover:bg-blue-50">
              更多选项
            </Button>
            <Button onClick={handleSaveEvent} className="bg-blue-600 hover:bg-blue-700 rounded-full px-6">
              保存
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
