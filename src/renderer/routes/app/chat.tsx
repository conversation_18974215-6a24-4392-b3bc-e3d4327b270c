import { createFileRoute } from '@tanstack/react-router'
import { EditIcon, PlusIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import ChatInterface from '@/components/chat/chat-interface'
import { useChat } from '@/hooks/use-chat'
import { useChatHistory } from '@/hooks/use-chat-history'
import { TooltipSimple } from '@/components/common/tooltip-simple'
import { ChatHistorySidebar } from '@/components/chat/chat-history-sidebar'
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar'

export const Route = createFileRoute('/app/chat')({
  component: RouteComponent,
})

function RouteComponent() {
  const {
    messages,
    inputValue,
    setInputValue,
    isLoading,
    handleSendMessage,
    clearMessages,
  } = useChat()

  const { chatHistory, createNewChat, loadChat, deleteChat, currentChatId } = useChatHistory()

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex h-full w-full relative">
        <ChatHistorySidebar
          chatHistory={chatHistory}
          onCreateNew={createNewChat}
          onLoadChat={loadChat}
          onDeleteChat={deleteChat}
          currentChatId={currentChatId}
        />
        
        <SidebarInset>
          {/* 顶部栏 */}
          <div className="w-full flex flex-row justify-between items-center px-6 py-2 border-b">
            <div className="flex items-center gap-3">
              {/* Sidebar 切换按钮 */}
              <SidebarTrigger />
              
              <div className="flex flex-col gap-1">
                {/* 对话标题 */}
                <h2 className="flex flex-row items-center gap-2 cursor-pointer rounded-lg px-1 py-0.5 hover:bg-muted">
                  <span className="text-sm font-semibold">友好问候</span>
                  <EditIcon className="size-3 text-primary" />
                </h2>
              </div>
            </div>

            <div className="flex flex-row gap-2">
              <TooltipSimple title="新建聊天" side="bottom">
                <Button
                  size="icon"
                  variant="ghost"
                  className="size-8"
                  onClick={createNewChat}
                >
                  <PlusIcon className="size-4" />
                </Button>
              </TooltipSimple>
              <TooltipSimple title="分享" side="bottom">
                <Button size="icon" variant="ghost" className="size-8 text-xl">
                  <span className="icon-[solar--share-outline]" />
                </Button>
              </TooltipSimple>
              <TooltipSimple title="收藏" side="bottom">
                <Button size="icon" variant="ghost" className="size-8 text-xl">
                  <span className="icon-[hugeicons--location-star-01]" />
                </Button>
              </TooltipSimple>
              <TooltipSimple title="删除" side="bottom">
                <Button size="icon" variant="ghost" className="size-8 text-xl">
                  <span className="icon-[hugeicons--delete-01]"></span>
                </Button>
              </TooltipSimple>
            </div>
          </div>

          {/* 消息区 */}
          <div className="flex-1 px-0 md:px-24 py-8">
            <div className="max-w-3xl mx-auto h-full">
              <ChatInterface
                messages={messages}
                inputValue={inputValue}
                onInputChange={setInputValue}
                isLoading={isLoading}
                onSend={handleSendMessage}
                onClearMessages={clearMessages}
              />
            </div>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}
