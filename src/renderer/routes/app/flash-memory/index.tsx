import { createFileRoute } from '@tanstack/react-router'
import React, { useState, useMemo } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  PenLineIcon,
  SearchIcon,
  TrashIcon,
  ClockIcon,
  PlusIcon,
  FilterIcon,
  MoreHorizontalIcon,
  BookmarkIcon,
  HashIcon,
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Textarea } from '@/components/ui/textarea'

interface Note {
  id: string
  content: string
  tags: string[]
  createdAt: number
  screenshot?: string
  url?: string
  isPinned?: boolean
}

export const Route = createFileRoute('/app/flash-memory/')({
  component: RouteComponent,
})

function NoteCard({
  note,
  onEdit,
  onDelete,
  onPin,
}: {
  note: Note
  onEdit: (note: Note) => void
  onDelete: (id: string) => void
  onPin: (id: string) => void
}) {
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`
    return date.toLocaleDateString('zh-CN')
  }

  return (
    <Card
      className={`group hover:shadow-md transition-all duration-200 ${note.isPinned ? 'ring-2 ring-blue-200 bg-blue-50/50' : ''}`}
    >
      <CardHeader className='p-4'>
        <div className='flex items-start justify-between gap-3'>
          <div className='flex-1 min-w-0'>
            <div className='text-sm leading-relaxed whitespace-pre-line break-words'>
              {note.content}
            </div>
            {note.tags.length > 0 && (
              <div className='flex flex-wrap gap-1 mt-3'>
                {note.tags.map(tag => (
                  <Badge
                    key={tag}
                    variant='secondary'
                    className='text-xs px-2 py-1 gap-1'
                  >
                    <HashIcon className='h-3 w-3' />
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
            <div className='flex items-center gap-2 mt-3 text-xs text-muted-foreground'>
              <ClockIcon className='h-3 w-3' />
              {formatTime(note.createdAt)}
              {note.isPinned && (
                <>
                  <span>•</span>
                  <BookmarkIcon className='h-3 w-3 text-blue-500' />
                  <span className='text-blue-500'>已置顶</span>
                </>
              )}
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant='ghost'
                size='icon'
                className='h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity'
              >
                <MoreHorizontalIcon className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onClick={() => onPin(note.id)}>
                <BookmarkIcon className='h-4 w-4 mr-2' />
                {note.isPinned ? '取消置顶' : '置顶'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit(note)}>
                <PenLineIcon className='h-4 w-4 mr-2' />
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(note.id)}
                className='text-destructive focus:text-destructive'
              >
                <TrashIcon className='h-4 w-4 mr-2' />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
    </Card>
  )
}

function AddNoteDialog({
  open,
  onOpenChange,
  editingNote,
  onSave,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingNote?: Note | null
  onSave: (content: string, tags: string[]) => void
}) {
  const [input, setInput] = useState(editingNote?.content || '')
  const [tagInput, setTagInput] = useState(editingNote?.tags.join(', ') || '')

  React.useEffect(() => {
    if (editingNote) {
      setInput(editingNote.content)
      setTagInput(editingNote.tags.join(', '))
    } else {
      setInput('')
      setTagInput('')
    }
  }, [editingNote, open])

  const handleSave = () => {
    if (!input.trim()) return

    const tags = tagInput.split(/[,，\s]/).filter(Boolean)
    onSave(input, tags)

    setInput('')
    setTagInput('')
    onOpenChange(false)
  }

  const handleCancel = () => {
    setInput('')
    setTagInput('')
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle>{editingNote ? '编辑闪记' : '新建闪记'}</DialogTitle>
        </DialogHeader>
        <div className='space-y-4 py-4'>
          <div className='space-y-2'>
            <label className='text-sm font-medium'>内容</label>
            <Textarea
              placeholder='记录你的想法...'
              value={input}
              onChange={e => setInput(e.target.value)}
              className='min-h-[120px] resize-none'
              autoFocus
            />
          </div>
          <div className='space-y-2'>
            <label className='text-sm font-medium'>标签</label>
            <Input
              placeholder='添加标签，用逗号分隔...'
              value={tagInput}
              onChange={e => setTagInput(e.target.value)}
            />
            <p className='text-xs text-muted-foreground'>
              使用逗号、中文逗号或空格分隔多个标签
            </p>
          </div>
        </div>
        <div className='flex justify-end gap-2'>
          <Button variant='outline' onClick={handleCancel}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={!input.trim()}>
            {editingNote ? '保存' : '添加'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

function QuickAddCard({ onAdd }: { onAdd: () => void }) {
  return (
    <Card
      className='border-dashed border-2 hover:border-primary/50 transition-colors cursor-pointer'
      onClick={onAdd}
    >
      <CardContent className='p-6 flex flex-col items-center justify-center text-center'>
        <PlusIcon className='h-8 w-8 text-muted-foreground mb-2' />
        <p className='text-sm text-muted-foreground'>点击添加新的闪记</p>
      </CardContent>
    </Card>
  )
}

function RouteComponent() {
  const [notes, setNotes] = useState<Note[]>([])
  const [search, setSearch] = useState('')
  const [editingNote, setEditingNote] = useState<Note | null>(null)
  const [activeTab, setActiveTab] = useState('all')
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  const allTags = useMemo(() => {
    const tagSet = new Set<string>()
    notes.forEach(note => note.tags.forEach(tag => tagSet.add(tag)))
    return Array.from(tagSet)
  }, [notes])

  const filteredNotes = useMemo(() => {
    let filtered = notes.filter(
      n =>
        n.content.toLowerCase().includes(search.toLowerCase()) ||
        n.tags.some(t => t.toLowerCase().includes(search.toLowerCase()))
    )

    if (selectedTags.length > 0) {
      filtered = filtered.filter(n =>
        selectedTags.some(tag => n.tags.includes(tag))
      )
    }

    switch (activeTab) {
      case 'today':
        return filtered.filter(n => {
          const today = new Date()
          const noteDate = new Date(n.createdAt)
          return today.toDateString() === noteDate.toDateString()
        })
      case 'week':
        return filtered.filter(n => {
          const now = new Date()
          const noteDate = new Date(n.createdAt)
          const diffTime = Math.abs(now.getTime() - noteDate.getTime())
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
          return diffDays <= 7
        })
      case 'pinned':
        return filtered.filter(n => n.isPinned)
      default:
        return filtered
    }
  }, [notes, search, selectedTags, activeTab])

  const sortedNotes = useMemo(() => {
    return [...filteredNotes].sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1
      if (!a.isPinned && b.isPinned) return 1
      return b.createdAt - a.createdAt
    })
  }, [filteredNotes])

  function handleSaveNote(content: string, tags: string[]) {
    if (editingNote) {
      setNotes(notes =>
        notes.map(n =>
          n.id === editingNote.id
            ? {
                ...n,
                content,
                tags,
              }
            : n
        )
      )
      setEditingNote(null)
    } else {
      setNotes(notes => [
        {
          id: Date.now().toString(),
          content,
          tags,
          createdAt: Date.now(),
          isPinned: false,
        },
        ...notes,
      ])
    }
  }

  function handleEdit(note: Note) {
    setEditingNote(note)
    setDialogOpen(true)
  }

  function handleDelete(id: string) {
    setNotes(notes => notes.filter(n => n.id !== id))
    if (editingNote?.id === id) {
      setEditingNote(null)
      setDialogOpen(false)
    }
  }

  function handlePin(id: string) {
    setNotes(notes =>
      notes.map(n => (n.id === id ? { ...n, isPinned: !n.isPinned } : n))
    )
  }

  function toggleTag(tag: string) {
    setSelectedTags(prev =>
      prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]
    )
  }

  function handleAddNew() {
    setEditingNote(null)
    setDialogOpen(true)
  }

  return (
    <div className='h-full flex flex-col bg-background'>
      {/* 顶部工具栏 */}
      <div className='border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
        <div className='flex items-center gap-4 px-6 py-4'>
          <div className='flex-1 max-w-md'>
            <div className='relative'>
              <SearchIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder='搜索闪记内容或标签...'
                value={search}
                onChange={e => setSearch(e.target.value)}
                className='pl-10 bg-muted/50'
              />
            </div>
          </div>

          <div className='flex items-center gap-2'>
            <Button onClick={handleAddNew} className='gap-2'>
              <PlusIcon className='h-4 w-4' />
              新建闪记
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='outline' size='sm' className='gap-2'>
                  <FilterIcon className='h-4 w-4' />
                  筛选
                  {selectedTags.length > 0 && (
                    <Badge variant='secondary' className='ml-1'>
                      {selectedTags.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end' className='w-48'>
                <div className='p-2'>
                  <p className='text-sm font-medium mb-2'>按标签筛选</p>
                  {allTags.length === 0 ? (
                    <p className='text-xs text-muted-foreground'>暂无标签</p>
                  ) : (
                    <div className='space-y-1'>
                      {allTags.map(tag => (
                        <div key={tag} className='flex items-center space-x-2'>
                          <input
                            type='checkbox'
                            id={tag}
                            checked={selectedTags.includes(tag)}
                            onChange={() => toggleTag(tag)}
                            className='rounded'
                          />
                          <label
                            htmlFor={tag}
                            className='text-sm cursor-pointer'
                          >
                            {tag}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                  {selectedTags.length > 0 && (
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => setSelectedTags([])}
                      className='w-full mt-2'
                    >
                      清除筛选
                    </Button>
                  )}
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* 标签栏 */}
        <div className='px-6 pb-4'>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-4 max-w-md'>
              <TabsTrigger value='all' className='text-xs'>
                全部
              </TabsTrigger>
              <TabsTrigger value='today' className='text-xs'>
                今天
              </TabsTrigger>
              <TabsTrigger value='week' className='text-xs'>
                本周
              </TabsTrigger>
              <TabsTrigger value='pinned' className='text-xs'>
                置顶
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className='flex-1 overflow-hidden'>
        <ScrollArea className='h-full'>
          <div className='p-6'>
            {/* 闪记列表 */}
            <div className='space-y-4'>
              {sortedNotes.length === 0 ? (
                <div className='text-center py-12'>
                  {search || selectedTags.length > 0 ? (
                    <div>
                      <SearchIcon className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
                      <p className='text-muted-foreground'>
                        没有找到匹配的闪记
                      </p>
                      <Button
                        variant='link'
                        onClick={() => {
                          setSearch('')
                          setSelectedTags([])
                        }}
                        className='mt-2'
                      >
                        清除搜索条件
                      </Button>
                    </div>
                  ) : (
                    <div>
                      <div className='mb-6'>
                        <QuickAddCard onAdd={handleAddNew} />
                      </div>
                      <p className='text-muted-foreground mb-2'>
                        还没有任何闪记
                      </p>
                      <p className='text-sm text-muted-foreground'>
                        开始记录你的想法和灵感吧
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  <QuickAddCard onAdd={handleAddNew} />
                  {sortedNotes.map(note => (
                    <NoteCard
                      key={note.id}
                      note={note}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                      onPin={handlePin}
                    />
                  ))}
                </>
              )}
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* 添加/编辑弹窗 */}
      <AddNoteDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        editingNote={editingNote}
        onSave={handleSaveNote}
      />
    </div>
  )
}
