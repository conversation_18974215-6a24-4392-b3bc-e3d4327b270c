import {
  createFileRoute,
  Outlet,
  useLocation,
  useNavigate,
} from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import {
  Settings2Icon,
  PaletteIcon,
  BrainIcon,
  KeyboardIcon,
  DatabaseIcon,
  BotIcon,
} from 'lucide-react'

export const Route = createFileRoute('/app/settings')({
  component: RouteComponent,
})

const settingsNavigation = [
  {
    id: 'model-service',
    title: '模型服务',
    icon: DatabaseIcon,
    path: '/app/settings/model-providers',
    description: 'AI 模型服务商和 API 配置',
  },
  {
    id: 'default-models',
    title: '默认模型',
    icon: BotIcon,
    path: '/app/settings/default-models',
    description: '设置应用的默认模型',
  },
  {
    id: 'general',
    title: '基础设置',
    icon: Settings2Icon,
    path: '/app/settings/base',
    description: '语言、启动和通知设置',
  },
  {
    id: 'appearance',
    title: '外观设置',
    icon: PaletteIcon,
    path: '/app/settings/appearance',
    description: '主题、字体和界面设置',
  },
  {
    id: 'ai',
    title: 'AI设置',
    icon: BrainIcon,
    path: '/app/settings/ai',
    description: 'API配置和模型设置',
  },
  {
    id: 'shortcuts',
    title: '快捷键设置',
    icon: KeyboardIcon,
    path: '/app/settings/shortcuts',
    description: '全局和应用内快捷键',
  },
]

function RouteComponent() {
  const location = useLocation()
  const navigate = useNavigate()

  // 如果访问 /app/settings，重定向到 /app/settings/base
  if (location.pathname === '/app/settings') {
    void navigate({ to: '/app/settings/base', replace: true })
    return null
  }

  return (
    <div className='flex h-full'>
      <div className='w-64 flex flex-col px-4'>
        <div className='py-5 flex-shrink-0'>
          <h2 className='text-xl font-semibold text-foreground'>设置</h2>
          <p className='text-xs text-muted-foreground mt-1'>
            管理您的应用偏好设置
          </p>
        </div>
        <div className='flex-1 overflow-auto'>
          <div className='space-y-2'>
            {settingsNavigation.map(item => {
              const Icon = item.icon
              const isActive = location.pathname === item.path
              return (
                <Button
                  key={item.id}
                  variant={isActive ? 'secondary' : 'ghost'}
                  className={cn(
                    'transition-all! w-full justify-start h-auto p-3 hover:bg-muted/20',
                    isActive && 'bg-secondary/50!'
                  )}
                  onClick={() => navigate({ to: item.path })}
                >
                  <div className='flex items-start space-x-3'>
                    <Icon className='size-5 mt-0.5 flex-shrink-0' />
                    <div className='text-left'>
                      <div className='font-medium'>{item.title}</div>
                      <div className='text-xs text-muted-foreground mt-0.5'>
                        {item.description}
                      </div>
                    </div>
                  </div>
                </Button>
              )
            })}
          </div>
        </div>
      </div>
      <div className='flex-1 overflow-hidden'>
        <Outlet />
      </div>
    </div>
  )
}
