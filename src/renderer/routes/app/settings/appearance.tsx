import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useState } from 'react'
import {
  PaletteIcon,
  MonitorIcon,
  SunIcon,
  MoonIcon,
  TypeIcon,
  EyeIcon,
  LayoutIcon,
} from 'lucide-react'

export const Route = createFileRoute('/app/settings/appearance')({
  component: RouteComponent,
})

function RouteComponent() {
  const [theme, setTheme] = useState<Theme>('system')
  const [selectedColor, setSelectedColor] = useState('blue')
  const [customColor, setCustomColor] = useState('#3b82f6')
  const [isCustomColorOpen, setIsCustomColorOpen] = useState(false)

  const predefinedColors = [
    { name: '蓝色', value: 'blue', color: '#3b82f6' },
    { name: '绿色', value: 'green', color: '#22c55e' },
    { name: '紫色', value: 'purple', color: '#a855f7' },
    { name: '红色', value: 'red', color: '#ef4444' },
    { name: '橙色', value: 'orange', color: '#f97316' },
    { name: '粉色', value: 'pink', color: '#ec4899' },
  ]

  const handleChangeTheme = (value: Theme) => {
    setTheme(value)
    window.smart.common.setTheme(value)
  }

  const handleColorChange = (value: string) => {
    if (value === 'custom') {
      setIsCustomColorOpen(true)
    } else {
      setSelectedColor(value)
    }
  }

  const handleCustomColorSave = () => {
    setSelectedColor('custom')
    setIsCustomColorOpen(false)
  }

  const getCurrentColorDisplay = () => {
    if (selectedColor === 'custom') {
      return { name: '自定义', color: customColor }
    }
    return (
      predefinedColors.find(c => c.value === selectedColor) ||
      predefinedColors[0]
    )
  }

  return (
    <div className='space-y-6'>
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <PaletteIcon className='h-5 w-5' />
            <span>主题设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>主题模式</label>
              <p className='text-xs text-muted-foreground'>选择应用主题模式</p>
            </div>
            <Select value={theme} onValueChange={handleChangeTheme}>
              <SelectTrigger className='w-[140px]'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='light'>
                  <div className='flex items-center space-x-2'>
                    <SunIcon className='h-4 w-4' />
                    <span>浅色模式</span>
                  </div>
                </SelectItem>
                <SelectItem value='dark'>
                  <div className='flex items-center space-x-2'>
                    <MoonIcon className='h-4 w-4' />
                    <span>深色模式</span>
                  </div>
                </SelectItem>
                <SelectItem value='system'>
                  <div className='flex items-center space-x-2'>
                    <MonitorIcon className='h-4 w-4' />
                    <span>跟随系统</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>主题色彩</label>
              <p className='text-xs text-muted-foreground'>选择应用主题色彩</p>
            </div>
            <Select value={selectedColor} onValueChange={handleColorChange}>
              <SelectTrigger className='w-[140px]'>
                <SelectValue>
                  <div className='flex items-center space-x-2'>
                    <div
                      className='w-4 h-4 rounded-full border border-border'
                      style={{
                        backgroundColor: getCurrentColorDisplay().color,
                      }}
                    />
                    <span>{getCurrentColorDisplay().name}</span>
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {predefinedColors.map(colorOption => (
                  <SelectItem key={colorOption.value} value={colorOption.value}>
                    <div className='flex items-center space-x-2'>
                      <div
                        className='w-4 h-4 rounded-full border border-border'
                        style={{ backgroundColor: colorOption.color }}
                      />
                      <span>{colorOption.name}</span>
                    </div>
                  </SelectItem>
                ))}
                <SelectItem value='custom'>
                  <div className='flex items-center space-x-2'>
                    <div className='w-4 h-4 rounded-full border border-border bg-gradient-to-r from-red-500 via-yellow-500 to-blue-500' />
                    <span>自定义颜色</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 自定义颜色弹窗 */}
          <Dialog open={isCustomColorOpen} onOpenChange={setIsCustomColorOpen}>
            <DialogContent className='sm:max-w-md'>
              <DialogHeader>
                <DialogTitle>自定义主题色彩</DialogTitle>
              </DialogHeader>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='color-input'>颜色值</Label>
                  <div className='flex space-x-2'>
                    <Input
                      id='color-input'
                      type='color'
                      value={customColor}
                      onChange={e => setCustomColor(e.target.value)}
                      className='w-16 h-10 p-1 border rounded'
                    />
                    <Input
                      type='text'
                      value={customColor}
                      onChange={e => setCustomColor(e.target.value)}
                      placeholder='#3b82f6'
                      className='flex-1'
                    />
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label>预览</Label>
                  <div className='flex items-center space-x-2 p-3 border rounded-md'>
                    <div
                      className='w-8 h-8 rounded-full border border-border'
                      style={{ backgroundColor: customColor }}
                    />
                    <span className='text-sm'>这是您选择的自定义颜色</span>
                  </div>
                </div>
                <div className='flex justify-end space-x-2'>
                  <Button
                    variant='outline'
                    onClick={() => setIsCustomColorOpen(false)}
                  >
                    取消
                  </Button>
                  <Button onClick={handleCustomColorSave}>确定</Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>

      {/* 字体设置 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <TypeIcon className='h-5 w-5' />
            <span>字体设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <label className='text-sm font-medium'>字体大小</label>
              <span className='text-xs text-muted-foreground'>中 (14px)</span>
            </div>
            <div className='space-y-3'>
              <Slider
                defaultValue={[14]}
                max={20}
                min={12}
                step={1}
                className='w-full'
              />
              <div className='flex justify-between text-xs text-muted-foreground'>
                <span>小 (12px)</span>
                <span>大 (20px)</span>
              </div>
            </div>
          </div>

          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>字体</label>
              <p className='text-xs text-muted-foreground'>选择界面字体</p>
            </div>
            <Select defaultValue='system'>
              <SelectTrigger className='w-[140px]'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='system'>系统默认</SelectItem>
                <SelectItem value='sans-serif'>无衬线字体</SelectItem>
                <SelectItem value='serif'>衬线字体</SelectItem>
                <SelectItem value='monospace'>等宽字体</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>字体平滑</label>
              <p className='text-xs text-muted-foreground'>启用字体抗锯齿</p>
            </div>
            <Switch defaultChecked />
          </div>
        </CardContent>
      </Card>

      {/* 界面设置 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <LayoutIcon className='h-5 w-5' />
            <span>界面设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>紧凑模式</label>
              <p className='text-xs text-muted-foreground'>减少界面元素间距</p>
            </div>
            <Switch />
          </div>

          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>显示侧边栏</label>
              <p className='text-xs text-muted-foreground'>
                默认显示左侧导航栏
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>动画效果</label>
              <p className='text-xs text-muted-foreground'>启用界面过渡动画</p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <label className='text-sm font-medium'>界面缩放</label>
              <span className='text-xs text-muted-foreground'>100%</span>
            </div>
            <div className='space-y-3'>
              <Slider
                defaultValue={[100]}
                max={150}
                min={75}
                step={25}
                className='w-full'
              />
              <div className='flex justify-between text-xs text-muted-foreground'>
                <span>75%</span>
                <span>150%</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 可访问性设置 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <EyeIcon className='h-5 w-5' />
            <span>可访问性</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>高对比度</label>
              <p className='text-xs text-muted-foreground'>增强界面对比度</p>
            </div>
            <Switch />
          </div>

          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>减少动画</label>
              <p className='text-xs text-muted-foreground'>
                减少或禁用动画效果
              </p>
            </div>
            <Switch />
          </div>

          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <label className='text-sm font-medium'>焦点指示器</label>
              <p className='text-xs text-muted-foreground'>显示键盘焦点边框</p>
            </div>
            <Switch defaultChecked />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
