import { createFileRoute } from '@tanstack/react-router'
import { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import {
  EyeIcon,
  EyeOffIcon,
  RefreshCwIcon,
  SearchIcon,
  PlusIcon,
  SettingsIcon,
  TrashIcon,
  BrainIcon,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useModelProvider } from '@/stores/model-provider'
import { ProviderAdd } from '@/components/settings/provider-add'

export const Route = createFileRoute('/app/settings/model-providers')({
  component: RouteComponent,
})

function RouteComponent() {
  const [search, setSearch] = useState('')
  const {
    providers,
    initialize,
    addProvider,
    updateProvider,
    toggleEnableProvider,
    toggleEnableModel,
    addModel,
  } = useModelProvider()
  const [providerId, setProviderId] = useState<string>()

  const [isAddProviderOpen, setIsAddProviderOpen] = useState(false)
  const [newProviderName, setNewProviderName] = useState('')
  const [newProviderBaseUrl, setNewProviderBaseUrl] = useState('')
  const [newProviderApiKey, setNewProviderApiKey] = useState('')

  const [showApiKey, setShowApiKey] = useState(false)
  const [isAddModelOpen, setIsAddModelOpen] = useState(false)
  const [newModelName, setNewModelName] = useState('')
  const [newModelDescription, setNewModelDescription] = useState('')

  const selectedProvider = providers.find(p => p.id === providerId)!

  const handleAddProvider = () => {
    if (newProviderName.trim() && newProviderBaseUrl.trim()) {
      const newProvider: ModelProvider = {
        id: newProviderName.toLowerCase().replace(/\s+/g, '-'),
        name: newProviderName,
        icon: '🤖',
        baseUrl: newProviderBaseUrl,
        apiKey: newProviderApiKey,
        enabled: false,
        models: [],
        webSite: '',
      }

      addProvider(newProvider)
      setNewProviderName('')
      setNewProviderBaseUrl('')
      setNewProviderApiKey('')
      setIsAddProviderOpen(false)
    }
  }

  const handleAddModel = () => {
    if (newModelName.trim()) {
      const newModel: Model = {
        id: newModelName.toLowerCase().replace(/\s+/g, '-'),
        name: newModelName,
        enabled: true,
        tools: [],
        group: '',
        description: newModelDescription || '',
      }

      addModel(selectedProvider.id, {
        ...newModel,
        enabled: true,
      })
      setNewModelName('')
      setNewModelDescription('')
      setIsAddModelOpen(false)
    }
  }
  const handleDeleteModel = (modelId: string) => {
    const index = selectedProvider.models.findIndex(m => m.id === modelId)
    if (index > -1) {
      selectedProvider.models.splice(index, 1)
    }
  }
  useEffect(() => {
    if (providers.length && !selectedProvider) {
      setProviderId(providers[0].id)
    }
  }, [providers, selectedProvider])

  useEffect(() => {
    initialize()
  }, [])

  return (
    <div className='flex h-full'>
      {/* 左侧：平台列表 */}
      <div className='w-80 flex flex-col pb-2'>
        <div className='p-4 flex-shrink-0'>
          <div className='flex space-x-2'>
            <div className='relative flex-1'>
              <SearchIcon className='absolute left-3 top-2.5 w-4 h-4 text-muted-foreground' />
              <Input
                placeholder='搜索模型平台...'
                value={search}
                onChange={e => setSearch(e.target.value)}
                className='pl-9'
              />
            </div>
            <ProviderAdd >
                <Button variant='outline' size='icon'>
                  <PlusIcon className='w-4 h-4' />
                </Button>
            </ProviderAdd>
            <Dialog
              open={isAddProviderOpen}
              onOpenChange={setIsAddProviderOpen}
            >
              <DialogTrigger asChild>
                <Button variant='outline' size='icon'>
                  <PlusIcon className='w-4 h-4' />
                </Button>
              </DialogTrigger>
              <DialogContent className='sm:max-w-md'>
                <DialogHeader>
                  <DialogTitle>添加模型服务商</DialogTitle>
                </DialogHeader>
                <div className='space-y-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='provider-name'>服务商名称</Label>
                    <Input
                      id='provider-name'
                      value={newProviderName}
                      onChange={e => setNewProviderName(e.target.value)}
                      placeholder='例如：OpenAI'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='provider-baseurl'>API 地址</Label>
                    <Input
                      id='provider-baseurl'
                      value={newProviderBaseUrl}
                      onChange={e => setNewProviderBaseUrl(e.target.value)}
                      placeholder='https://api.openai.com/v1'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='provider-apikey'>API 密钥 (可选)</Label>
                    <Input
                      id='provider-apikey'
                      type='password'
                      value={newProviderApiKey}
                      onChange={e => setNewProviderApiKey(e.target.value)}
                      placeholder='sk-...'
                    />
                  </div>
                  <div className='flex justify-end space-x-2'>
                    <Button
                      variant='outline'
                      onClick={() => setIsAddProviderOpen(false)}
                    >
                      取消
                    </Button>
                    <Button onClick={handleAddProvider}>添加</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        <div className='flex-1 min-h-0'>
          <ScrollArea className='h-full'>
            <div className='p-2 space-y-1'>
              {providers.map(provider => (
                <div
                  key={provider.id}
                  className={cn(
                    `flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors`,
                    {
                      'bg-muted': provider.id === providerId,
                      'hover:bg-muted/50': provider.id !== providerId,
                    }
                  )}
                  onClick={() => setProviderId(provider.id)}
                >
                  <div className='flex items-center space-x-3'>
                    {provider.icon && (
                      <span className='text-xl'>{provider.icon}</span>
                    )}
                    <div>
                      <div className='font-medium'>{provider.name}</div>
                    </div>
                  </div>
                  {provider.enabled && (
                    <div className='w-2 h-2 bg-green-500 rounded-full'></div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>
      {selectedProvider && (
        <div className='flex-1 bg-background'>
          <ScrollArea className='h-full'>
            <div className='p-6'>
              {/* 平台标题 */}
              <div className='flex items-center justify-between mb-6'>
                <div className='flex items-center space-x-3'>
                  {selectedProvider?.icon && (
                    <span className='text-2xl'>{selectedProvider.icon}</span>
                  )}
                  <h1 className='text-xl font-semibold'>
                    {selectedProvider?.name}
                  </h1>
                </div>
                <Button variant='outline' size='sm'>
                  <SettingsIcon className='w-4 h-4 mr-2' />
                  高级设置
                </Button>
              </div>

              <div className='space-y-6 max-w-4xl'>
                {/* API 地址设置 */}
                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <label className='text-sm font-medium'>API 地址</label>
                      <p className='text-xs text-muted-foreground mt-1'>
                        {selectedProvider.id === 'ollama'
                          ? '本地服务无需配置'
                          : '请输入您的 API 地址'}
                      </p>
                    </div>
                    <Button size='sm' variant='outline' onClick={() => {}}>
                      <RefreshCwIcon className='w-4 h-4 mr-1' />
                      重置
                    </Button>
                  </div>
                  <Input
                    value={selectedProvider.baseUrl}
                    placeholder='API 地址'
                    onChange={e => {
                      selectedProvider.baseUrl = e.target.value
                      updateProvider(selectedProvider)
                    }}
                  />
                  <p className='text-xs text-muted-foreground'>
                    /结果忽略 v1 版本，# 结果型模型使用自定义地址
                  </p>
                </div>
                {/* API 密钥设置 */}
                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <label className='text-sm font-medium'>API 密钥</label>
                      <p className='text-xs text-muted-foreground mt-1'>
                        {selectedProvider.id === 'ollama'
                          ? '本地服务无需密钥'
                          : '请输入您的 API 密钥'}
                      </p>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() => setShowApiKey(!showApiKey)}
                        disabled={selectedProvider.id === 'ollama'}
                      >
                        {showApiKey ? (
                          <EyeIcon className='w-4 h-4' />
                        ) : (
                          <EyeOffIcon className='w-4 h-4' />
                        )}
                      </Button>
                      <Button
                        size='sm'
                        variant='outline'
                        disabled={selectedProvider.id === 'ollama'}
                      >
                        检测
                      </Button>
                    </div>
                  </div>
                  <Input
                    type={showApiKey ? 'text' : 'password'}
                    value={selectedProvider.apiKey}
                    placeholder={
                      selectedProvider.id === 'ollama'
                        ? '本地服务无需配置'
                        : 'API 密钥'
                    }
                    onChange={e => {
                      selectedProvider.apiKey = e.target.value
                      updateProvider(selectedProvider)
                    }}
                    disabled={selectedProvider.id === 'ollama'}
                  />
                </div>

                {/* 启用模型服务开关 */}
                <div className='flex items-center justify-between p-3 border rounded-lg bg-muted/30'>
                  <div className='space-y-1'>
                    <div className='text-sm font-medium'>启用模型服务</div>
                    <p className='text-xs text-muted-foreground'>
                      开启后可使用该平台的模型进行对话
                    </p>
                  </div>
                  <Switch
                    checked={selectedProvider.enabled}
                    onCheckedChange={() => {
                      toggleEnableProvider(selectedProvider.id)
                    }}
                  />
                </div>

                {/* 模型管理标题和操作 */}
                <div className='flex items-center justify-between'>
                  <div>
                    <label className='text-sm font-medium'>模型管理</label>
                    <p className='text-xs text-muted-foreground mt-1'>
                      管理 {selectedProvider.name} 的可用模型
                    </p>
                  </div>
                  <div className='flex space-x-2'>
                    <Dialog
                      open={isAddModelOpen}
                      onOpenChange={setIsAddModelOpen}
                    >
                      <DialogTrigger asChild>
                        <Button variant='outline' size='sm'>
                          <PlusIcon className='w-4 h-4 mr-1' />
                          添加
                        </Button>
                      </DialogTrigger>
                      <DialogContent className='sm:max-w-md'>
                        <DialogHeader>
                          <DialogTitle>添加模型</DialogTitle>
                        </DialogHeader>
                        <div className='space-y-4'>
                          <div className='space-y-2'>
                            <Label htmlFor='model-name'>模型名称</Label>
                            <Input
                              id='model-name'
                              value={newModelName}
                              onChange={e => setNewModelName(e.target.value)}
                              placeholder='例如：gpt-4o'
                            />
                          </div>
                          <div className='space-y-2'>
                            <Label htmlFor='model-description'>
                              描述 (可选)
                            </Label>
                            <Input
                              id='model-description'
                              value={newModelDescription}
                              onChange={e =>
                                setNewModelDescription(e.target.value)
                              }
                              placeholder='模型描述'
                            />
                          </div>
                          <div className='flex justify-end space-x-2'>
                            <Button
                              variant='outline'
                              onClick={() => setIsAddModelOpen(false)}
                            >
                              取消
                            </Button>
                            <Button onClick={handleAddModel}>添加</Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>

                {/* 当前服务商的模型列表 */}
                {selectedProvider.models.length > 0 ? (
                  <div className='space-y-3'>
                    {selectedProvider.models.map(model => (
                      <div
                        key={model.id}
                        className='flex items-center justify-between p-2 border rounded-lg hover:bg-muted/50'
                      >
                        <div className='flex items-center space-x-3'>
                          <div className='flex-1'>
                            <div className='flex items-center space-x-2'>
                              <span className='text-xs font-medium'>
                                {model.name}
                              </span>
                              {model.enabled && (
                                <Badge variant='secondary' className='text-xs'>
                                  已启用
                                </Badge>
                              )}
                            </div>
                            {model.description && (
                              <p className='text-xs text-muted-foreground mt-1'>
                                {model.description}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <Switch
                            checked={model.enabled}
                            onCheckedChange={() =>
                              toggleEnableModel(selectedProvider.id, model.id)
                            }
                          />
                          <Button
                            size='sm'
                            variant='ghost'
                            className='w-8 h-8 p-0'
                          >
                            <SettingsIcon className='w-4 h-4' />
                          </Button>
                          <Button
                            size='sm'
                            variant='ghost'
                            className='w-8 h-8 p-0 text-red-500 hover:text-red-700'
                            onClick={() => handleDeleteModel(model.id)}
                          >
                            <TrashIcon className='w-4 h-4' />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className='text-center text-muted-foreground py-8 border rounded-lg'>
                    <BrainIcon className='w-12 h-12 mx-auto mb-4 opacity-50' />
                    <p className='mb-4'>该平台暂无可用模型</p>
                    <Button
                      variant='outline'
                      onClick={() => setIsAddModelOpen(true)}
                    >
                      <PlusIcon className='w-4 h-4 mr-2' />
                      添加第一个模型
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  )
}
