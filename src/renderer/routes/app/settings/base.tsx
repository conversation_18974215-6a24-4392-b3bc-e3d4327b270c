import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { MonitorIcon, BellIcon, GlobeIcon } from 'lucide-react'

export const Route = createFileRoute('/app/settings/base')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className='space-y-6'>
      {/* 语言设置 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <GlobeIcon className='h-5 w-5' />
            <span>语言设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <Label className='text-sm font-medium'>界面语言</Label>
              <p className='text-xs text-muted-foreground'>
                选择应用界面显示语言
              </p>
            </div>
            <Select defaultValue='zh-CN'>
              <SelectTrigger className='w-[180px]'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='zh-CN'>简体中文</SelectItem>
                <SelectItem value='en-US'>English</SelectItem>
                <SelectItem value='ja-JP'>日本語</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 启动设置 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <MonitorIcon className='h-5 w-5' />
            <span>启动设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <Label className='text-sm font-medium'>开机自启动</Label>
              <p className='text-xs text-muted-foreground'>
                系统启动时自动运行应用
              </p>
            </div>
            <Switch />
          </div>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <Label className='text-sm font-medium'>启动时最小化</Label>
              <p className='text-xs text-muted-foreground'>
                应用启动时最小化到系统托盘
              </p>
            </div>
            <Switch />
          </div>
        </CardContent>
      </Card>

      {/* 通知设置 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <BellIcon className='h-5 w-5' />
            <span>通知设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <Label className='text-sm font-medium'>桌面通知</Label>
              <p className='text-xs text-muted-foreground'>显示系统桌面通知</p>
            </div>
            <Switch defaultChecked />
          </div>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <Label className='text-sm font-medium'>声音提醒</Label>
              <p className='text-xs text-muted-foreground'>通知时播放提示音</p>
            </div>
            <Switch />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
