import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select'
import { ScrollArea } from '@/components/ui/scroll-area'

export const Route = createFileRoute('/app/settings/default-models')({
  component: RouteComponent,
})

interface DefaultModelConfig {
  defaultAssistant: string
  topicNaming: string
  translation: string
  quickAssistant: string
}

interface ModelInfo {
  value: string
  label: string
  platform: string
  icon: string
}

// 平台图标映射
const platformIcons: Record<string, string> = {
  DeepSeek: '🧠',
  Qwen: '☁️',
  OpenAI: '🤖',
  Anthropic: '🎭',
  Google: '🔍',
  Baidu: '🐻',
  Zhipu: '🌟',
  MiniMax: '🎯',
  Ollama: '🦙',
}

function RouteComponent() {
  // 默认模型配置
  const [defaultModelConfig, setDefaultModelConfig] =
    useState<DefaultModelConfig>({
      defaultAssistant: 'deepseek-ai/DeepSeek-V3',
      topicNaming: 'Qwen/Qwen3-8B',
      translation: 'deepseek-ai/DeepSeek-V3',
      quickAssistant: 'deepseek-ai/DeepSeek-V3',
    })

  // 模拟的可用模型数据（实际应该从全局状态获取）
  const getAllEnabledModels = (): ModelInfo[] => {
    const list: ModelInfo[] = []
    smart.database.get('model-service').then(res => {
      console.log('model-service', res)
      if (!res) return
      const platforms = (res.platforms || []).filter(p => p.active)
      platforms.forEach(platform => {
        platform.models.forEach(model => {
          list.push({
            value: `${platform.id}/${model.id}`,
            label: model.name,
            platform: platform.name,
            icon: platformIcons[platform.name],
          })
        })
      })
    })
    console.log('list', list)
    return list
    return [
      {
        value: 'deepseek-ai/DeepSeek-V3',
        label: 'DeepSeek-V3',
        platform: 'DeepSeek',
        icon: platformIcons['DeepSeek'],
      },
      {
        value: 'deepseek-ai/DeepSeek-R1',
        label: 'DeepSeek-R1',
        platform: 'DeepSeek',
        icon: platformIcons['DeepSeek'],
      },
      {
        value: 'qwen/Qwen3-8B',
        label: 'Qwen3-8B',
        platform: 'Qwen',
        icon: platformIcons['Qwen'],
      },
      {
        value: 'qwen/Qwen2.5-7B',
        label: 'Qwen2.5-7B-Instruct',
        platform: 'Qwen',
        icon: platformIcons['Qwen'],
      },
      {
        value: 'openai/gpt-4o',
        label: 'GPT-4o',
        platform: 'OpenAI',
        icon: platformIcons['OpenAI'],
      },
      {
        value: 'openai/gpt-4o-mini',
        label: 'GPT-4o mini',
        platform: 'OpenAI',
        icon: platformIcons['OpenAI'],
      },
      {
        value: 'anthropic/claude-3.5-sonnet',
        label: 'Claude 3.5 Sonnet',
        platform: 'Anthropic',
        icon: platformIcons['Anthropic'],
      },
    ]
  }

  // 按平台分组模型
  const getGroupedModels = () => {
    const models = getAllEnabledModels()
    const grouped: Record<string, ModelInfo[]> = {}

    models.forEach(model => {
      if (!grouped[model.platform]) {
        grouped[model.platform] = []
      }
      grouped[model.platform].push(model)
    })

    return grouped
  }

  return (
    <div className='h-full'>
      <ScrollArea className='h-full'>
        <div className='p-6 space-y-6'>
          <div className='flex flex-col gap-2'>
            <h3>默认助手模型</h3>
            <Select
              value={defaultModelConfig.defaultAssistant}
              onValueChange={value =>
                setDefaultModelConfig(prev => ({
                  ...prev,
                  defaultAssistant: value,
                }))
              }
            >
              <SelectTrigger className='w-full max-w-md'>
                <SelectValue placeholder='选择默认助手模型'>
                  {defaultModelConfig.defaultAssistant &&
                    (() => {
                      const model = getAllEnabledModels().find(
                        m => m.value === defaultModelConfig.defaultAssistant
                      )
                      return model ? (
                        <div className='flex items-center gap-3 w-full'>
                          <span className='text-lg flex-shrink-0'>
                            {model.icon}
                          </span>
                          <div className='flex flex-row items-start min-w-0 flex-1'>
                            <span className='font-medium text-sm truncate w-full'>
                              {model.label}
                            </span>
                            <span className='text-xs text-muted-foreground truncate w-full'>
                              {model.platform}
                            </span>
                          </div>
                        </div>
                      ) : (
                        defaultModelConfig.defaultAssistant
                      )
                    })()}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {Object.entries(getGroupedModels()).map(
                  ([platform, models]) => (
                    <SelectGroup key={platform}>
                      <SelectLabel className='flex items-center gap-2 py-2'>
                        <span className='text-lg'>
                          {platformIcons[platform]}
                        </span>
                        <span>{platform}</span>
                      </SelectLabel>
                      {models.map(model => (
                        <SelectItem key={model.value} value={model.value}>
                          <div className='flex items-center gap-3 w-full'>
                            <span className='text-base flex-shrink-0'>
                              {model.icon}
                            </span>
                            <div className='flex flex-col items-start min-w-0 flex-1'>
                              <span className='font-medium text-sm'>
                                {model.label}
                              </span>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  )
                )}
              </SelectContent>
            </Select>
            <p className='text-xs text-muted-foreground'>
              创建新助手时使用的模型，如果助手未设置模型
            </p>
          </div>
          <div className='flex flex-col gap-2'>
            <h3>话题命名模型</h3>
            <Select
              value={defaultModelConfig.topicNaming}
              onValueChange={value =>
                setDefaultModelConfig(prev => ({
                  ...prev,
                  topicNaming: value,
                }))
              }
            >
              <SelectTrigger className='w-full max-w-md'>
                <SelectValue placeholder='选择话题命名模型'>
                  {defaultModelConfig.topicNaming &&
                    (() => {
                      const model = getAllEnabledModels().find(
                        m => m.value === defaultModelConfig.topicNaming
                      )
                      return model ? (
                        <div className='flex items-center gap-3 w-full'>
                          <span className='text-lg flex-shrink-0'>
                            {model.icon}
                          </span>
                          <div className='flex flex-row items-start min-w-0 flex-1'>
                            <span className='font-medium text-sm truncate w-full'>
                              {model.label}
                            </span>
                            <span className='text-xs text-muted-foreground truncate w-full'>
                              {model.platform}
                            </span>
                          </div>
                        </div>
                      ) : (
                        defaultModelConfig.topicNaming
                      )
                    })()}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {Object.entries(getGroupedModels()).map(
                  ([platform, models]) => (
                    <SelectGroup key={platform}>
                      <SelectLabel className='flex items-center gap-2 py-2'>
                        <span className='text-lg'>
                          {platformIcons[platform]}
                        </span>
                        <span>{platform}</span>
                      </SelectLabel>
                      {models.map(model => (
                        <SelectItem key={model.value} value={model.value}>
                          <div className='flex items-center gap-3 w-full'>
                            <span className='text-base flex-shrink-0'>
                              {model.icon}
                            </span>
                            <div className='flex flex-col items-start min-w-0 flex-1'>
                              <span className='font-medium text-sm'>
                                {model.label}
                              </span>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  )
                )}
              </SelectContent>
            </Select>
            <p className='text-xs text-muted-foreground'>话题命名使用的模型</p>
          </div>
          <div className='flex flex-col gap-2'>
            <h3>翻译模型</h3>
            <Select
              value={defaultModelConfig.translation}
              onValueChange={value =>
                setDefaultModelConfig(prev => ({
                  ...prev,
                  translation: value,
                }))
              }
            >
              <SelectTrigger className='w-full max-w-md'>
                <SelectValue placeholder='选择翻译模型'>
                  {defaultModelConfig.translation &&
                    (() => {
                      const model = getAllEnabledModels().find(
                        m => m.value === defaultModelConfig.translation
                      )
                      return model ? (
                        <div className='flex items-center gap-3 w-full'>
                          <span className='text-lg flex-shrink-0'>
                            {model.icon}
                          </span>
                          <div className='flex flex-row items-start min-w-0 flex-1'>
                            <span className='font-medium text-sm truncate w-full'>
                              {model.label}
                            </span>
                            <span className='text-xs text-muted-foreground truncate w-full'>
                              {model.platform}
                            </span>
                          </div>
                        </div>
                      ) : (
                        defaultModelConfig.translation
                      )
                    })()}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {Object.entries(getGroupedModels()).map(
                  ([platform, models]) => (
                    <SelectGroup key={platform}>
                      <SelectLabel className='flex items-center gap-2 py-2'>
                        <span className='text-lg'>
                          {platformIcons[platform]}
                        </span>
                        <span>{platform}</span>
                      </SelectLabel>
                      {models.map(model => (
                        <SelectItem key={model.value} value={model.value}>
                          <div className='flex items-center gap-3 w-full'>
                            <span className='text-base flex-shrink-0'>
                              {model.icon}
                            </span>
                            <div className='flex flex-col items-start min-w-0 flex-1'>
                              <span className='font-medium text-sm'>
                                {model.label}
                              </span>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  )
                )}
              </SelectContent>
            </Select>
            <p className='text-xs text-muted-foreground'>翻译服务使用的模型</p>
          </div>
          <div className='flex flex-col gap-2'>
            <h3>快捷助手模型</h3>
            <Select
              value={defaultModelConfig.quickAssistant}
              onValueChange={value =>
                setDefaultModelConfig(prev => ({
                  ...prev,
                  quickAssistant: value,
                }))
              }
            >
              <SelectTrigger className='w-full max-w-md'>
                <SelectValue placeholder='选择快捷助手模型'>
                  {defaultModelConfig.quickAssistant &&
                    (() => {
                      const model = getAllEnabledModels().find(
                        m => m.value === defaultModelConfig.quickAssistant
                      )
                      return model ? (
                        <div className='flex items-center gap-3 w-full'>
                          <span className='text-lg flex-shrink-0'>
                            {model.icon}
                          </span>
                          <div className='flex flex-row items-start min-w-0 flex-1'>
                            <span className='font-medium text-sm truncate w-full'>
                              {model.label}
                            </span>
                            <span className='text-xs text-muted-foreground truncate w-full'>
                              {model.platform}
                            </span>
                          </div>
                        </div>
                      ) : (
                        defaultModelConfig.quickAssistant
                      )
                    })()}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {Object.entries(getGroupedModels()).map(
                  ([platform, models]) => (
                    <SelectGroup key={platform}>
                      <SelectLabel className='flex items-center gap-2 py-2'>
                        <span className='text-lg'>
                          {platformIcons[platform]}
                        </span>
                        <span>{platform}</span>
                      </SelectLabel>
                      {models.map(model => (
                        <SelectItem key={model.value} value={model.value}>
                          <div className='flex items-center gap-3 w-full'>
                            <span className='text-base flex-shrink-0'>
                              {model.icon}
                            </span>
                            <div className='flex flex-col items-start min-w-0 flex-1'>
                              <span className='font-medium text-sm'>
                                {model.label}
                              </span>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  )
                )}
              </SelectContent>
            </Select>
            <p className='text-xs text-muted-foreground'>
              快捷操作和临时对话使用的模型
            </p>
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
