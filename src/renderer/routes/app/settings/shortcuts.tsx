import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  KeyboardIcon,
  GlobeIcon,
  MonitorIcon,
} from 'lucide-react'

export const Route = createFileRoute('/app/settings/shortcuts')({
  component: RouteComponent,
})

const globalShortcuts = [
  {
    id: 'show-hide',
    name: '显示/隐藏窗口',
    shortcut: 'Ctrl+Shift+Space',
    description: '快速显示或隐藏主窗口',
  },
  {
    id: 'quick-search',
    name: '快速搜索',
    shortcut: 'Ctrl+Shift+F',
    description: '打开全局搜索功能',
  },
  {
    id: 'new-chat',
    name: '新建对话',
    shortcut: 'Ctrl+Shift+N',
    description: '快速创建新的AI对话',
  },
]

const appShortcuts = [
  {
    id: 'toggle-sidebar',
    name: '切换侧边栏',
    shortcut: 'Ctrl+B',
    description: '显示或隐藏左侧边栏',
  },
  {
    id: 'settings',
    name: '打开设置',
    shortcut: 'Ctrl+,',
    description: '打开应用设置页面',
  },
  {
    id: 'search',
    name: '页面搜索',
    shortcut: 'Ctrl+F',
    description: '在当前页面搜索内容',
  },
]

function ShortcutItem({
  shortcut,
  isGlobal = false,
}: {
  shortcut: any
  isGlobal?: boolean
}) {
  return (
    <div className='flex items-center justify-between py-3'>
      <div className='flex-1'>
        <div className='flex items-center space-x-2'>
          <span className='font-medium'>{shortcut.name}</span>
          {isGlobal && (
            <Badge variant='secondary' className='text-xs'>
              全局
            </Badge>
          )}
        </div>
        <p className='text-sm text-muted-foreground mt-1'>
          {shortcut.description}
        </p>
      </div>
      <Badge variant='outline' className='font-mono'>
        {shortcut.shortcut}
      </Badge>
    </div>
  )
}

function RouteComponent() {

  return (
    <div className='space-y-6'>
      {/* 全局设置 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <KeyboardIcon className='h-5 w-5' />
            <span>快捷键设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div>
              <Label className='text-sm font-medium'>启用全局快捷键</Label>
              <p className='text-xs text-muted-foreground'>
                允许在其他应用中使用快捷键
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className='flex items-center justify-between'>
            <div>
              <Label className='text-sm font-medium'>快捷键提示</Label>
              <p className='text-xs text-muted-foreground'>
                在界面上显示快捷键提示
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className='flex items-center justify-between'>
            <div>
              <Label className='text-sm font-medium'>冲突检测</Label>
              <p className='text-xs text-muted-foreground'>
                检测并警告快捷键冲突
              </p>
            </div>
            <Switch defaultChecked />
          </div>
        </CardContent>
      </Card>

      {/* 全局快捷键 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <GlobeIcon className='h-5 w-5' />
            <span>全局快捷键</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-3'>
          {globalShortcuts.map(shortcut => (
            <ShortcutItem
              key={shortcut.id}
              shortcut={shortcut}
              isGlobal={true}
            />
          ))}
        </CardContent>
      </Card>

      {/* 应用内快捷键 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <MonitorIcon className='h-5 w-5' />
            <span>应用内快捷键</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-3'>
          {appShortcuts.map(shortcut => (
            <ShortcutItem
              key={shortcut.id}
              shortcut={shortcut}
            />
          ))}
        </CardContent>
      </Card>

    </div>
  )
}
