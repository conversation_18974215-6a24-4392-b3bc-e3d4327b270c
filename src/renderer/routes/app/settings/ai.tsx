import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { <PERSON>lider } from '@/components/ui/slider'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { useEffect, useState } from 'react'
import {
  BrainIcon,
  KeyIcon,
  MessageSquareIcon,
  DatabaseIcon,
  PlusIcon,
  TrashIcon,
} from 'lucide-react'

export const Route = createFileRoute('/app/settings/ai')({
  component: RouteComponent,
})

interface ModelProvider {
  id: string
  name: string
  baseUrl: string
  apiKey: string
  models: Model[]
}

interface Model {
  id: string
  name: string
  description?: string
}

function RouteComponent() {
  const [providers, setProviders] = useState<ModelProvider[]>([
    {
      id: 'openai',
      name: 'OpenAI',
      baseUrl: 'https://api.openai.com/v1',
      apiKey: '',
      models: [
        { id: 'gpt-4', name: 'GPT-4', description: '最新的GPT-4模型' },
        {
          id: 'gpt-4-turbo',
          name: 'GPT-4 Turbo',
          description: '更快的GPT-4版本',
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          description: '经济实惠的选择',
        },
      ],
    },
    {
      id: 'anthropic',
      name: 'Anthropic',
      baseUrl: 'https://api.anthropic.com/v1',
      apiKey: '',
      models: [
        {
          id: 'claude-3-opus',
          name: 'Claude 3 Opus',
          description: '最强大的Claude模型',
        },
        {
          id: 'claude-3-sonnet',
          name: 'Claude 3 Sonnet',
          description: '平衡性能和成本',
        },
      ],
    },
  ])

  const [selectedProvider, setSelectedProvider] = useState('openai')
  const [selectedModel, setSelectedModel] = useState('gpt-4')
  const [isAddProviderOpen, setIsAddProviderOpen] = useState(false)
  const [isAddModelOpen, setIsAddModelOpen] = useState(false)
  const [isManageModelsOpen, setIsManageModelsOpen] = useState(false)
  const [newProvider, setNewProvider] = useState({
    name: '',
    baseUrl: '',
    apiKey: '',
  })
  const [newModel, setNewModel] = useState({ name: '', description: '' })

  const currentProvider = providers.find(p => p.id === selectedProvider)

  const handleAddProvider = () => {
    if (newProvider.name && newProvider.baseUrl) {
      const provider: ModelProvider = {
        id: newProvider.name.toLowerCase().replace(/\s+/g, '-'),
        name: newProvider.name,
        baseUrl: newProvider.baseUrl,
        apiKey: newProvider.apiKey,
        models: [],
      }
      setProviders([...providers, provider])
      setNewProvider({ name: '', baseUrl: '', apiKey: '' })
      setIsAddProviderOpen(false)
    }
  }

  const handleAddModel = () => {
    if (newModel.name && currentProvider) {
      const model: Model = {
        id: newModel.name.toLowerCase().replace(/\s+/g, '-'),
        name: newModel.name,
        description: newModel.description,
      }
      const updatedProviders = providers.map(p =>
        p.id === selectedProvider ? { ...p, models: [...p.models, model] } : p
      )
      setProviders(updatedProviders)
      setNewModel({ name: '', description: '' })
      setIsAddModelOpen(false)
    }
  }

  const handleDeleteModel = (modelId: string) => {
    const updatedProviders = providers.map(p =>
      p.id === selectedProvider
        ? { ...p, models: p.models.filter(m => m.id !== modelId) }
        : p
    )
    setProviders(updatedProviders)
  }

  useEffect(() => {
    // alert('ok')
    // window.smart.database.set('modals', [
    //   {
    //     id: 'gpt-4',
    //     name: 'GPT-4',
    //     description: '最新的GPT-4模型',
    //   },
    //   {
    //     id: 'gpt-4-turbo',
    //     name: 'GPT-4 Turbo',
    //     description: '更快的GPT-4版本',
    //   },
    //   {
    //     id: 'gpt-3.5-turbo',
    //     name: 'GPT-3.5 Turbo',
    //     description: '经济实惠的选择',
    //   },
    // ],{
    //   namespace: 'ai'
    // }).then(res => {
    //   console.log('set modals', res)
    // })

      window.smart.database.get('modals', {
        namespace: 'ai'
      }).then(res => {
        console.log('modals', res)
        console.log(JSON.parse(res.value))
      })
  }, [])

  return (
    <div className='space-y-6'>
      {/* 服务提供商管理 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center justify-between'>
            <div className='flex items-center space-x-2'>
              <KeyIcon className='h-5 w-5' />
              <span>服务提供商</span>
            </div>
            <Dialog
              open={isAddProviderOpen}
              onOpenChange={setIsAddProviderOpen}
            >
              <DialogTrigger asChild>
                <Button variant='outline' size='sm'>
                  <PlusIcon className='h-4 w-4 mr-2' />
                  添加服务商
                </Button>
              </DialogTrigger>
              <DialogContent className='sm:max-w-md'>
                <DialogHeader>
                  <DialogTitle>添加服务提供商</DialogTitle>
                </DialogHeader>
                <div className='space-y-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='provider-name'>服务商名称</Label>
                    <Input
                      id='provider-name'
                      value={newProvider.name}
                      onChange={e =>
                        setNewProvider({ ...newProvider, name: e.target.value })
                      }
                      placeholder='例如：OpenAI'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='provider-url'>API端点</Label>
                    <Input
                      id='provider-url'
                      value={newProvider.baseUrl}
                      onChange={e =>
                        setNewProvider({
                          ...newProvider,
                          baseUrl: e.target.value,
                        })
                      }
                      placeholder='https://api.openai.com/v1'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='provider-key'>API密钥</Label>
                    <Input
                      id='provider-key'
                      type='password'
                      value={newProvider.apiKey}
                      onChange={e =>
                        setNewProvider({
                          ...newProvider,
                          apiKey: e.target.value,
                        })
                      }
                      placeholder='请输入API密钥'
                    />
                  </div>
                  <div className='flex justify-end space-x-2'>
                    <Button
                      variant='outline'
                      onClick={() => setIsAddProviderOpen(false)}
                    >
                      取消
                    </Button>
                    <Button onClick={handleAddProvider}>添加</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <Label>当前服务商</Label>
              <p className='text-xs text-muted-foreground'>
                选择要使用的AI服务提供商
              </p>
            </div>
            <Select
              value={selectedProvider}
              onValueChange={setSelectedProvider}
            >
              <SelectTrigger className='w-[180px]'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {providers.map(provider => (
                  <SelectItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {currentProvider && (
            <>
              <div className='space-y-2'>
                <Label htmlFor='current-api-key'>API密钥</Label>
                <Input
                  id='current-api-key'
                  type='password'
                  value={currentProvider.apiKey}
                  onChange={e => {
                    const updatedProviders = providers.map(p =>
                      p.id === selectedProvider
                        ? { ...p, apiKey: e.target.value }
                        : p
                    )
                    setProviders(updatedProviders)
                  }}
                  placeholder='请输入您的API密钥'
                  className='font-mono'
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='current-api-endpoint'>API端点</Label>
                <Input
                  id='current-api-endpoint'
                  value={currentProvider.baseUrl}
                  onChange={e => {
                    const updatedProviders = providers.map(p =>
                      p.id === selectedProvider
                        ? { ...p, baseUrl: e.target.value }
                        : p
                    )
                    setProviders(updatedProviders)
                  }}
                  className='font-mono'
                />
              </div>

              <div className='flex items-center space-x-2'>
                <Button variant='outline' size='sm'>
                  测试连接
                </Button>
                <Badge variant='secondary' className='text-xs'>
                  未连接
                </Badge>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* 模型管理 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <BrainIcon className='h-5 w-5' />
            <span>模型设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <Label>默认模型</Label>
              <p className='text-xs text-muted-foreground'>
                选择默认使用的AI模型
              </p>
            </div>
            <div className='flex items-center space-x-2'>
              <Select
                value={selectedModel}
                onValueChange={setSelectedModel}
                disabled={
                  !currentProvider || currentProvider.models.length === 0
                }
              >
                <SelectTrigger className='w-[140px]'>
                  <SelectValue placeholder='选择模型' />
                </SelectTrigger>
                <SelectContent>
                  {currentProvider?.models.map(model => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant='outline'
                size='sm'
                disabled={!currentProvider}
                onClick={() => setIsManageModelsOpen(true)}
              >
                管理模型
              </Button>
            </div>
          </div>

          {/* 模型管理弹窗 */}
          <Dialog
            open={isManageModelsOpen}
            onOpenChange={setIsManageModelsOpen}
          >
            <DialogContent className='sm:max-w-lg'>
              <DialogHeader>
                <DialogTitle className='flex items-center justify-between'>
                  <span>管理模型 - {currentProvider?.name}</span>
                  <Dialog
                    open={isAddModelOpen}
                    onOpenChange={setIsAddModelOpen}
                  >
                    <DialogTrigger asChild>
                      <Button variant='outline' size='sm'>
                        <PlusIcon className='h-4 w-4 mr-2' />
                        添加模型
                      </Button>
                    </DialogTrigger>
                    <DialogContent className='sm:max-w-md'>
                      <DialogHeader>
                        <DialogTitle>
                          添加模型到 {currentProvider?.name}
                        </DialogTitle>
                      </DialogHeader>
                      <div className='space-y-4'>
                        <div className='space-y-2'>
                          <Label htmlFor='model-name'>模型名称</Label>
                          <Input
                            id='model-name'
                            value={newModel.name}
                            onChange={e =>
                              setNewModel({ ...newModel, name: e.target.value })
                            }
                            placeholder='例如：gpt-4'
                          />
                        </div>
                        <div className='space-y-2'>
                          <Label htmlFor='model-description'>模型描述</Label>
                          <Input
                            id='model-description'
                            value={newModel.description}
                            onChange={e =>
                              setNewModel({
                                ...newModel,
                                description: e.target.value,
                              })
                            }
                            placeholder='例如：最新的GPT-4模型'
                          />
                        </div>
                        <div className='flex justify-end space-x-2'>
                          <Button
                            variant='outline'
                            onClick={() => setIsAddModelOpen(false)}
                          >
                            取消
                          </Button>
                          <Button onClick={handleAddModel}>添加</Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </DialogTitle>
              </DialogHeader>
              <div className='space-y-4'>
                {currentProvider && currentProvider.models.length === 0 ? (
                  <div className='text-center py-8 text-muted-foreground'>
                    <BrainIcon className='h-12 w-12 mx-auto mb-4 opacity-50' />
                    <p className='text-sm'>暂无模型</p>
                    <p className='text-xs'>点击上方按钮添加模型</p>
                  </div>
                ) : (
                  <div className='space-y-2 max-h-80 overflow-y-auto'>
                    {currentProvider?.models.map(model => (
                      <div
                        key={model.id}
                        className='flex items-center justify-between p-3 border border-border rounded-lg'
                      >
                        <div className='flex-1'>
                          <div className='font-medium'>{model.name}</div>
                          {model.description && (
                            <p className='text-sm text-muted-foreground mt-1'>
                              {model.description}
                            </p>
                          )}
                        </div>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => handleDeleteModel(model.id)}
                          className='text-destructive hover:text-destructive'
                        >
                          <TrashIcon className='h-4 w-4' />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
                <div className='flex justify-end'>
                  <Button
                    variant='outline'
                    onClick={() => setIsManageModelsOpen(false)}
                  >
                    关闭
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label>温度 (创造性)</Label>
                <p className='text-xs text-muted-foreground'>
                  控制回复的创造性
                </p>
              </div>
              <div className='w-32'>
                <Slider
                  defaultValue={[0.7]}
                  max={2}
                  min={0}
                  step={0.1}
                  className='w-full'
                />
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label>最大令牌数</Label>
                <p className='text-xs text-muted-foreground'>限制回复长度</p>
              </div>
              <div className='w-32'>
                <Slider
                  defaultValue={[2048]}
                  max={4096}
                  min={256}
                  step={256}
                  className='w-full'
                />
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label className='text-sm font-medium'>流式响应</Label>
                <p className='text-xs text-muted-foreground'>
                  实时显示AI回复内容
                </p>
              </div>
              <Switch defaultChecked />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 对话设置 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <MessageSquareIcon className='h-5 w-5' />
            <span>对话设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-2'>
            <Label htmlFor='system-prompt'>系统提示词</Label>
            <Textarea
              id='system-prompt'
              placeholder='您是一个有用的AI助手...'
              className='min-h-[80px]'
              defaultValue='您是一个有用、无害且诚实的AI助手。请用中文回答用户的问题，提供准确和有用的信息。'
            />
          </div>

          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <Label className='text-sm font-medium'>上下文长度</Label>
              <p className='text-xs text-muted-foreground'>
                保留的对话历史轮数
              </p>
            </div>
            <div className='w-32'>
              <Slider
                defaultValue={[10]}
                max={50}
                min={1}
                step={1}
                className='w-full'
              />
            </div>
          </div>

          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <Label className='text-sm font-medium'>自动保存对话</Label>
              <p className='text-xs text-muted-foreground'>自动保存对话历史</p>
            </div>
            <Switch defaultChecked />
          </div>
        </CardContent>
      </Card>

      {/* 使用统计 */}
      <Card className='filter-card'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <DatabaseIcon className='h-5 w-5' />
            <span>使用统计</span>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-2 gap-4'>
            <div className='text-center p-4 border border-border rounded-lg'>
              <div className='text-2xl font-bold text-foreground'>1,234</div>
              <div className='text-sm text-muted-foreground'>总对话数</div>
            </div>
            <div className='text-center p-4 border border-border rounded-lg'>
              <div className='text-2xl font-bold text-foreground'>56.7K</div>
              <div className='text-sm text-muted-foreground'>总令牌数</div>
            </div>
          </div>

          <Button variant='outline' className='w-full'>
            查看详细统计
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
