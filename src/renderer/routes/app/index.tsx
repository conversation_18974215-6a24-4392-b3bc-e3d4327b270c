import { createFileRoute, Outlet, <PERSON> } from '@tanstack/react-router'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Settings2Icon } from 'lucide-react'

export const Route = createFileRoute('/app/')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>欢迎使用 Smart Toolbox</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            这是一个智能工具箱应用，提供各种实用功能。
          </p>

          <div className="flex space-x-4">
            <Link to="/app/settings/base">
              <Button className="flex items-center space-x-2">
                <Settings2Icon className="h-4 w-4" />
                <span>打开设置</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      <Outlet />
    </div>
  )
}
