import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  ChevronDownIcon,
  CopyIcon,
  LanguagesIcon,
  Volume2Icon,
} from 'lucide-react'
import { createFileRoute } from '@tanstack/react-router'

import SearchIcon from '@/assets/icons/search.svg?react'
import DragIcon from '@/assets/icons/drag.svg?react'

import { useEffect, useRef } from 'react'

export const Route = createFileRoute('/tools/quick-toolbar')({
  component: RouteComponent,
})

function RouteComponent() {
  const isMove = useRef(false)
  const startPosition = useRef({ x: 0, y: 0 })
  const enterRef = useRef(false)

  const handleStartMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const { x, y } = e.nativeEvent
    startPosition.current = { x, y }
    isMove.current = true
  }

  const handleMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isMove.current) return
    const { x, y } = e.nativeEvent
    window.smart.toolbar.operate('move', {
      x: x - startPosition.current.x,
      y: y - startPosition.current.y,
    })
  }

  const handleEndMove = () => {
    isMove.current = false
  }

  const handleOpenOption = () => {}

  const handleOut = e => {
    if (e.relatedTarget === null) {
      window.smart.toolbar.operate('leave')
    }
  }
  const handleEnter = e => {
    window.smart.toolbar.operate('enter')
  }

  useEffect(() => {
    document.addEventListener('mouseenter', handleEnter)
    document.addEventListener('mouseout', handleOut)
    return () => {
      document.removeEventListener('mouseenter', handleEnter)
      document.removeEventListener('mouseout', handleOut)
    }
  }, [])

  return (
    <div
      className='w-screen h-screen rounded-lg bg-background flex flex-row justify-between items-center px-2 select-none'
      role='toolbar'
      tabIndex={0}
      id='quick-toolbar'
      aria-label='Quick actions toolbar'
      onMouseEnter={() => {
        window.smart.toolbar.operate('enter')
        enterRef.current = true
      }}
      onMouseLeave={() => {
        window.smart.toolbar.operate('leave')
      }}
    >
      <div className='flex items-center justify-center cursor-move pl-2 drag'>
        <DragIcon className='size-5' />
      </div>
      <Button variant='ghost' className='text-xs h-8 cursor-pointer'>
        <SearchIcon />
        搜索
      </Button>
      <Button variant='ghost' className='text-xs h-8 cursor-pointer'>
        <LanguagesIcon />
        翻译
      </Button>
      <Button variant='ghost' className='text-xs h-8 cursor-pointer'>
        <CopyIcon className='size-3.5' />
        复制
      </Button>
      <Button variant='ghost' className='text-xs h-8 cursor-pointer'>
        <CopyIcon className='size-3.5' />
        随记
      </Button>
      <Button variant='ghost' className='text-xs h-8 cursor-pointer'>
        <Volume2Icon className='size-4.5' />
        朗读
      </Button>
      <Separator orientation='vertical' className='h-4! mx-1 bg-accent/70' />
      <Button
        variant='ghost'
        size='icon'
        className='size-8 cursor-pointer'
        onClick={handleOpenOption}
      >
        <ChevronDownIcon className='size-4' />
      </Button>
    </div>
  )
}
