import { createFileRoute, redirect } from '@tanstack/react-router'

export const Route = createFileRoute('/')({
  component: RouteComponent,
  beforeLoad: async () => {
    // You can perform any pre-load actions here, such as fetching data or initializing state.
    console.log('Loading the root route...')
    throw redirect({ to: '/app/chat' })
  }
})

function RouteComponent() {
  return <div>Hello "/"!</div>
}
