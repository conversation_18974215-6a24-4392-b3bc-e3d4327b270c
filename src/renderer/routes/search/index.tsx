import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { createFileRoute } from '@tanstack/react-router'
import { LucideOctagonPause } from 'lucide-react'
import { useState } from 'react'

export const Route = createFileRoute('/search/')({
  component: RouteComponent,
})

function RouteComponent() {
  const [value, setValue] = useState('')
  return (
    <div className='w-full bg-background/50'>
      <div className='w-full h-14 pl-4 box-border flex flex-row items-center drag'>
        <input
          className={cn('text-[16px] outline-none w-42 no-drag', {
            'w-full!': value.length > 0,
          })}
          value={value}
          onChange={e => {
            setValue(e.target.value)
          }}
          placeholder='搜索应用程序或命令...'
        />
      </div>
      <div className='bg-gray-200 w-full flex flex-row justify-between items-center h-8 border-t border-border/20 px-2 py-1.5'>
        <div className='flex flex-row items-center gap-1'>
          <LucideOctagonPause className='size-4' />
          <span className='text-xs font-semibold text-accent-foreground'>Smart Toolbox</span>
        </div>
        <div>
          <Button variant='ghost' size='sm'>
            操作 <kbd>Esc</kbd>
          </Button>
        </div>
      </div>
    </div>
  )
}
