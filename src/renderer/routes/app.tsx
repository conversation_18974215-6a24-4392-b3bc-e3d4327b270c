import { AppSidebar } from '@/components/app/app-sidebar'
import { WindowHeaderBar } from '@/components/app/window-header-bar'
import { createFileRoute, Outlet } from '@tanstack/react-router'

export const Route = createFileRoute('/app')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className='w-screen h-screen flex flex-row bg-background/70'>
      <AppSidebar />
      <div className='flex-1 bg-background flex flex-col rounded-bl-lg rounded-tl-lg'>
        {smart.common.isWindows() && <WindowHeaderBar />}
        <div className='flex-1 overflow-hidden'>
          <Outlet />
        </div>
      </div>
    </div>
  )
}
