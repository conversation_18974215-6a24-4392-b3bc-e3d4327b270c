import { useState, useEffect, useRef } from 'react'
import type { ChatCompletionChunk } from 'openai/resources'
import type { Stream } from 'openai/streaming'
import { useOpenAI } from './use-openai'

export function useChat() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { client } = useOpenAI()
  const [stream, setStream] = useState<Stream<ChatCompletionChunk> | null>(null)
  const streamIdRef = useRef<string | null>(null)

  const sendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return
    const userMessage: Message = {
      id: Date.now().toString(),
      content: content.trim(),
      role: 'user',
      timestamp: Date.now(),
    }
    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)
    const streamId = Math.random().toString(36).substring(2, 9)
    try {
      setMessages(prev => [
        ...prev,
        {
          id: streamId,
          reasoningContent: '',
          content: '',
          loading: true,
          role: 'assistant',
          timestamp: Date.now(),
        },
      ])
      const stream = await client.chat.completions.create({
        messages: [...messages, userMessage],
        temperature: 0.7,
        model: 'moonshotai/kimi-k2:free',
        stream: true,
      })
      setStream(stream)
      streamIdRef.current = streamId
    } catch (_error) {
      // 处理错误
      const errorMessage: Message = {
        id: streamId,
        content: '抱歉，我遇到了一些问题，请稍后再试。',
        role: 'assistant',
        timestamp: Date.now(),
      }
      setMessages(prev => {
        const index = prev.findIndex(message => message.id === streamId)
        if (index !== -1) {
          prev[index] = errorMessage
        }
        return [...prev]
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendMessage = () => {
    sendMessage(inputValue)
  }

  const clearMessages = () => {
    setMessages([
      {
        id: Date.now().toString(),
        content: '你好！我是AI助手，有什么可以帮助你的吗？',
        role: 'assistant',
        timestamp: Date.now(),
      },
    ])
  }

  useEffect(() => {
    const processStream = async () => {
      if (!stream) return
      for await (const chunk of stream) {
        const delta = chunk.choices[0].delta || {}
        setMessages(prev => {
          const index = prev.findIndex(
            message => message.id === streamIdRef.current
          )
          if (index !== -1) {
            const item = prev[index]
            const reasoningContent =
              item.reasoningContent + (delta.reasoning_content || '')
            const content = item.content + (delta.content || '')
            prev[index] = {
              ...prev[index],
              reasoningContent,
              content,
              loading: false,
            }
          }
          return [...prev]
        })
      }
      setStream(null)
      streamIdRef.current = null
    }
    processStream()
  }, [stream])

  return {
    messages,
    inputValue,
    setInputValue,
    isLoading,
    sendMessage,
    handleSendMessage,
    clearMessages,
  }
}
