import { useRef, useEffect, type RefObject } from 'react'

type UseDragMoveProps = {
  triggerRef: RefObject<HTMLElement | null>
  targetRef: RefObject<HTMLElement | null>
  onChange?: (position: { x: number; y: number }) => void
}

const useDragMove = ({ triggerRef, targetRef, onChange }: UseDragMoveProps) => {
  const isDragging = useRef(false)
  const offset = useRef({ x: 0, y: 0 })

  useEffect(() => {
    const triggerEl = triggerRef.current
    const targetEl = targetRef.current

    if (!triggerEl || !targetEl) return

    // 设置可拖拽样式
    triggerEl.style.cursor = 'grab'

    const handleMouseDown = (e: MouseEvent) => {
      e.preventDefault()
      isDragging.current = true

      const rect = targetEl.getBoundingClientRect()
      offset.current = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      }
    }

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging.current) return

      const x = e.clientX - offset.current.x
      const y = e.clientY - offset.current.y

      targetEl.style.position = 'absolute'
      targetEl.style.left = `${x}px`
      targetEl.style.top = `${y}px`
      onChange?.({ x, y })
    }

    const handleMouseUp = () => {
      isDragging.current = false
    }

    triggerEl.addEventListener('mousedown', handleMouseDown)
    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('mouseup', handleMouseUp)

    return () => {
      triggerEl.removeEventListener('mousedown', handleMouseDown)
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('mouseup', handleMouseUp)
    }
  }, [triggerRef, targetRef])

  return {}
}

export default useDragMove
