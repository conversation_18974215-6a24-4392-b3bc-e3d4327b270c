import { useState, useEffect, useCallback } from 'react'

const CHAT_HISTORY_KEY = 'chat_history'

export const useChatHistory = () => {
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([])
  const [currentChatId, setCurrentChatId] = useState<string | undefined>()
  const [isLoading, setIsLoading] = useState(false)

  // 保存聊天历史索引
  const saveHistoryIndex = useCallback(async (history: ChatHistory[]) => {
    try {
      await smart.database.put(CHAT_HISTORY_KEY, {
        list: history,
        timestamp: Date.now(),
      })
    } catch (error) {
      console.error('保存聊天历史索引失败:', error)
    }
  }, [])

  // 加载聊天历史
  const loadChatHistory = useCallback(async () => {
    try {
      const result = await smart.database.get(CHAT_HISTORY_KEY)
      if (result && result.list && result.list.length) {
        setChatHistory(
          result.list.sort(
            (a: ChatHistory, b: ChatHistory) => b.timestamp - a.timestamp
          )
        )
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error)
    }
  }, [])

  // 保存聊天历史
  const saveChatHistory = useCallback(
    async (history: ChatHistory[]) => {
      try {
        await saveHistoryIndex(history)
        setChatHistory(history.sort((a, b) => b.timestamp - a.timestamp))
      } catch (error) {
        console.error('保存聊天历史失败:', error)
      }
    },
    [saveHistoryIndex]
  )

  // 创建新聊天
  const createNewChat = useCallback(async () => {
    const newChat: ChatHistory = {
      id: Date.now().toString(),
      title: '新对话',
      timestamp: Date.now(),
      messages: [],
    }

    try {
      await smart.database.put(`chat_${newChat.id}`, newChat)
      const updatedHistory = [newChat, ...chatHistory]
      await saveChatHistory(updatedHistory)
      setCurrentChatId(newChat.id)
      return newChat
    } catch (error) {
      console.error('创建新聊天失败:', error)
      return null
    }
  }, [chatHistory, saveChatHistory])

  // 保存当前聊天
  const saveCurrentChat = useCallback(
    async (messages: Message[], title?: string) => {
      if (!currentChatId) return

      const updatedHistory = chatHistory.map(chat => {
        if (chat.id === currentChatId) {
          return {
            ...chat,
            title: title || chat.title,
            messages,
            timestamp: Date.now(),
          }
        }
        return chat
      })

      await saveChatHistory(updatedHistory)
    },
    [currentChatId, chatHistory, saveChatHistory]
  )

  // 加载指定聊天
  const loadChat = useCallback(async (chatId: string) => {
    try {
      setIsLoading(true)
      const chat = await smart.database.get(`chat_${chatId}`)
      if (chat) {
        setCurrentChatId(chatId)
        return chat
      }
    } catch (error) {
      console.error('加载聊天失败:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 删除聊天
  const deleteChat = useCallback(
    async (chatId: string) => {
      try {
        // 从数据库中删除聊天记录
        await smart.database.remove(`chat_${chatId}`)

        // 更新聊天历史列表
        const updatedHistory = chatHistory.filter(chat => chat.id !== chatId)
        await saveChatHistory(updatedHistory)

        // 如果删除的是当前聊天，清除当前聊天ID
        if (currentChatId === chatId) {
          setCurrentChatId(undefined)
        }
      } catch (error) {
        console.error('删除聊天失败:', error)
      }
    },
    [chatHistory, currentChatId, saveChatHistory]
  )

  // 更新聊天标题
  const updateChatTitle = useCallback(
    async (chatId: string, title: string) => {
      try {
        const updatedHistory = chatHistory.map(chat => {
          if (chat.id === chatId) {
            return { ...chat, title }
          }
          return chat
        })
        await saveChatHistory(updatedHistory)

        // 同时更新数据库中的聊天记录
        const chat = await smart.database.get(`chat_${chatId}`)
        if (chat) {
          await smart.database.put(`chat_${chatId}`, { ...chat, title })
        }
      } catch (error) {
        console.error('更新聊天标题失败:', error)
      }
    },
    [chatHistory, saveChatHistory]
  )

  useEffect(() => {
    loadChatHistory()
  }, [])

  return {
    chatHistory,
    currentChatId,
    isLoading,
    loadChat,
    createNewChat,
    saveCurrentChat,
    deleteChat,
    updateChatTitle,
    setCurrentChatId,
  }
}
