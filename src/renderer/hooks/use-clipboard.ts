import { useState, useEffect } from 'react'

interface ClipboardData {
  text?: string
  image?: Buffer
  files?: string[]
  contentType: 'text' | 'image' | 'files' | 'unknown'
}

export function useClipboard() {
  const [clipboardHistory, setClipboardHistory] = useState<ClipboardData[]>([])

  useEffect(() => {
    // 检查剪贴板API是否可用
    if (!smart?.clipboard) {
      console.warn('剪贴板API不可用')
      return
    }

    // 使用剪贴板API监听变化
    const unsubscribe = smart.clipboard.watchHandler((data) => {
      console.log('剪贴板变化:', data)
      
      // setClipboardHistory(prevHistory => [data, ...prevHistory.slice(0, 9)]) // 保持最近10条记录
    })

    // 清理函数，在组件卸载时取消监听
    return unsubscribe
  }, [])

  const clearHistory = () => {
    setClipboardHistory([])
  }

  return {
    clipboardHistory,
    clearHistory,
  }
} 