import type { Message } from '@/components/chat/types'
import { Button } from '@/components/ui/button'
import { CopyIcon } from 'lucide-react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import MarkdownRenderer from '@/components/ui/markdown-renderer'

export const MessageItemAssistant = ({ message }: { message: Message }) => {
  return (
    <div className='w-full flex flex-row justify-start items-center py-5'>
      <div className='w-full flex flex-col gap-2 items-start'>
        <div className='p-2 w-full flex flex-col gap-2'>
          {message.reasoning_content && (
            <div className='text-xs bg-muted/50 text-muted-foreground rounded-lg'>
              <Accordion
                type='single'
                collapsible
                className='w-full'
                defaultValue='reasoning'
              >
                <AccordionItem value='reasoning'>
                  <AccordionTrigger className='flex-none cursor-pointer px-5'>
                    {message.content ? '已完成思考' : '思考中...'}
                  </AccordionTrigger>
                  <AccordionContent className='text-sm border-l pl-5 mx-5 mb-5 pt-4'>
                    <MarkdownRenderer className='text-muted-foreground'>{message.reasoning_content}</MarkdownRenderer>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          )}
          <MarkdownRenderer>{message.content}</MarkdownRenderer>
        </div>
        <div className='flex flex-row gap-2'>
          <Button variant='ghost' size='icon'>
            <CopyIcon className='w-4 h-4' />
          </Button>
        </div>
      </div>
    </div>
  )
}
