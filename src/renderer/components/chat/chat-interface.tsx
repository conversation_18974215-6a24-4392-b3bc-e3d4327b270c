import React from 'react'
import type { Message } from '@/components/chat/types'
import MessageList from '@/components/chat/message-list'
import MessageInput from '@/components/chat/message-input'
import EmptyState from '@/components/chat/empty-state'

interface ChatInterfaceProps {
  messages: Message[]
  inputValue: string
  isLoading?: boolean
  onInputChange: (value: string) => void
  onSend: () => void
  onClearMessages: () => void
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  inputValue,
  isLoading = false,
  onInputChange,
  onSend,
}) => {
  return (
    <div className='flex flex-col h-full justify-center px-4'>
      {/* 消息区域 */}

      <div className='flex-1 overflow-hidden'>
        {messages.length === 0 ? (
          <EmptyState />
        ) : (
          <MessageList messages={messages} />
        )}
      </div>

      {/* 输入区域 */}
      <MessageInput
        value={inputValue}
        onChange={onInputChange}
        onSend={onSend}
        isLoading={isLoading}
      />
    </div>
  )
}

export default ChatInterface
