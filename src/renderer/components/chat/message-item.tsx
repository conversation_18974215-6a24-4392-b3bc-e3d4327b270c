import React from 'react'
import type { Message } from '@/components/chat/types'
import { cn } from '@/lib/utils'
import Markdown from 'react-markdown'
import { MessageItemUser } from './message-item-user'
import { MessageItemAssistant } from './message-item-assistant'

interface MessageItemProps {
  message: Message
}

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const isUser = message.role === 'user'

  if (isUser) {
    return <MessageItemUser message={message} />
  }

  return <MessageItemAssistant message={message} />
}

export default MessageItem
