export interface Message {
  role: 'user' | 'assistant'
  reasoning_content?: string
  content: string
  id?: string
  timestamp?: number
  loading?: boolean
}

export interface ChatState {
  messages: Message[]
  isLoading: boolean
  error?: string
}

export interface ChatProps {
  messages: Message[]
  onSendMessage: (content: string) => void
  isLoading?: boolean
}

export interface MessageItemProps {
  message: Message
}

export interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSend: () => void
  disabled?: boolean
  placeholder?: string
} 