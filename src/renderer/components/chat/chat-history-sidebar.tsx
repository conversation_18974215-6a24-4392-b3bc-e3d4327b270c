import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  PlusIcon,
  SearchIcon,
  MessageSquareIcon,
  MoreHorizontalIcon,
  TrashIcon,
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import { cn } from '@/lib/utils'

interface ChatHistorySidebarProps {
  chatHistory: ChatHistory[]
  onCreateNew: () => void
  onLoadChat: (chatId: string) => void
  onDeleteChat: (chatId: string) => void
  currentChatId?: string
}

export const ChatHistorySidebar: React.FC<ChatHistorySidebarProps> = ({
  chatHistory,
  onCreateNew,
  onLoadChat,
  onDeleteChat,
  currentChatId,
}) => {
  const [searchQuery, setSearchQuery] = React.useState('')

  const filteredHistory = chatHistory.filter(chat =>
    chat.title.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffDays = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24)
    )

    if (diffDays === 0) return '今天'
    if (diffDays === 1) return '昨天'
    if (diffDays < 7) return `${diffDays}天前`
    return date.toLocaleDateString('zh-CN')
  }

  // 按日期分组聊天记录
  const groupedHistory = React.useMemo(() => {
    const groups: { [key: string]: ChatHistory[] } = {}
    
    filteredHistory.forEach(chat => {
      const dateKey = formatDate(chat.timestamp)
      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(chat)
    })
    
    return groups
  }, [filteredHistory])

  return (
    <Sidebar collapsible="icon" className="w-80 absolute">
      <SidebarHeader>
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold px-2">聊天历史</h2>
          <Button
            size="icon"
            variant="ghost"
            className="size-8"
            onClick={onCreateNew}
          >
            <PlusIcon className="size-4" />
          </Button>
        </div>
        
        {/* 搜索框 */}
        <div className="relative px-2">
          <SearchIcon className="absolute left-5 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
          <SidebarInput
            placeholder="搜索聊天记录..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </SidebarHeader>

      <SidebarContent>
        {Object.keys(groupedHistory).length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <MessageSquareIcon className="size-12 mb-2 opacity-50 text-muted-foreground" />
            <p className="text-muted-foreground">暂无聊天记录</p>
          </div>
        ) : (
          Object.entries(groupedHistory).map(([dateGroup, chats]) => (
            <SidebarGroup key={dateGroup}>
              <SidebarGroupLabel>{dateGroup}</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {chats.map(chat => (
                    <SidebarMenuItem key={chat.id}>
                      <SidebarMenuButton
                        onClick={() => onLoadChat(chat.id)}
                        isActive={currentChatId === chat.id}
                        className="w-full justify-start"
                      >
                        <MessageSquareIcon className="size-4" />
                        <div className="flex flex-col items-start min-w-0 flex-1">
                          <span className="font-medium truncate w-full text-left">
                            {chat.title}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {chat.messages.length} 条消息
                          </span>
                        </div>
                      </SidebarMenuButton>
                      <SidebarMenuAction
                        onClick={e => {
                          e.stopPropagation()
                          onDeleteChat(chat.id)
                        }}
                        showOnHover
                      >
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="size-6"
                            >
                              <MoreHorizontalIcon className="size-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => onDeleteChat(chat.id)}
                              className="text-destructive"
                            >
                              <TrashIcon className="size-4 mr-2" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </SidebarMenuAction>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          ))
        )}
      </SidebarContent>

      <SidebarFooter>
        <div className="text-xs text-muted-foreground text-center p-2">
          共 {chatHistory.length} 个对话
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
