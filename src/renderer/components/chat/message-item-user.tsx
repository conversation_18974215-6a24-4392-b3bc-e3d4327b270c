import type { Message } from '@/components/chat/types'
import { Button } from '@/components/ui/button'
import { CopyIcon, MoreHorizontalIcon, ShareIcon } from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '../ui/tooltip'

const TooltipWrapper = ({ children, tooltip }: { children: React.ReactNode, tooltip: string }) => {
  return (
    <Tooltip delayDuration={0}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent side='top' align='center'>
        {tooltip}
      </TooltipContent>
    </Tooltip>
  )
}

export const MessageItemUser = ({ message }: { message: Message }) => {
  
  return (
    <div className='w-full flex flex-row justify-end items-center py-5'>
      <div className='w-max-2/3 flex flex-col gap-2 items-end'>
        <div className='bg-background text-foreground rounded-lg p-2 text-sm'>
          {message.content}
        </div>
        <div className='flex flex-row gap-0.5'>
          <TooltipWrapper tooltip='复制'>
            <Button variant='ghost' size='icon' className='h-8 px-2 py-0.5'>
              <CopyIcon className='size-3.5' />
            </Button>
          </TooltipWrapper>
          <TooltipWrapper tooltip='分享'>
            <Button variant='ghost' className='h-8 px-2 py-0.5 text-xs'>
              <ShareIcon className='size-3.5' />
              分享
            </Button>
          </TooltipWrapper>
          <TooltipWrapper tooltip='更多'>
            <Button variant='ghost' className='h-8 px-2 py-0.5'>
              <MoreHorizontalIcon className='size-3.5' />
            </Button>
          </TooltipWrapper>
        </div>
      </div>
    </div>
  )
}
