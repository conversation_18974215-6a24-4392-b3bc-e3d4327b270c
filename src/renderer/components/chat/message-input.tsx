import React, { type ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { ArrowUpIcon } from 'lucide-react'
import { TooltipSimple } from '../common/tooltip-simple'

interface MessageInputProps {
  value: string
  onChange: (value: string) => void
  onSend: () => void
  isLoading?: boolean
}

const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChange,
  onSend,
  isLoading = false,
}) => {
  const textareaRef = React.useRef<HTMLTextAreaElement>(null)

  const handleChange = (e: React.FormEvent<HTMLTextAreaElement>) => {
    onChange((e.target as HTMLTextAreaElement).value)
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      onSend()
    }
  }

  return (
    <div className='w-full gap-2 flex flex-col pb-5'>
      <div className='flex flex-col items-end gap-2 p-4 pb-1 rounded-xl shadow border border-muted/90 bg-background relative'>
        <textarea
          ref={textareaRef}
          className='w-full min-h-12 text-sm bg-background outline-none resize-none'
          placeholder='发送消息、输入 @ 或 / 选择技能'
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          value={value}
        />
        <div className='w-full flex flex-row justify-between items-center'>
          <div className='flex items-center gap-2'>
            <TooltipSimple title='选择文件'>
              <Button variant='ghost' size='icon'>
                <span className='icon-[hugeicons--link-02] text-xl' />
              </Button>
            </TooltipSimple>

            <TooltipSimple title='启用联网搜索'>
              <Button variant='ghost' size='sm'>
                <span className='icon-[hugeicons--internet]' />
                联网
              </Button>
            </TooltipSimple>

            <TooltipSimple title='启用深度思考'>
              <Button variant='secondary' size='sm'>
                <span className='icon-[hugeicons--bitcoin-mind]' />
                深度思考
              </Button>
            </TooltipSimple>
          </div>
          <div className='flex items-center gap-1'>
            <TooltipSimple title='语音输入'>
              <Button
                size='icon'
                variant='ghost'
              >
                <span className='icon-[material-symbols--auto-detect-voice] text-xl' />
              </Button>
            </TooltipSimple>
            <Button
              size='icon'
              disabled={isLoading || !value.trim()}
              onClick={onSend}
              className='bg-primary hover:bg-primary/90'
            >
              <ArrowUpIcon />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MessageInput
