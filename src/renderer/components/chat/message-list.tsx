import React from 'react'
import type { Message } from '@/components/chat/types'
import MessageItem from '@/components/chat/message-item'
import { ScrollArea } from '@/components/ui/scroll-area'

interface MessageListProps {
  messages: Message[]
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  return (
    <ScrollArea className='h-full'>
      {messages.map((message, index) => (
        <MessageItem key={index} message={message} />
      ))}
    </ScrollArea>
  )
}

export default MessageList
