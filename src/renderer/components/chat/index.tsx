
import React from 'react'
import type { Message } from '@/components/chat/types'
import MessageList from '@/components/chat/message-list'
import MessageInput from '@/components/chat/message-input'
import EmptyState from '@/components/chat/empty-state'

export interface ChatProps {
  messages: Message[]
  inputValue: string
  isLoading?: boolean
  onInputChange: (value: string) => void
  onSend: () => void
  onClearMessages?: () => void
}

const Chat: React.FC<ChatProps> = ({
  messages,
  inputValue,
  isLoading = false,
  onInputChange,
  onSend,
  onClearMessages,
}) => {
  return (
    <div className="flex flex-col h-full w-full bg-[#fafbfc]">
      <div className="flex-1 flex flex-col justify-end max-w-3xl mx-auto w-full px-2 md:px-0 py-8">
        {messages.length === 0 ? (
          <EmptyState />
        ) : (
          <MessageList messages={messages} />
        )}
      </div>
      <div className="max-w-3xl mx-auto w-full px-2 md:px-0">
        <MessageInput
          value={inputValue}
          onChange={onInputChange}
          onSend={onSend}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}

export default Chat

