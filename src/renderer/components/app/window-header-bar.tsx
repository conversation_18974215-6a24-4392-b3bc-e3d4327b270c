import { MinusIcon, SquareIcon, XIcon } from 'lucide-react'
import { But<PERSON> } from '../ui/button'

export const WindowHeaderBar = () => {
  return (
    <div className='flex flex-row justify-end items-center drag'>
      <div className='flex flex-row items-center p-0.5 no-drag'>
        <Button size='icon' variant='ghost'>
          <MinusIcon className='size-5' />
        </Button>
        <Button size='icon' variant='ghost'>
          <SquareIcon className='size-4' />
        </Button>
        <Button
          size='icon'
          variant='ghost'
          className='hover:bg-destructive hover:text-white'
        >
          <XIcon className='size-5' />
        </Button>
      </div>
    </div>
  )
}
