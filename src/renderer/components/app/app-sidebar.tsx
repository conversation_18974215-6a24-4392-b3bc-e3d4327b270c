import type { ReactNode } from 'react'
import { useLocation, useNavigate } from '@tanstack/react-router'
import { Button } from '@/components/ui/button.tsx'
import { BugIcon, InfoIcon, Settings2Icon } from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip.tsx'
import { ScrollArea } from '@/components/ui/scroll-area.tsx'
import { cn } from '@/lib/utils.ts'

import Logo from '@/assets/logo.svg?react'
import SearchIcon from '@/assets/icons/search.svg?react'
import CalendarIcon from '@/assets/icons/calendar.svg?react'
import NoteBookIcon from '@/assets/icons/notebook.svg?react'

const TooltipWrapper = ({
  children,
  title,
}: {
  children: ReactNode
  title: string
}) => (
  <Tooltip>
    <TooltipTrigger asChild>{children}</TooltipTrigger>
    <TooltipContent side='right'>{title}</TooltipContent>
  </Tooltip>
)
interface Menu {
  icon: ReactNode
  to: string
  title: string
}
const menus: Menu[] = [
  {
    icon: <SearchIcon className='size-5 font-bold' />,
    to: '/app/chat',
    title: 'AI 搜索',
  },
  {
    icon: <span className="icon-[hugeicons--notion-01] font-semibold text-2xl" />,
    to: '/app/flash-memory',
    title: '闪记',
  },
  {
    icon: <span className="icon-[akar-icons--schedule] text-xl" />,
    to: '/app/calendar',
    title: '我的日程',
  },
]

export function AppSidebar() {
  const navigate = useNavigate()
  const location = useLocation()

  const handleNavigate = (menu: Menu) => {
    if (location.pathname === menu.to) {
      return
    }
    navigate({ to: menu.to })
  }
  return (
    <section
      className={cn(
        'drag w-16 gap-2 shrink-0 h-screen flex flex-col pb-2.5 pt-2.5',
        {
          'pt-10': window?.smart?.common.isMac(),
        }
      )}
    >
      <div className='flex flex-col gap-2 items-center'>
        <div className='size-8 flex items-center justify-center'>
          <Logo className='size-7 text-primary' />
        </div>
      </div>
      <div className='flex-1'>
        <ScrollArea className='h-full'>
          <div className='flex flex-col items-center gap-1.5 py-3 no-drag'>
            {menus.map(menu => (
              <TooltipWrapper title={menu.title} key={menu.title}>
                <Button
                  size='icon'
                  variant={location.pathname === menu.to ? 'default' : 'ghost'}
                  className={cn(
                    'hover:bg-primary/10',
                    'data-[state=open]:bg-primary/10',
                    'data-[state=selected]:bg-primary/10'
                  )}
                  onClick={() => handleNavigate(menu)}
                >
                  {menu.icon}
                </Button>
              </TooltipWrapper>
            ))}
          </div>
        </ScrollArea>
      </div>
      <div className='flex flex-col gap-1.5 items-center py-2.5 no-drag'>
        <TooltipWrapper title='关于Smart Toolbox'>
          <Button size='icon' variant='ghost'>
            <InfoIcon />
          </Button>
        </TooltipWrapper>
        <Button
          size='icon'
          variant='ghost'
          onClick={() => smart?.common.debug()}
        >
          <BugIcon />
        </Button>
        <TooltipWrapper title='设置'>
          <Button
            size='icon'
            variant={
              location.pathname.startsWith('/app/settings')
                ? 'default'
                : 'ghost'
            }
            onClick={() => navigate({ to: '/app/settings/base' })}
          >
            <Settings2Icon />
          </Button>
        </TooltipWrapper>
      </div>
    </section>
  )
}
