import { type FC, type ReactNode, useEffect, useRef, useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog.tsx'
import { Button } from '@/components/ui/button.tsx'
import { Label } from '@/components/ui/label.tsx'
import { Input } from '@/components/ui/input.tsx'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { UploadIcon } from 'lucide-react'

export interface IProviderAddProps {
  children: ReactNode
}

export const ProviderAdd: FC<IProviderAddProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [name, setName] = useState('')
  const [icon, setIcon] = useState('')
  const [baseUrl, setBaseUrl] = useState('')
  const [type, setType] = useState('')

  useEffect(() => {
    if (baseUrl) {
      fetch(baseUrl)
        .then(res => res.json())
        .then(data => {
          console.log(data)
        })
    }
  }, [baseUrl])

  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleIconChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setIcon(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSelectIcon = async () => {
    const files = await smart.common.selectFile({
      properties: ['openFile'],
      filters: [{ name: '图标', extensions: ['ico', 'png', 'jpg', 'jpeg'] }],
    })
    if (files?.length) {
      const file = files[0]
      const filePath = await smart.common.saveFile(file)
      setIcon(filePath)
    }
  }

  const handleResetIcon = () => {
    setIcon('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleOperateProvider = () => {}

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>添加服务商</DialogTitle>
        </DialogHeader>
        <div className='space-y-4'>
          <div className='flex justify-center'>
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Avatar className='rounded-lg! size-12'>
                  <AvatarImage className='rounded-lg' src={icon} />
                  <AvatarFallback className='rounded-lg text-xl font-bold'>
                    {(name || 'S').slice(0, 1).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className='w-30'
                side='bottom'
                sideOffset={0}
              >
                <DropdownMenuItem onClick={handleSelectIcon}>
                  <UploadIcon />
                  上传图标
                </DropdownMenuItem>
                {icon && (
                  <DropdownMenuItem onClick={handleResetIcon}>
                    重置
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className='space-y-2'>
            <Label htmlFor='provider-name'>服务商名称</Label>
            <Input
              id='provider-name'
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder='例如：OpenAI'
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='provider-type'>服务商类型</Label>
            <Select onValueChange={setType} value={type}>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='选择服务商类型' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='openai'>OpenAI</SelectItem>
                <SelectItem value='azure'>Azure</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className='flex justify-end space-x-2'>
            <Button variant='outline' onClick={() => setIsOpen(false)}>
              取消
            </Button>
            <Button onClick={handleOperateProvider}>添加</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
