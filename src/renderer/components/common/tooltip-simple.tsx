import type { ReactNode } from 'react'
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'

interface TooltipSimpleProps {
  children: ReactNode
  title: string
  side?: 'top' | 'right' | 'bottom' | 'left'
}

export const TooltipSimple = ({
  children,
  title,
  side = 'top',
}: TooltipSimpleProps) => (
  <Tooltip>
    <TooltipTrigger asChild>{children}</TooltipTrigger>
    <TooltipContent side={side}>{title}</TooltipContent>
  </Tooltip>
)
