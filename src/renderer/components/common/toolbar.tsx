import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { InfoIcon, MenuIcon, UserIcon } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

const isIOS =
  window.navigator.userAgent.includes('iPhone') ||
  window.navigator.userAgent.includes('iPad') ||
  window.navigator.userAgent.includes('iPod') ||
  window.navigator.userAgent.includes('Mac')
const isAndroid = window.navigator.userAgent.includes('Android')

export const Toolbar = () => {
  console.log(isIOS, isAndroid, window.navigator.userAgent)

  return (
    <div
      className={cn(
        'h-12 bg-background backdrop-blur-sm border-b border-border/15'
      )}
    >
      <div
        className={cn('flex items-center justify-between h-full px-4', {
          'pl-12': isIOS,
          'pr-12': isAndroid,
        })}
      >
        <div className='flex items-center gap-2'>
          <Avatar className='w-6 h-6 rounded-sm'>
            <AvatarImage  src='https://ss0.bdstatic.com/70cFvXSh_Q1YnxGkpoWK1HF6hhy/it/u=3634180796,1033544592&fm=253&gp=0.jpg' />
            <AvatarFallback>
              <UserIcon className='w-4 h-4' />
            </AvatarFallback>
          </Avatar>
          <h2 className='text-sm font-bold text-primary'>设置</h2>
        </div>
        {isIOS ? (
          <div className='flex items-center gap-2'>
            <Button variant='ghost' size='icon'>
              <InfoIcon className='w-4 h-4' />
            </Button>
          </div>
        ) : (
          <div className='flex items-center gap-2'>
            <Button variant='ghost' size='icon'>
              <MenuIcon />
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
