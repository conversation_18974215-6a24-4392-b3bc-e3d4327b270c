import { useState, useEffect, useCallback } from 'react'
import { toast } from 'sonner'
import type {
  ClipboardHistoryItem,
  ClipboardHistoryState,
  ClipboardHistoryActions,
  ClipboardContentType
} from './types'
import { getPrimaryContentType, getAvailableContentTypes } from './types'

const MAX_HISTORY_ITEMS = 100
const STORAGE_KEY = 'clipboard-history'

function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

function createPreview(content: ClipboardData): string {
  const primaryType = getPrimaryContentType(content.contentType)

  switch (primaryType) {
    case 'text':
      return content.text?.slice(0, 100) || ''
    case 'html':
      // 移除HTML标签，只保留文本内容
      const textContent = content.html?.replace(/<[^>]*>/g, '') || ''
      return textContent.slice(0, 100)
    case 'files':
      const fileCount = content.files?.length || 0
      const firstFile = content.files?.[0] || ''
      return fileCount > 1
        ? `${fileCount} 个文件 (${firstFile.split('/').pop()}, ...)`
        : firstFile.split('/').pop() || ''
    case 'image':
      return '图片'
    default:
      return '未知内容'
  }
}

function calculateSize(content: ClipboardData): number {
  let size = 0
  if (content.text) size += content.text.length
  if (content.html) size += content.html.length
  if (content.image) size += content.image.length
  if (content.files) size += content.files.join('').length
  return size
}

export function useClipboardHistory(maxItems: number = MAX_HISTORY_ITEMS) {
  const [state, setState] = useState<ClipboardHistoryState>({
    items: [],
    isLoading: false,
    searchQuery: '',
    selectedType: 'all'
  })

  // 从本地存储加载历史记录
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const items = JSON.parse(stored) as ClipboardHistoryItem[]
        setState(prev => ({ ...prev, items }))
      }
    } catch (error) {
      console.error('加载剪切板历史失败:', error)
    }
  }, [])

  // 保存到本地存储
  const saveToStorage = useCallback((items: ClipboardHistoryItem[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(items))
    } catch (error) {
      console.error('保存剪切板历史失败:', error)
    }
  }, [])

  // 添加新项目
  const addItem = useCallback((content: ClipboardData) => {
    if (!content || content.contentType.includes('empty')) return

    const primaryType = getPrimaryContentType(content.contentType)
    const newItem: ClipboardHistoryItem = {
      id: generateId(),
      content,
      timestamp: Date.now(),
      preview: createPreview(content),
      size: calculateSize(content),
      currentDisplayType: primaryType
    }

    setState(prev => {
      // 检查是否已存在相同内容
      const primaryType = getPrimaryContentType(content.contentType)
      const isDuplicate = prev.items.some(item => {
        const itemPrimaryType = getPrimaryContentType(item.content.contentType)
        if (itemPrimaryType !== primaryType) return false

        switch (primaryType) {
          case 'text':
            return item.content.text === content.text
          case 'html':
            return item.content.html === content.html
          case 'files':
            return JSON.stringify(item.content.files) === JSON.stringify(content.files)
          case 'image':
            return item.content.image === content.image
          default:
            return false
        }
      })

      if (isDuplicate) return prev

      const newItems = [newItem, ...prev.items].slice(0, maxItems)
      saveToStorage(newItems)
      
      return {
        ...prev,
        items: newItems
      }
    })
  }, [maxItems, saveToStorage])

  // 删除项目
  const removeItem = useCallback((id: string) => {
    setState(prev => {
      const newItems = prev.items.filter(item => item.id !== id)
      saveToStorage(newItems)
      return {
        ...prev,
        items: newItems
      }
    })
    toast.success('已删除剪切板历史项')
  }, [saveToStorage])

  // 清空历史
  const clearHistory = useCallback(() => {
    setState(prev => ({
      ...prev,
      items: []
    }))
    saveToStorage([])
    toast.success('已清空剪切板历史')
  }, [saveToStorage])

  // 复制到剪切板
  const copyToClipboard = useCallback(async (item: ClipboardHistoryItem, type?: ClipboardContentType) => {
    try {
      if (!window.smart?.clipboard) {
        throw new Error('剪切板API不可用')
      }

      const targetType = type || item.currentDisplayType || getPrimaryContentType(item.content.contentType)

      // 根据指定类型创建剪切板数据
      const clipboardData: ClipboardData = {
        contentType: [targetType]
      }

      switch (targetType) {
        case 'text':
          if (item.content.text) {
            clipboardData.text = item.content.text
          }
          break
        case 'html':
          if (item.content.html) {
            clipboardData.html = item.content.html
          }
          break
        case 'image':
          if (item.content.image) {
            clipboardData.image = item.content.image
          }
          break
        case 'files':
          if (item.content.files) {
            clipboardData.files = item.content.files
          }
          break
      }

      await window.smart.clipboard.set(clipboardData)
      toast.success(`已复制${targetType === 'text' ? '文本' : targetType === 'html' ? 'HTML' : targetType === 'image' ? '图片' : '文件'}到剪切板`)
    } catch (error) {
      console.error('复制失败:', error)
      toast.error('复制失败')
    }
  }, [])

  // 设置搜索查询
  const setSearchQuery = useCallback((query: string) => {
    setState(prev => ({
      ...prev,
      searchQuery: query
    }))
  }, [])

  // 设置选中的类型
  const setSelectedType = useCallback((type: ClipboardContentType | 'all') => {
    setState(prev => ({
      ...prev,
      selectedType: type
    }))
  }, [])

  // 更新项目的显示类型
  const updateItemDisplayType = useCallback((id: string, type: ClipboardContentType) => {
    setState(prev => {
      const newItems = prev.items.map(item =>
        item.id === id
          ? { ...item, currentDisplayType: type }
          : item
      )
      saveToStorage(newItems)
      return {
        ...prev,
        items: newItems
      }
    })
  }, [saveToStorage])

  // 监听剪切板变化
  useEffect(() => {
    if (!window.smart?.clipboard) {
      console.warn('剪切板API不可用')
      return
    }

    const unsubscribe = window.smart.clipboard.watchHandler((data) => {
      addItem(data)
    })

    return unsubscribe
  }, [addItem])

  // 过滤项目
  const filteredItems = state.items.filter(item => {
    // 类型过滤
    const primaryType = getPrimaryContentType(item.content.contentType)
    if (state.selectedType !== 'all' && primaryType !== state.selectedType) {
      return false
    }

    // 搜索过滤
    if (state.searchQuery) {
      const query = state.searchQuery.toLowerCase()
      return item.preview?.toLowerCase().includes(query) || false
    }

    return true
  })

  const actions: ClipboardHistoryActions = {
    addItem,
    removeItem,
    clearHistory,
    copyToClipboard,
    setSearchQuery,
    setSelectedType,
    updateItemDisplayType
  }

  return {
    ...state,
    filteredItems,
    actions
  }
}
