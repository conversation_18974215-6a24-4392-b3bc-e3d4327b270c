export interface ClipboardHistoryItem {
  id: string
  content: ClipboardData
  timestamp: number
  preview?: string
  size?: number
  currentDisplayType?: ClipboardContentType // 当前显示的类型
}

export interface ClipboardHistoryState {
  items: ClipboardHistoryItem[]
  isLoading: boolean
  searchQuery: string
  selectedType: ClipboardContentType | 'all'
}

export type ClipboardContentType = 'text' | 'image' | 'files' | 'html' | 'empty'

// 获取主要内容类型（从数组中取第一个）
export function getPrimaryContentType(contentTypes: ClipboardContentType[]): ClipboardContentType {
  return contentTypes[0] || 'empty'
}

// 获取可用的内容类型（过滤掉empty和无内容的类型）
export function getAvailableContentTypes(content: ClipboardData): ClipboardContentType[] {
  const availableTypes: ClipboardContentType[] = []

  if (content.text) availableTypes.push('text')
  if (content.html) availableTypes.push('html')
  if (content.image) availableTypes.push('image')
  if (content.files && content.files.length > 0) availableTypes.push('files')

  return availableTypes
}

// 根据类型获取对应的内容
export function getContentByType(content: ClipboardData, type: ClipboardContentType): any {
  switch (type) {
    case 'text':
      return content.text
    case 'html':
      return content.html
    case 'image':
      return content.image
    case 'files':
      return content.files
    default:
      return null
  }
}

export interface ClipboardHistoryActions {
  addItem: (content: ClipboardData) => void
  removeItem: (id: string) => void
  clearHistory: () => void
  copyToClipboard: (item: ClipboardHistoryItem, type?: ClipboardContentType) => void
  setSearchQuery: (query: string) => void
  setSelectedType: (type: ClipboardContentType | 'all') => void
  updateItemDisplayType: (id: string, type: ClipboardContentType) => void
}

export interface ClipboardHistoryProps {
  maxItems?: number
  className?: string
  onItemSelect?: (item: ClipboardHistoryItem) => void
}
