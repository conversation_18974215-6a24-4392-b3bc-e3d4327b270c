import React from 'react'
import ClipboardHistory from './clipboard-history'

/**
 * 剪切板历史组件演示页面
 * 展示多类型切换功能
 */
export default function ClipboardHistoryDemo() {
  const handleItemSelect = (item: any) => {
    console.log('选中的剪切板项:', item)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-background/90 p-6">
      <div className="max-w-5xl mx-auto">
        <div className="mb-8 text-center">
          <div className="inline-flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-primary/10 border border-primary/20">
              <svg className="size-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              剪切板历史管理
            </h1>
          </div>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            实时监听剪切板变化，支持多类型内容切换显示，让您的工作更加高效。
          </p>
        </div>

        {/* 主要组件 */}
        <div className="mb-8">
          <ClipboardHistory
            maxItems={50}
            onItemSelect={handleItemSelect}
            className="h-[700px] shadow-2xl"
          />
        </div>

        {/* 新功能亮点 */}
        <div className="mb-8 p-6 rounded-xl bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <svg className="size-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            多类型切换功能
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-primary">智能类型检测</h4>
              <p className="text-sm text-muted-foreground">
                自动检测剪切板内容包含的所有类型（文本、HTML、图片、文件），并在界面上显示可切换的类型标签。
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-primary">快捷切换</h4>
              <p className="text-sm text-muted-foreground">
                点击类型图标按钮可以快速切换查看不同类型的内容，复制时只复制当前显示的类型内容。
              </p>
            </div>
          </div>
        </div>

        {/* 功能特性 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="p-6 rounded-xl bg-background/60 border border-border/50 backdrop-blur-sm">
            <div className="p-3 rounded-lg bg-blue-500/10 w-fit mb-4">
              <svg className="size-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.79 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.79 4 8 4s8-1.79 8-4M4 7c0-2.21 3.79-4 8-4s8 1.79 8 4" />
              </svg>
            </div>
            <h3 className="font-semibold mb-2">多类型支持</h3>
            <p className="text-sm text-muted-foreground">
              支持文本、HTML、图片、文件等多种类型的内容
            </p>
          </div>

          <div className="p-6 rounded-xl bg-background/60 border border-border/50 backdrop-blur-sm">
            <div className="p-3 rounded-lg bg-green-500/10 w-fit mb-4">
              <svg className="size-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </div>
            <h3 className="font-semibold mb-2">类型切换</h3>
            <p className="text-sm text-muted-foreground">
              一键切换查看不同类型的内容表示
            </p>
          </div>

          <div className="p-6 rounded-xl bg-background/60 border border-border/50 backdrop-blur-sm">
            <div className="p-3 rounded-lg bg-purple-500/10 w-fit mb-4">
              <svg className="size-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="font-semibold mb-2">精准复制</h3>
            <p className="text-sm text-muted-foreground">
              只复制当前选中类型的内容到剪切板
            </p>
          </div>

          <div className="p-6 rounded-xl bg-background/60 border border-border/50 backdrop-blur-sm">
            <div className="p-3 rounded-lg bg-orange-500/10 w-fit mb-4">
              <svg className="size-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="font-semibold mb-2">实时监听</h3>
            <p className="text-sm text-muted-foreground">
              自动监听剪切板变化，实时更新历史记录
            </p>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="p-6 rounded-xl bg-gradient-to-r from-muted/50 to-muted/30 border border-border/50 backdrop-blur-sm">
          <h3 className="font-semibold mb-4 flex items-center gap-2">
            <svg className="size-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            使用说明
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ul className="space-y-2">
              <li className="flex items-start gap-2 text-sm">
                <span className="text-primary">•</span>
                <span>复制包含多种类型的内容会自动检测所有类型</span>
              </li>
              <li className="flex items-start gap-2 text-sm">
                <span className="text-primary">•</span>
                <span>点击类型图标按钮可以快速切换显示类型</span>
              </li>
              <li className="flex items-start gap-2 text-sm">
                <span className="text-primary">•</span>
                <span>复制按钮会复制当前显示类型的内容</span>
              </li>
            </ul>
            <ul className="space-y-2">
              <li className="flex items-start gap-2 text-sm">
                <span className="text-primary">•</span>
                <span>支持搜索和按类型筛选历史记录</span>
              </li>
              <li className="flex items-start gap-2 text-sm">
                <span className="text-primary">•</span>
                <span>可以删除单个项目或清空所有历史</span>
              </li>
              <li className="flex items-start gap-2 text-sm">
                <span className="text-primary">•</span>
                <span>历史记录自动保存，重启后依然可用</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
