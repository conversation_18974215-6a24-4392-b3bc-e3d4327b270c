# 剪切板历史管理组件

一个功能完整的剪切板历史管理组件，基于 shadcn/ui 设计系统构建，支持实时监听剪切板变化并提供丰富的管理功能。

## 功能特性

- ✅ **实时监听**: 自动监听剪切板变化，实时添加历史记录
- ✅ **多种类型支持**: 文本、图片、文件、HTML 等多种内容类型
- ✅ **快捷类型切换**: 通过图标按钮快速切换查看不同类型的内容
- ✅ **精准复制**: 只复制当前选中类型的内容到剪切板
- ✅ **搜索过滤**: 支持关键词搜索和按类型筛选
- ✅ **快速操作**: 一键复制历史项到剪切板
- ✅ **历史管理**: 支持删除单个项目或清空所有历史
- ✅ **本地存储**: 历史记录持久化保存
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **无障碍支持**: 完整的键盘导航和屏幕阅读器支持

## 组件结构

```
clipboard-history/
├── index.tsx                      # 主入口文件
├── types.ts                       # 类型定义
├── clipboard-history.tsx          # 主组件
├── clipboard-history-toolbar.tsx  # 工具栏组件
├── clipboard-history-list.tsx     # 列表组件
├── clipboard-history-item.tsx     # 历史项组件
├── use-clipboard-history.ts       # 自定义Hook
├── demo.tsx                       # 演示组件
└── README.md                      # 说明文档
```

## 快速开始

### 基础使用

```tsx
import ClipboardHistory from '@/components/plugins/clipboard-history'

function App() {
  return (
    <div className="container mx-auto p-4">
      <ClipboardHistory />
    </div>
  )
}
```

### 自定义配置

```tsx
import ClipboardHistory from '@/components/plugins/clipboard-history'

function App() {
  const handleItemSelect = (item) => {
    console.log('选中的项目:', item)
  }

  return (
    <ClipboardHistory
      maxItems={50}                    // 最大历史记录数量
      className="h-[600px]"           // 自定义样式
      onItemSelect={handleItemSelect}  // 选中回调
    />
  )
}
```

## API 参考

### ClipboardHistory Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `maxItems` | `number` | `100` | 最大历史记录数量 |
| `className` | `string` | - | 自定义CSS类名 |
| `onItemSelect` | `(item: ClipboardHistoryItem) => void` | - | 选中历史项时的回调函数 |

### ClipboardHistoryItem 类型

```typescript
interface ClipboardHistoryItem {
  id: string                    // 唯一标识符
  content: ClipboardData        // 剪切板内容
  timestamp: number             // 时间戳
  preview?: string              // 内容预览
  size?: number                 // 内容大小（字节）
}
```

### ClipboardData 类型

```typescript
interface ClipboardData {
  text?: string                 // 文本内容
  image?: Buffer                // 图片数据
  files?: string[]              // 文件路径列表
  html?: string                 // HTML内容
  contentType: 'text' | 'image' | 'files' | 'html' | 'empty'
}
```

## 自定义Hook

### useClipboardHistory

```tsx
import { useClipboardHistory } from '@/components/plugins/clipboard-history/use-clipboard-history'

function CustomComponent() {
  const {
    items,              // 所有历史项
    filteredItems,      // 过滤后的历史项
    isLoading,          // 加载状态
    searchQuery,        // 搜索查询
    selectedType,       // 选中的类型
    actions             // 操作方法
  } = useClipboardHistory(50)

  return (
    <div>
      {filteredItems.map(item => (
        <div key={item.id} onClick={() => actions.copyToClipboard(item)}>
          {item.preview}
        </div>
      ))}
    </div>
  )
}
```

## 样式定制

组件使用 Tailwind CSS 和 shadcn/ui 组件库，可以通过以下方式定制样式：

### 1. 通过 className 属性

```tsx
<ClipboardHistory className="border-2 border-primary rounded-xl" />
```

### 2. 通过 CSS 变量

```css
.clipboard-history {
  --clipboard-item-hover: theme(colors.blue.50);
  --clipboard-item-selected: theme(colors.blue.100);
}
```

## 注意事项

1. **权限要求**: 需要剪切板访问权限，确保 `window.smart.clipboard` API 可用
2. **性能考虑**: 大量历史记录可能影响性能，建议设置合理的 `maxItems` 值
3. **存储限制**: 历史记录保存在 localStorage 中，注意存储空间限制
4. **图片处理**: 图片以 Base64 格式存储，大图片会占用较多内存

## 故障排除

### 剪切板监听不工作

检查剪切板API是否可用：

```javascript
if (!window.smart?.clipboard) {
  console.error('剪切板API不可用')
}
```

### 历史记录不保存

检查 localStorage 是否可用：

```javascript
try {
  localStorage.setItem('test', 'test')
  localStorage.removeItem('test')
} catch (error) {
  console.error('localStorage不可用:', error)
}
```

### 性能问题

1. 减少 `maxItems` 值
2. 避免保存过大的图片或文件
3. 定期清理历史记录

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的剪切板历史管理功能
- 实现搜索和过滤功能
- 添加多种内容类型支持
