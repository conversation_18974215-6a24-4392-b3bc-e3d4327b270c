import { ScrollArea } from '@/components/ui/scroll-area'
import { ClipboardHistoryItem } from './clipboard-history-item'
import type { ClipboardHistoryItem as ClipboardHistoryItemType, ClipboardContentType } from './types'

interface ClipboardHistoryListProps {
  items: ClipboardHistoryItemType[]
  isLoading: boolean
  onCopy: (item: ClipboardHistoryItemType, type?: ClipboardContentType) => void
  onDelete: (id: string) => void
  onSelect?: (item: ClipboardHistoryItemType) => void
  onTypeChange?: (id: string, type: ClipboardContentType) => void
}

function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
        <svg
          className="w-8 h-8 text-muted-foreground"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-muted-foreground mb-2">
        暂无剪切板历史
      </h3>
      <p className="text-sm text-muted-foreground max-w-sm">
        复制一些内容到剪切板，它们会自动出现在这里
      </p>
    </div>
  )
}

function LoadingState() {
  return (
    <div className="space-y-4 p-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className="animate-pulse">
          <div className="bg-muted rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-muted-foreground/20 rounded" />
                <div className="w-12 h-5 bg-muted-foreground/20 rounded" />
              </div>
              <div className="w-16 h-4 bg-muted-foreground/20 rounded" />
            </div>
            <div className="space-y-2">
              <div className="w-full h-4 bg-muted-foreground/20 rounded" />
              <div className="w-3/4 h-4 bg-muted-foreground/20 rounded" />
            </div>
            <div className="w-20 h-3 bg-muted-foreground/20 rounded" />
          </div>
        </div>
      ))}
    </div>
  )
}

function NoResultsState() {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
        <svg
          className="w-8 h-8 text-muted-foreground"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-muted-foreground mb-2">
        未找到匹配的内容
      </h3>
      <p className="text-sm text-muted-foreground max-w-sm">
        尝试调整搜索条件或筛选类型
      </p>
    </div>
  )
}

export function ClipboardHistoryList({
  items,
  isLoading,
  onCopy,
  onDelete,
  onSelect,
  onTypeChange
}: ClipboardHistoryListProps) {
  if (isLoading) {
    return <LoadingState />
  }

  if (items.length === 0) {
    return <EmptyState />
  }

  return (
    <ScrollArea className="flex-1">
      <div className="space-y-3 p-4">
        {items.map((item) => (
          <ClipboardHistoryItem
            key={item.id}
            item={item}
            onCopy={onCopy}
            onDelete={onDelete}
            onSelect={onSelect}
            onTypeChange={onTypeChange}
          />
        ))}
      </div>
    </ScrollArea>
  )
}
