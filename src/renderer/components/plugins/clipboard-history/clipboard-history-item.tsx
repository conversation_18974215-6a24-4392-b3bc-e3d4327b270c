import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { Copy, Trash2, FileText, Image, Files, Code } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

import { cn } from '@/lib/utils'
import type { ClipboardHistoryItem, ClipboardContentType } from './types'
import { getPrimaryContentType, getAvailableContentTypes } from './types'

interface ClipboardHistoryItemProps {
  item: ClipboardHistoryItem
  onCopy: (item: ClipboardHistoryItem, type?: ClipboardContentType) => void
  onDelete: (id: string) => void
  onSelect?: (item: ClipboardHistoryItem) => void
  onTypeChange?: (id: string, type: ClipboardContentType) => void
}

function getTypeIcon(contentType: string) {
  switch (contentType) {
    case 'text':
      return FileText
    case 'image':
      return Image
    case 'files':
      return Files
    case 'html':
      return Code
    default:
      return FileText
  }
}

function getTypeLabel(contentType: string) {
  switch (contentType) {
    case 'text':
      return '文本'
    case 'image':
      return '图片'
    case 'files':
      return '文件'
    case 'html':
      return 'HTML'
    default:
      return '未知'
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

function renderContent(
  item: ClipboardHistoryItem,
  displayType: ClipboardContentType
) {
  const { content } = item

  switch (displayType) {
    case 'text':
      return (
        <div className='space-y-2'>
          <div className='p-3 rounded-md bg-muted/20 border border-border/30'>
            <p className='text-sm text-foreground line-clamp-3 leading-relaxed'>
              {content.text}
            </p>
          </div>
        </div>
      )

    case 'image':
      return (
        <div className='space-y-3'>
          {content.image && (
            <div className='relative max-w-40 max-h-40 overflow-hidden rounded-lg border border-border/50 bg-muted/20 p-2'>
              <img
                src={`data:image/png;base64,${content.image}`}
                alt='剪切板图片'
                className='w-full h-full object-contain rounded'
              />
            </div>
          )}
        </div>
      )

    case 'files':
      return (
        <div className='space-y-3'>
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium text-muted-foreground'>
              {content.files?.length || 0} 个文件
            </span>
          </div>
          <div className='space-y-2'>
            {content.files?.slice(0, 3).map((file, index) => (
              <div
                key={index}
                className='flex items-center gap-2 p-2 rounded-md bg-muted/30 border border-border/30'
              >
                <Files className='size-4 text-muted-foreground flex-shrink-0' />
                <span className='text-xs text-foreground truncate font-mono'>
                  {file.split('/').pop()}
                </span>
              </div>
            ))}
            {(content.files?.length || 0) > 3 && (
              <div className='text-xs text-muted-foreground p-2 rounded-md bg-muted/20 text-center'>
                还有 {(content.files?.length || 0) - 3} 个文件...
              </div>
            )}
          </div>
        </div>
      )

    case 'html':
      return (
        <div className='space-y-3'>
          <div className='p-3 rounded-md bg-muted/20 border border-border/30'>
            <p className='text-sm text-foreground line-clamp-3 leading-relaxed font-mono'>
              {item.preview}
            </p>
          </div>
        </div>
      )

    default:
      return <p className='text-sm text-muted-foreground'>未知内容类型</p>
  }
}

export function ClipboardHistoryItem({
  item,
  onCopy,
  onDelete,
  onSelect,
  onTypeChange,
}: ClipboardHistoryItemProps) {
  const availableTypes = getAvailableContentTypes(item.content)
  const currentType =
    item.currentDisplayType || getPrimaryContentType(item.content.contentType)
  const TypeIcon = getTypeIcon(currentType)
  const typeLabel = getTypeLabel(currentType)
  const hasMultipleTypes = availableTypes.length > 1
  const timeAgo = formatDistanceToNow(item.timestamp, {
    addSuffix: true,
    locale: zhCN,
  })

  return (
    <div
      className={cn(
        'group p-4 rounded-lg border border-border/50 bg-background/50',
        'hover:bg-accent/50 hover:border-border transition-all duration-200 cursor-pointer',
        'backdrop-blur-sm'
      )}
      onClick={() => onSelect?.(item)}
    >
      <div className='space-y-3'>
        {/* 头部信息 */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3'>
            <div
              className={cn(
                'p-2 rounded-md transition-colors',
                currentType === 'text' &&
                  'bg-blue-500/10 text-blue-600 dark:text-blue-400',
                currentType === 'image' &&
                  'bg-green-500/10 text-green-600 dark:text-green-400',
                currentType === 'files' &&
                  'bg-orange-500/10 text-orange-600 dark:text-orange-400',
                currentType === 'html' &&
                  'bg-purple-500/10 text-purple-600 dark:text-purple-400'
              )}
            >
              <TypeIcon className='size-4' />
            </div>

            <div className='flex items-center gap-2'>
              {/* 类型切换按钮组 */}
              {hasMultipleTypes ? (
                <div className='flex items-center gap-1 p-1 bg-muted/30 rounded-md'>
                  {availableTypes.map(type => {
                    const Icon = getTypeIcon(type)
                    const label = getTypeLabel(type)
                    return (
                      <TooltipProvider key={type}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant={
                                currentType === type ? 'secondary' : 'ghost'
                              }
                              size='sm'
                              className={cn(
                                'h-6 px-2 text-xs',
                                currentType === type && 'shadow-sm'
                              )}
                              onClick={e => {
                                e.stopPropagation()
                                onTypeChange?.(item.id, type)
                              }}
                            >
                              <Icon className='size-3' />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{label}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )
                  })}
                </div>
              ) : (
                <Badge variant='outline' className='text-xs font-medium'>
                  {typeLabel}
                </Badge>
              )}

              {/* 大小和时间信息 */}
              <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                {item.size && (
                  <span className='bg-muted/50 px-2 py-1 rounded'>
                    {formatFileSize(item.size)}
                  </span>
                )}
                <span className='bg-muted/30 px-2 py-1 rounded'>{timeAgo}</span>
              </div>
            </div>
          </div>

          <div className='flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-all duration-200'>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='size-8 hover:bg-primary/10 hover:text-primary'
                    onClick={e => {
                      e.stopPropagation()
                      onCopy(item, currentType)
                    }}
                  >
                    <Copy className='size-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>复制到剪切板</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='size-8 hover:bg-destructive/10 hover:text-destructive'
                    onClick={e => {
                      e.stopPropagation()
                      onDelete(item.id)
                    }}
                  >
                    <Trash2 className='size-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>删除</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* 内容预览 */}
        <div className='pl-1'>{renderContent(item, currentType)}</div>
      </div>
    </div>
  )
}
