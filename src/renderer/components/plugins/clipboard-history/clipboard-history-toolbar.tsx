import {
  Search,
  Filter,
  Trash2,
  FileText,
  Image,
  Files,
  Code,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import type { ClipboardContentType } from './types'

interface ClipboardHistoryToolbarProps {
  searchQuery: string
  selectedType: ClipboardContentType | 'all'
  itemCount: number
  filteredCount: number
  onSearchChange: (query: string) => void
  onTypeChange: (type: ClipboardContentType | 'all') => void
  onClearHistory: () => void
}

const typeOptions = [
  { value: 'all', label: '全部', icon: Filter },
  { value: 'text', label: '文本', icon: FileText },
  { value: 'image', label: '图片', icon: Image },
  { value: 'files', label: '文件', icon: Files },
  { value: 'html', label: 'HTML', icon: Code },
] as const

export function ClipboardHistoryToolbar({
  searchQuery,
  selectedType,
  itemCount,
  filteredCount,
  onSearchChange,
  onTypeChange,
  onClearHistory,
}: ClipboardHistoryToolbarProps) {
  return (
    <div className='flex flex-col gap-4 p-6 border-b border-border/50 bg-gradient-to-r from-background/80 to-background/60 backdrop-blur-sm'>
      {/* 标题和统计 */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-3'>
          <div className='p-2 rounded-lg bg-primary/10'>
            <svg
              className='size-5 text-primary'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
              />
            </svg>
          </div>
          <div>
            <h2 className='text-lg font-semibold text-foreground'>
              剪切板历史
            </h2>
            <Badge variant='outline' className='text-xs mt-1'>
              {filteredCount !== itemCount
                ? `${filteredCount}/${itemCount}`
                : itemCount}{' '}
              项
            </Badge>
          </div>
        </div>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant='outline'
              size='sm'
              disabled={itemCount === 0}
              className='text-destructive hover:text-destructive hover:bg-destructive/10 border-destructive/20'
            >
              <Trash2 className='size-4' />
              清空历史
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认清空历史</AlertDialogTitle>
              <AlertDialogDescription>
                此操作将删除所有剪切板历史记录，且无法撤销。确定要继续吗？
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction
                onClick={onClearHistory}
                className='bg-destructive text-white hover:bg-destructive/90'
              >
                确认清空
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      {/* 搜索和过滤 */}
      <div className='flex gap-3'>
        <div className='relative flex-1'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground size-4' />
          <Input
            placeholder='搜索剪切板内容...'
            value={searchQuery}
            onChange={e => onSearchChange(e.target.value)}
            className='pl-10 bg-background/50 border-border/50 focus:bg-background focus:border-primary/50'
          />
        </div>

        <Select value={selectedType} onValueChange={onTypeChange}>
          <SelectTrigger className='w-36 bg-background/50 border-border/50 hover:bg-background'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {typeOptions.map(option => {
              const Icon = option.icon
              return (
                <SelectItem key={option.value} value={option.value}>
                  <div className='flex items-center gap-2'>
                    <Icon className='size-4' />
                    {option.label}
                  </div>
                </SelectItem>
              )
            })}
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
