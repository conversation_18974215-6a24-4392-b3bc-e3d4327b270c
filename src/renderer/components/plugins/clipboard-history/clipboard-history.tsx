import { cn } from '@/lib/utils'
import { ClipboardHistoryToolbar } from './clipboard-history-toolbar'
import { ClipboardHistoryList } from './clipboard-history-list'
import { useClipboardHistory } from './use-clipboard-history'
import type { ClipboardHistoryProps } from './types'

export default function ClipboardHistory({
  maxItems = 100,
  className,
  onItemSelect,
}: ClipboardHistoryProps) {
  const {
    items,
    filteredItems,
    isLoading,
    searchQuery,
    selectedType,
    actions,
  } = useClipboardHistory(maxItems)

  return (
    <div className={cn(
      'flex flex-col h-full rounded-xl border border-border/50 bg-background/80 backdrop-blur-sm overflow-hidden',
      'shadow-lg shadow-black/5',
      className
    )}>
      <ClipboardHistoryToolbar
        searchQuery={searchQuery}
        selectedType={selectedType}
        itemCount={items.length}
        filteredCount={filteredItems.length}
        onSearchChange={actions.setSearchQuery}
        onTypeChange={actions.setSelectedType}
        onClearHistory={actions.clearHistory}
      />

      <ClipboardHistoryList
        items={filteredItems}
        isLoading={isLoading}
        onCopy={actions.copyToClipboard}
        onDelete={actions.removeItem}
        onSelect={onItemSelect}
        onTypeChange={actions.updateItemDisplayType}
      />
    </div>
  )
}
