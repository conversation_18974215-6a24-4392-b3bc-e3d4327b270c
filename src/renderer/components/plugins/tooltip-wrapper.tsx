import {
  Tooltip,
  Toolt<PERSON>Content,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

export interface TooltipWrapperProps {
  children: React.ReactNode
  content: React.ReactNode
}

export const TooltipWrapper = ({ children, content }: TooltipWrapperProps) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent>
          <p>{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
