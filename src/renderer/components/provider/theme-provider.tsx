import { createContext, useContext, useEffect, useState } from 'react'

const THEME_MODES = {
  DARK: 'dark',
  LIGHT: 'light',
  SYSTEM: 'system',
} as const

type Theme = (typeof THEME_MODES)[keyof typeof THEME_MODES]

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => void
}

const initialState: ThemeProviderState = {
  theme: THEME_MODES.SYSTEM,
  setTheme: () => null,
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

const updateRootClass = (theme: string) => {
  const root = window.document.documentElement
  root.classList.remove(THEME_MODES.LIGHT, THEME_MODES.DARK)
  root.classList.add(theme)
}

const getSystemTheme = (): Theme =>
  window.matchMedia('(prefers-color-scheme: dark)').matches
    ? THEME_MODES.DARK
    : THEME_MODES.LIGHT

export function ThemeProvider({
  children,
  defaultTheme = THEME_MODES.SYSTEM,
  storageKey = 'smart-theme',
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme
  )

  function handleSystemThemeChange() {
    if (theme === THEME_MODES.SYSTEM) {
      updateRootClass(getSystemTheme())
    }
  }

  function updateTheme(newTheme: Theme) {
    localStorage.setItem(storageKey, newTheme)
    setTheme(newTheme)
  }

  useEffect(() => {
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)')

    if (theme === THEME_MODES.SYSTEM) {
      handleSystemThemeChange()
      systemTheme.addEventListener('change', handleSystemThemeChange)
      return () => {
        systemTheme.removeEventListener('change', handleSystemThemeChange)
      }
    }

    updateRootClass(theme)
  }, [theme])

  return (
    <ThemeProviderContext.Provider
      {...props}
      value={{
        theme,
        setTheme: updateTheme,
      }}
    >
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)

  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }

  return context
}
