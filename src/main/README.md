# Smart Toolbox 主进程架构文档

## 概述

Smart Toolbox 主进程采用了全新的模块化架构设计，提供清晰的职责分离、统一的生命周期管理和完善的错误处理机制。

## 新架构设计 (v2.0)

### 目录结构

```
src/main/
├── main.ts                          # 新的主进程入口文件
├── index.ts                         # 兼容性入口文件
├── app/                             # 应用层
│   ├── application.ts               # 应用管理器
│   ├── lifecycle.ts                 # 应用生命周期
│   └── events.ts                    # 应用事件处理
├── core/                            # 核心层
│   ├── config/                      # 配置管理
│   │   ├── config-manager.ts
│   │   ├── config-schema.ts
│   │   └── default-config.ts
│   ├── logging/                     # 日志管理
│   │   ├── log-manager.ts
│   │   ├── log-formatter.ts
│   │   └── log-transport.ts
│   ├── error/                       # 错误处理
│   │   ├── error-handler.ts
│   │   ├── error-types.ts
│   │   └── error-recovery.ts
│   └── service/                     # 服务管理
│       ├── service-manager.ts
│       ├── service-registry.ts
│       ├── service-adapters.ts
│       └── adapters/
│           └── common-core.adapter.ts
├── services/                        # 服务层
│   ├── base/                        # 基础服务
│   │   ├── base-service.ts
│   │   ├── service-interface.ts
│   │   └── service-types.ts
│   ├── clipboard/                   # 剪贴板服务
│   │   └── clipboard-service.ts
│   ├── database/                    # 数据库服务
│   │   └── database-service.ts
│   ├── storage/                     # 存储服务
│   │   └── storage-service.ts
│   ├── window/                      # 窗口服务
│   │   └── window-service.ts
│   └── factory/                     # 服务工厂
│       └── service-factory.ts
├── windows/                         # 窗口层
│   ├── manager/                     # 窗口管理器
│   │   └── window-manager.ts
│   ├── main/                        # 主窗口
│   ├── search/                      # 搜索窗口
│   └── toolbar/                     # 工具栏窗口
├── shared/                          # 共享层
│   ├── types/                       # 类型定义
│   │   ├── app.types.ts
│   │   ├── service.types.ts
│   │   └── window.types.ts
│   ├── constants/                   # 常量定义
│   │   ├── app.constants.ts
│   │   └── service.constants.ts
│   ├── utils/                       # 工具函数
│   │   ├── path.utils.ts
│   │   ├── file.utils.ts
│   │   └── validation.utils.ts
│   └── configs/                     # 配置文件
│       ├── app.config.ts
│       └── service.config.ts
└── tests/                           # 测试文件
    ├── unit/                        # 单元测试
    └── integration/                 # 集成测试
```

### 核心组件

```
Application (应用管理器)
├── ConfigManager (配置管理器)
├── LogManager (日志管理器)
├── ServiceManager (服务管理器)
├── WindowManager (窗口管理器)
└── ErrorHandler (错误处理器)
```

### 组件职责

#### 1. ApplicationManager (应用管理器)
- **文件**: `src/main/core/application-manager.ts`
- **职责**: 应用的整体生命周期管理
- **功能**:
  - 应用启动和初始化
  - 协调各个管理器的初始化顺序
  - 应用关闭和资源清理
  - 全局事件处理
  - 快捷键管理

#### 2. ConfigManager (配置管理器)
- **文件**: `src/main/core/config-manager.ts`
- **职责**: 统一配置管理
- **功能**:
  - 配置文件加载和保存
  - 环境变量处理
  - 配置验证和默认值
  - 配置热更新
  - 多层级配置合并

#### 3. LogManager (日志管理器)
- **文件**: `src/main/core/log-manager.ts`
- **职责**: 统一日志管理
- **功能**:
  - 多级别日志记录 (DEBUG, INFO, WARN, ERROR)
  - 控制台和文件输出
  - 日志格式化 (JSON/文本)
  - 日志文件轮转和清理
  - 日志队列处理

#### 4. ServiceManager (服务管理器)
- **文件**: `src/main/core/service-manager.ts`
- **职责**: 管理所有业务服务
- **功能**:
  - 服务注册和发现
  - 服务依赖关系管理
  - 服务生命周期管理
  - 服务健康检查
  - 拓扑排序初始化

#### 5. WindowManager (窗口管理器)
- **文件**: `src/main/core/window-manager.ts`
- **职责**: 管理所有窗口组件
- **功能**:
  - 窗口创建和销毁
  - 窗口状态管理
  - 窗口类型管理 (主窗口、搜索窗口、工具栏)
  - 窗口事件处理
  - 单例模式支持

#### 6. ErrorHandler (错误处理器)
- **文件**: `src/main/core/error-handler.ts`
- **职责**: 统一错误处理
- **功能**:
  - 错误分类和严重程度判断
  - 全局异常捕获
  - 错误日志记录
  - 用户错误通知
  - 错误历史和统计

## 服务架构

### 服务接口 (IService)

所有服务都必须实现 `IService` 接口：

```typescript
interface IService {
  readonly name: string
  readonly dependencies?: string[]
  initialize(): Promise<void>
  start?(): Promise<void>
  stop?(): Promise<void>
  destroy(): Promise<void>
  getState?(): ServiceState
  healthCheck?(): Promise<boolean>
}
```

### 服务适配器

为了兼容现有服务，创建了服务适配器：

- **CommonCoreAdapter**: 通用核心服务
- **DatabaseServiceAdapter**: 数据库服务
- **StorageServiceAdapter**: 存储服务
- **ClipboardServiceAdapter**: 剪贴板服务
- **WindowServiceAdapter**: 窗口服务
- **GlobalHookServiceAdapter**: 全局钩子服务
- **SmartClipboardServiceAdapter**: 智能剪贴板服务

### 服务依赖关系

```
CommonCore (基础服务)
├── Database (依赖 CommonCore)
├── Storage (依赖 CommonCore)
│   └── Clipboard (依赖 Storage)
│       └── SmartClipboard (依赖 Clipboard)
├── WindowService (依赖 CommonCore)
└── GlobalHook (无依赖)
```

## 初始化流程

### 应用启动流程

1. **错误处理器设置**: 设置全局异常捕获
2. **应用管理器创建**: 创建单例应用管理器
3. **配置管理器初始化**: 加载配置文件和环境变量
4. **日志管理器初始化**: 设置日志输出和格式
5. **服务管理器初始化**: 注册和初始化所有服务
6. **窗口管理器初始化**: 创建默认窗口
7. **快捷键注册**: 注册全局快捷键
8. **应用就绪**: 发出就绪事件

### 服务初始化顺序

服务管理器使用拓扑排序确保按正确的依赖顺序初始化服务：

```
1. CommonCore
2. Database, Storage, WindowService, GlobalHook (并行)
3. Clipboard (依赖 Storage)
4. SmartClipboard (依赖 Clipboard)
```

## 关闭流程

### 应用关闭流程

1. **接收关闭信号**: 处理 before-quit 事件
2. **注销快捷键**: 清理全局快捷键
3. **窗口管理器销毁**: 关闭所有窗口
4. **服务管理器销毁**: 按依赖关系逆序停止服务
5. **日志管理器销毁**: 刷新日志缓冲区
6. **配置管理器销毁**: 保存配置更改
7. **应用退出**: 清理资源并退出

## 配置管理

### 配置文件结构

```typescript
interface AppConfig {
  app: {
    name: string
    version: string
    isDevelopment: boolean
    dataPath: string
    logLevel: 'debug' | 'info' | 'warn' | 'error'
  }
  window: {
    main: { width: number, height: number, show: boolean }
    search: { width: number, height: number, show: boolean }
    toolbar: { width: number, height: number, show: boolean }
  }
  shortcuts: { [action: string]: string }
  services: {
    database: { type: string, path?: string }
    ai: { provider: string, apiKey?: string }
  }
  features: {
    clipboard: { enabled: boolean, maxHistory: number }
    toolbar: { enabled: boolean, autoHide: boolean }
  }
}
```

### 配置优先级

1. **运行时配置** (最高优先级)
2. **环境变量配置**
3. **配置文件**
4. **默认配置** (最低优先级)

## 日志管理

### 日志级别

- **DEBUG**: 调试信息
- **INFO**: 一般信息
- **WARN**: 警告信息
- **ERROR**: 错误信息

### 日志输出

- **控制台输出**: 开发模式下启用
- **文件输出**: 保存到 `userData/logs/` 目录
- **日志轮转**: 按大小和数量限制自动清理

## 错误处理

### 错误分类

- **SYSTEM**: 系统级错误
- **NETWORK**: 网络相关错误
- **VALIDATION**: 验证错误
- **BUSINESS**: 业务逻辑错误
- **UNKNOWN**: 未知错误

### 错误严重程度

- **CRITICAL**: 致命错误，需要退出应用
- **HIGH**: 高级错误，影响功能
- **MEDIUM**: 中级错误，可以恢复
- **LOW**: 低级错误，仅记录日志

## 使用示例

### 获取服务实例

```typescript
const appManager = ApplicationManager.getInstance()
const serviceManager = appManager.getServiceManager()
const databaseService = serviceManager?.getService<DatabaseServiceAdapter>('database')
```

### 记录日志

```typescript
const logManager = appManager.getLogManager()
logManager?.info('MyComponent', '这是一条信息日志')
logManager?.error('MyComponent', '这是一条错误日志', error)
```

### 处理错误

```typescript
const errorHandler = ErrorHandler.getInstance()
await errorHandler.handleError(error, {
  showDialog: true,
  logError: true,
  notifyUser: true
})
```

## 扩展指南

### 添加新服务

1. 创建服务类实现 `IService` 接口
2. 在 `service-adapters.ts` 中创建适配器
3. 在 `ApplicationManager.registerServices()` 中注册服务
4. 配置服务依赖关系

### 添加新窗口类型

1. 在 `WindowType` 枚举中添加新类型
2. 在 `WindowManager.createWindowInstance()` 中添加创建逻辑
3. 实现窗口类，继承 `CommonWindow`

### 添加新配置

1. 在 `AppConfig` 接口中添加配置项
2. 在 `ConfigManager.loadDefaultConfig()` 中设置默认值
3. 添加环境变量映射（如需要）

## 最佳实践

1. **单一职责**: 每个组件只负责一个明确的功能
2. **依赖注入**: 通过管理器获取依赖，避免直接实例化
3. **错误处理**: 使用统一的错误处理机制
4. **日志记录**: 记录关键操作和错误信息
5. **配置管理**: 使用配置管理器而不是硬编码
6. **生命周期**: 正确实现初始化和清理逻辑
7. **异步处理**: 正确处理异步操作和错误

## 迁移指南

### 从旧架构迁移

如果你需要从旧的主进程架构迁移到新架构，请按以下步骤操作：

1. **备份现有代码**: 确保备份当前的 `src/main/index.ts` 文件
2. **更新入口文件**: 使用新的 `index.ts` 文件
3. **迁移服务**: 将现有服务包装为服务适配器
4. **更新配置**: 将硬编码配置移动到配置文件
5. **测试功能**: 确保所有功能正常工作

### 兼容性说明

- 新架构与现有的渲染进程代码完全兼容
- IPC 通信接口保持不变
- 现有的服务功能通过适配器保持兼容

## 故障排除

### 常见问题

1. **服务初始化失败**
   - 检查服务依赖关系是否正确
   - 查看日志文件获取详细错误信息
   - 确保所有必需的配置项已设置

2. **窗口无法显示**
   - 检查窗口管理器是否正确初始化
   - 验证窗口配置是否正确
   - 查看控制台错误信息

3. **配置加载失败**
   - 检查配置文件格式是否正确
   - 验证文件权限
   - 查看配置管理器日志

### 调试技巧

1. **启用调试日志**: 设置 `app.logLevel` 为 `debug`
2. **查看错误历史**: 使用 `ErrorHandler.getErrorHistory()`
3. **检查服务状态**: 使用 `ServiceManager.getAllServiceStates()`
4. **监听事件**: 监听各管理器的事件来了解状态变化

## 性能优化

1. **延迟加载**: 非关键服务可以延迟初始化
2. **资源清理**: 确保正确清理不再使用的资源
3. **日志级别**: 生产环境使用较高的日志级别
4. **配置缓存**: 避免频繁读取配置文件

## 安全考虑

1. **配置验证**: 验证所有外部配置输入
2. **错误信息**: 避免在错误信息中泄露敏感信息
3. **文件权限**: 确保日志和配置文件的适当权限
4. **依赖检查**: 定期检查和更新依赖项
