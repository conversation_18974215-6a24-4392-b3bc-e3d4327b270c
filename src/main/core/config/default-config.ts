/**
 * 默认配置
 * 提供应用的默认配置值和配置验证
 */

import { app } from 'electron'
import { join } from 'path'
import type { AppConfig } from './config-schema'

/**
 * 获取默认配置
 */
export function getDefaultConfig(): AppConfig {
  const userDataPath = app.getPath('userData')
  
  return {
    app: {
      name: 'Smart Toolbox',
      version: '1.0.0',
      isDevelopment: process.env.NODE_ENV === 'development',
      dataPath: userDataPath,
      logLevel: 'info',
      autoStart: false,
      minimizeToTray: true,
      closeToTray: true
    },
    
    window: {
      main: {
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        show: true,
        center: true,
        resizable: true,
        maximizable: true,
        minimizable: true,
        closable: true,
        alwaysOnTop: false,
        skipTaskbar: false,
        frame: true,
        transparent: false,
        opacity: 1.0,
        backgroundColor: '#ffffff',
        titleBarStyle: 'default',
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          enableRemoteModule: false,
          webSecurity: true,
          allowRunningInsecureContent: false,
          experimentalFeatures: false
        }
      },
      
      search: {
        width: 600,
        height: 400,
        minWidth: 400,
        minHeight: 300,
        show: false,
        center: true,
        resizable: true,
        maximizable: false,
        minimizable: false,
        closable: true,
        alwaysOnTop: true,
        skipTaskbar: true,
        frame: false,
        transparent: true,
        opacity: 0.95,
        backgroundColor: '#00000000',
        focusable: true,
        movable: true
      },
      
      toolbar: {
        width: 300,
        height: 50,
        show: false,
        resizable: false,
        maximizable: false,
        minimizable: false,
        closable: false,
        alwaysOnTop: true,
        skipTaskbar: true,
        frame: false,
        transparent: true,
        opacity: 0.9,
        backgroundColor: '#00000000',
        focusable: false,
        movable: true,
        autoHide: true,
        hideTimeout: 3000
      }
    },
    
    shortcuts: {
      'show-toolbar': 'Ctrl+Space',
      'show-search': 'Ctrl+Shift+F',
      'show-main': 'Ctrl+Shift+M',
      'toggle-clipboard': 'Ctrl+Shift+V',
      'quit-app': 'Ctrl+Q'
    },
    
    services: {
      database: {
        type: 'sqlite',
        path: join(userDataPath, 'database.sqlite'),
        pool: {
          min: 1,
          max: 10,
          idle: 30000
        },
        backup: {
          enabled: true,
          interval: 3600000, // 1小时
          maxBackups: 10,
          path: join(userDataPath, 'backups')
        }
      },
      
      storage: {
        rootPath: join(userDataPath, 'storage'),
        cache: {
          enabled: true,
          maxSize: 1000,
          ttl: 3600000 // 1小时
        },
        backup: {
          enabled: false,
          interval: 3600000,
          maxBackups: 5
        },
        encryption: {
          enabled: false,
          algorithm: 'aes-256-gcm'
        }
      },
      
      clipboard: {
        enabled: true,
        maxHistorySize: 100,
        enableHistory: true,
        watchInterval: 1000,
        autoWatch: true,
        filters: {
          maxTextLength: 10000,
          allowedFileTypes: ['*'],
          filterDuplicates: true
        }
      },
      

      
      window: {
        enabled: true,
        limits: {
          maxWindows: 50,
          minWidth: 300,
          minHeight: 200,
          maxWidth: 4000,
          maxHeight: 3000
        },
        management: {
          autoCleanup: true,
          trackFocus: true,
          saveState: false
        }
      }
    },
    
    features: {
      clipboard: {
        enabled: true,
        smartPaste: true,
        historyLimit: 100,
        autoClean: true,
        cleanInterval: 86400000 // 24小时
      },
      
      search: {
        enabled: true,
        fuzzySearch: true,
        maxResults: 50,
        searchDelay: 300,
        cacheResults: true
      },
      
      toolbar: {
        enabled: true,
        autoShow: false,
        showDelay: 100,
        hideDelay: 3000,
        position: 'top',
        size: 'medium'
      },
      
      ai: {
        enabled: false,
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2048,
        timeout: 30000
      }
    },
    
    ui: {
      theme: 'auto',
      language: 'zh-CN',
      fontSize: 14,
      fontFamily: 'system-ui, -apple-system, sans-serif',
      animations: true,
      transparency: true,
      accentColor: '#007acc',
      borderRadius: 6,
      spacing: 8
    },
    
    security: {
      enableCSP: true,
      allowedOrigins: ['file://', 'app://'],
      enableCORS: false,
      maxRequestSize: 10 * 1024 * 1024, // 10MB
      rateLimit: {
        enabled: true,
        windowMs: 60000, // 1分钟
        maxRequests: 100
      }
    },
    
    performance: {
      enableGPU: true,
      enableWebGL: true,
      maxMemoryUsage: 512 * 1024 * 1024, // 512MB
      gcInterval: 60000, // 1分钟
      preloadScripts: true,
      lazyLoading: true
    },
    
    logging: {
      level: 'info',
      enableConsole: true,
      enableFile: true,
      filePath: join(userDataPath, 'logs'),
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      format: 'text',
      includeTimestamp: true,
      includeLevel: true,
      includeCategory: true
    },
    
    updates: {
      enabled: true,
      checkInterval: 86400000, // 24小时
      autoDownload: false,
      autoInstall: false,
      channel: 'stable',
      server: 'https://api.smart-toolbox.com/updates'
    },
    
    telemetry: {
      enabled: false,
      anonymize: true,
      endpoint: 'https://api.smart-toolbox.com/telemetry',
      interval: 3600000, // 1小时
      includeSystemInfo: false,
      includeUsageStats: false
    }
  }
}

/**
 * 验证配置
 */
export function validateConfig(config: any): void {
  if (!config || typeof config !== 'object') {
    throw new Error('配置必须是一个对象')
  }

  // 验证必需的配置项
  const requiredPaths = [
    'app.name',
    'app.version',
    'window.main.width',
    'window.main.height',
    'services.database.type',
    'services.storage.rootPath'
  ]

  for (const path of requiredPaths) {
    if (!hasPath(config, path)) {
      throw new Error(`缺少必需的配置项: ${path}`)
    }
  }

  // 验证数据类型
  validateTypes(config)

  // 验证数值范围
  validateRanges(config)
}

/**
 * 检查对象是否有指定路径
 */
function hasPath(obj: any, path: string): boolean {
  const keys = path.split('.')
  let current = obj

  for (const key of keys) {
    if (!current || typeof current !== 'object' || !(key in current)) {
      return false
    }
    current = current[key]
  }

  return current !== undefined && current !== null
}

/**
 * 验证数据类型
 */
function validateTypes(config: any): void {
  const typeChecks = [
    { path: 'app.name', type: 'string' },
    { path: 'app.version', type: 'string' },
    { path: 'app.isDevelopment', type: 'boolean' },
    { path: 'window.main.width', type: 'number' },
    { path: 'window.main.height', type: 'number' },
    { path: 'services.database.type', type: 'string' },
    { path: 'services.storage.rootPath', type: 'string' }
  ]

  for (const check of typeChecks) {
    const value = getValueByPath(config, check.path)
    if (value !== undefined && typeof value !== check.type) {
      throw new Error(`配置项 ${check.path} 必须是 ${check.type} 类型`)
    }
  }
}

/**
 * 验证数值范围
 */
function validateRanges(config: any): void {
  const rangeChecks = [
    { path: 'window.main.width', min: 300, max: 4000 },
    { path: 'window.main.height', min: 200, max: 3000 },
    { path: 'window.search.width', min: 200, max: 2000 },
    { path: 'window.search.height', min: 150, max: 1500 },
    { path: 'services.clipboard.maxHistorySize', min: 1, max: 1000 },
    { path: 'ui.fontSize', min: 8, max: 32 }
  ]

  for (const check of rangeChecks) {
    const value = getValueByPath(config, check.path)
    if (typeof value === 'number') {
      if (value < check.min || value > check.max) {
        throw new Error(`配置项 ${check.path} 必须在 ${check.min} 到 ${check.max} 之间`)
      }
    }
  }
}

/**
 * 根据路径获取值
 */
function getValueByPath(obj: any, path: string): any {
  const keys = path.split('.')
  let current = obj

  for (const key of keys) {
    if (!current || typeof current !== 'object') {
      return undefined
    }
    current = current[key]
  }

  return current
}

export default getDefaultConfig
