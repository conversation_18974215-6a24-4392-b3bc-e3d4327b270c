/**
 * 配置管理器
 * 负责应用配置的加载、保存、验证和管理
 */

import { app } from 'electron'
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs'
import { join, dirname } from 'path'
import { EventEmitter } from 'events'
import type { AppConfig } from './config-schema'
import { validateConfig, getDefaultConfig } from './default-config'

/**
 * 配置管理器类
 */
export class ConfigManager extends EventEmitter {
  private config: AppConfig
  private configPath: string
  private isInitialized = false
  private watchTimer?: NodeJS.Timeout

  constructor() {
    super()
    this.configPath = join(app.getPath('userData'), 'config.json')
    this.config = getDefaultConfig()
  }

  /**
   * 初始化配置管理器
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('[ConfigManager] 配置管理器已经初始化')
      return
    }

    try {
      // 确保配置目录存在
      this.ensureConfigDirectory()

      // 加载配置
      await this.loadConfig()

      // 验证配置
      this.validateCurrentConfig()

      // 启动配置监听
      this.startConfigWatch()

      this.isInitialized = true
      this.emit('initialized')
      console.log('[ConfigManager] 配置管理器初始化完成')

    } catch (error) {
      console.error('[ConfigManager] 配置管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * 获取配置值
   */
  public get<T = any>(key: string, defaultValue?: T): T {
    const keys = key.split('.')
    let value: any = this.config

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return defaultValue as T
      }
    }

    return value as T
  }

  /**
   * 设置配置值
   */
  public set(key: string, value: any): void {
    const keys = key.split('.')
    const lastKey = keys.pop()
    
    if (!lastKey) {
      throw new Error('无效的配置键')
    }

    let target: any = this.config
    for (const k of keys) {
      if (!target[k] || typeof target[k] !== 'object') {
        target[k] = {}
      }
      target = target[k]
    }

    const oldValue = target[lastKey]
    target[lastKey] = value

    // 触发变更事件
    this.emit('configChanged', { key, oldValue, newValue: value })

    // 自动保存
    this.saveConfig()
  }

  /**
   * 检查配置键是否存在
   */
  public has(key: string): boolean {
    const keys = key.split('.')
    let value: any = this.config

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return false
      }
    }

    return true
  }

  /**
   * 删除配置键
   */
  public delete(key: string): void {
    const keys = key.split('.')
    const lastKey = keys.pop()
    
    if (!lastKey) {
      throw new Error('无效的配置键')
    }

    let target: any = this.config
    for (const k of keys) {
      if (!target[k] || typeof target[k] !== 'object') {
        return // 键不存在
      }
      target = target[k]
    }

    if (lastKey in target) {
      const oldValue = target[lastKey]
      delete target[lastKey]
      
      // 触发变更事件
      this.emit('configChanged', { key, oldValue, newValue: undefined })
      
      // 自动保存
      this.saveConfig()
    }
  }

  /**
   * 获取完整配置
   */
  public getAll(): AppConfig {
    return JSON.parse(JSON.stringify(this.config))
  }

  /**
   * 重置配置为默认值
   */
  public reset(): void {
    const oldConfig = this.config
    this.config = getDefaultConfig()
    
    this.emit('configReset', { oldConfig, newConfig: this.config })
    this.saveConfig()
  }

  /**
   * 重新加载配置
   */
  public async reload(): Promise<void> {
    await this.loadConfig()
    this.validateCurrentConfig()
    this.emit('configReloaded')
  }

  /**
   * 手动保存配置
   */
  public save(): void {
    this.saveConfig()
  }

  /**
   * 销毁配置管理器
   */
  public async destroy(): Promise<void> {
    this.stopConfigWatch()
    this.saveConfig()
    this.removeAllListeners()
    this.isInitialized = false
    console.log('[ConfigManager] 配置管理器销毁完成')
  }

  /**
   * 确保配置目录存在
   */
  private ensureConfigDirectory(): void {
    const configDir = dirname(this.configPath)
    if (!existsSync(configDir)) {
      mkdirSync(configDir, { recursive: true })
    }
  }

  /**
   * 加载配置文件
   */
  private async loadConfig(): Promise<void> {
    try {
      if (existsSync(this.configPath)) {
        const configData = readFileSync(this.configPath, 'utf-8')
        const loadedConfig = JSON.parse(configData)
        
        // 合并默认配置和加载的配置
        this.config = this.mergeConfig(getDefaultConfig(), loadedConfig)
        
        console.log('[ConfigManager] 配置文件加载成功')
      } else {
        // 配置文件不存在，使用默认配置并保存
        this.config = getDefaultConfig()
        this.saveConfig()
        console.log('[ConfigManager] 使用默认配置并创建配置文件')
      }

      // 加载环境变量配置
      this.loadEnvironmentConfig()

    } catch (error) {
      console.error('[ConfigManager] 加载配置文件失败:', error)
      console.log('[ConfigManager] 使用默认配置')
      this.config = getDefaultConfig()
    }
  }

  /**
   * 保存配置文件
   */
  private saveConfig(): void {
    try {
      const configData = JSON.stringify(this.config, null, 2)
      writeFileSync(this.configPath, configData, 'utf-8')
      console.log('[ConfigManager] 配置文件保存成功')
    } catch (error) {
      console.error('[ConfigManager] 保存配置文件失败:', error)
    }
  }

  /**
   * 加载环境变量配置
   */
  private loadEnvironmentConfig(): void {
    const envMappings = {
      'SMART_TOOLBOX_LOG_LEVEL': 'app.logLevel',
      'SMART_TOOLBOX_DEV_MODE': 'app.isDevelopment',
      'SMART_TOOLBOX_DATA_PATH': 'app.dataPath'
    }

    for (const [envKey, configKey] of Object.entries(envMappings)) {
      const envValue = process.env[envKey]
      if (envValue !== undefined) {
        let value: any = envValue
        
        // 类型转换
        if (envValue === 'true') value = true
        else if (envValue === 'false') value = false
        else if (!isNaN(Number(envValue))) value = Number(envValue)
        
        this.set(configKey, value)
        console.log(`[ConfigManager] 从环境变量加载配置: ${configKey} = ${value}`)
      }
    }
  }

  /**
   * 合并配置对象
   */
  private mergeConfig(defaultConfig: any, userConfig: any): any {
    const result = { ...defaultConfig }
    
    for (const key in userConfig) {
      if (userConfig.hasOwnProperty(key)) {
        if (typeof userConfig[key] === 'object' && userConfig[key] !== null && !Array.isArray(userConfig[key])) {
          result[key] = this.mergeConfig(result[key] || {}, userConfig[key])
        } else {
          result[key] = userConfig[key]
        }
      }
    }
    
    return result
  }

  /**
   * 验证当前配置
   */
  private validateCurrentConfig(): void {
    try {
      validateConfig(this.config)
      console.log('[ConfigManager] 配置验证通过')
    } catch (error) {
      console.error('[ConfigManager] 配置验证失败:', error)
      console.log('[ConfigManager] 重置为默认配置')
      this.config = getDefaultConfig()
      this.saveConfig()
    }
  }

  /**
   * 启动配置文件监听
   */
  private startConfigWatch(): void {
    // 每5秒检查一次配置文件是否有外部修改
    this.watchTimer = setInterval(() => {
      this.checkConfigFileChanges()
    }, 5000)
  }

  /**
   * 停止配置文件监听
   */
  private stopConfigWatch(): void {
    if (this.watchTimer) {
      clearInterval(this.watchTimer)
      this.watchTimer = undefined
    }
  }

  /**
   * 检查配置文件外部修改
   */
  private checkConfigFileChanges(): void {
    try {
      if (existsSync(this.configPath)) {
        const configData = readFileSync(this.configPath, 'utf-8')
        const fileConfig = JSON.parse(configData)
        
        // 简单比较配置是否有变化
        if (JSON.stringify(fileConfig) !== JSON.stringify(this.config)) {
          console.log('[ConfigManager] 检测到配置文件外部修改，重新加载')
          this.reload()
        }
      }
    } catch (error) {
      // 忽略检查错误
    }
  }
}

export default ConfigManager
