/**
 * 配置模式定义
 * 定义应用的完整配置结构和类型
 */

/**
 * 应用配置接口
 */
export interface AppConfig {
  app: {
    name: string
    version: string
    isDevelopment: boolean
    dataPath: string
    logLevel: 'debug' | 'info' | 'warn' | 'error'
    autoStart: boolean
    minimizeToTray: boolean
    closeToTray: boolean
  }
  
  window: {
    main: {
      width: number
      height: number
      minWidth: number
      minHeight: number
      show: boolean
      center: boolean
      resizable: boolean
      maximizable: boolean
      minimizable: boolean
      closable: boolean
      alwaysOnTop: boolean
      skipTaskbar: boolean
      frame: boolean
      transparent: boolean
      opacity: number
      backgroundColor: string
      titleBarStyle: 'default' | 'hidden' | 'hiddenInset' | 'customButtonsOnHover'
      webPreferences: {
        nodeIntegration: boolean
        contextIsolation: boolean
        enableRemoteModule: boolean
        webSecurity: boolean
        allowRunningInsecureContent: boolean
        experimentalFeatures: boolean
      }
    }
    
    search: {
      width: number
      height: number
      minWidth: number
      minHeight: number
      show: boolean
      center: boolean
      resizable: boolean
      maximizable: boolean
      minimizable: boolean
      closable: boolean
      alwaysOnTop: boolean
      skipTaskbar: boolean
      frame: boolean
      transparent: boolean
      opacity: number
      backgroundColor: string
      focusable: boolean
      movable: boolean
    }
    
    toolbar: {
      width: number
      height: number
      show: boolean
      resizable: boolean
      maximizable: boolean
      minimizable: boolean
      closable: boolean
      alwaysOnTop: boolean
      skipTaskbar: boolean
      frame: boolean
      transparent: boolean
      opacity: number
      backgroundColor: string
      focusable: boolean
      movable: boolean
      autoHide: boolean
      hideTimeout: number
    }
  }
  
  shortcuts: {
    [action: string]: string
  }
  
  services: {
    database: {
      type: 'sqlite' | 'mysql' | 'postgresql'
      path?: string
      host?: string
      port?: number
      username?: string
      password?: string
      database?: string
      pool: {
        min: number
        max: number
        idle: number
      }
      backup: {
        enabled: boolean
        interval: number
        maxBackups: number
        path: string
      }
    }
    
    storage: {
      rootPath: string
      cache: {
        enabled: boolean
        maxSize: number
        ttl: number
      }
      backup: {
        enabled: boolean
        interval: number
        maxBackups: number
      }
      encryption: {
        enabled: boolean
        algorithm: string
      }
    }
    
    clipboard: {
      enabled: boolean
      maxHistorySize: number
      enableHistory: boolean
      watchInterval: number
      autoWatch: boolean
      filters: {
        maxTextLength: number
        allowedFileTypes: string[]
        filterDuplicates: boolean
      }
    }
    

    
    window: {
      enabled: boolean
      limits: {
        maxWindows: number
        minWidth: number
        minHeight: number
        maxWidth: number
        maxHeight: number
      }
      management: {
        autoCleanup: boolean
        trackFocus: boolean
        saveState: boolean
      }
    }
  }
  
  features: {
    clipboard: {
      enabled: boolean
      smartPaste: boolean
      historyLimit: number
      autoClean: boolean
      cleanInterval: number
    }
    
    search: {
      enabled: boolean
      fuzzySearch: boolean
      maxResults: number
      searchDelay: number
      cacheResults: boolean
    }
    
    toolbar: {
      enabled: boolean
      autoShow: boolean
      showDelay: number
      hideDelay: number
      position: 'top' | 'bottom' | 'left' | 'right'
      size: 'small' | 'medium' | 'large'
    }
    
    ai: {
      enabled: boolean
      provider: string
      apiKey?: string
      model: string
      temperature: number
      maxTokens: number
      timeout: number
    }
  }
  
  ui: {
    theme: 'light' | 'dark' | 'auto'
    language: string
    fontSize: number
    fontFamily: string
    animations: boolean
    transparency: boolean
    accentColor: string
    borderRadius: number
    spacing: number
  }
  
  security: {
    enableCSP: boolean
    allowedOrigins: string[]
    enableCORS: boolean
    maxRequestSize: number
    rateLimit: {
      enabled: boolean
      windowMs: number
      maxRequests: number
    }
  }
  
  performance: {
    enableGPU: boolean
    enableWebGL: boolean
    maxMemoryUsage: number
    gcInterval: number
    preloadScripts: boolean
    lazyLoading: boolean
  }
  
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error'
    enableConsole: boolean
    enableFile: boolean
    filePath: string
    maxFileSize: number
    maxFiles: number
    format: 'json' | 'text'
    includeTimestamp: boolean
    includeLevel: boolean
    includeCategory: boolean
  }
  
  updates: {
    enabled: boolean
    checkInterval: number
    autoDownload: boolean
    autoInstall: boolean
    channel: 'stable' | 'beta' | 'alpha'
    server: string
  }
  
  telemetry: {
    enabled: boolean
    anonymize: boolean
    endpoint: string
    interval: number
    includeSystemInfo: boolean
    includeUsageStats: boolean
  }
}

/**
 * 窗口配置接口
 */
export interface WindowConfig {
  width: number
  height: number
  minWidth?: number
  minHeight?: number
  maxWidth?: number
  maxHeight?: number
  x?: number
  y?: number
  show?: boolean
  center?: boolean
  resizable?: boolean
  maximizable?: boolean
  minimizable?: boolean
  closable?: boolean
  alwaysOnTop?: boolean
  skipTaskbar?: boolean
  frame?: boolean
  transparent?: boolean
  opacity?: number
  backgroundColor?: string
  titleBarStyle?: 'default' | 'hidden' | 'hiddenInset' | 'customButtonsOnHover'
  webPreferences?: {
    nodeIntegration?: boolean
    contextIsolation?: boolean
    enableRemoteModule?: boolean
    webSecurity?: boolean
    allowRunningInsecureContent?: boolean
    experimentalFeatures?: boolean
  }
}

/**
 * 服务配置接口
 */
export interface ServiceConfig {
  name: string
  version?: string
  enabled?: boolean
  dependencies?: string[]
  config?: Record<string, any>
  healthCheckInterval?: number
  retry?: {
    maxAttempts: number
    delay: number
    backoff?: 'linear' | 'exponential'
  }
}

/**
 * 快捷键配置接口
 */
export interface ShortcutConfig {
  [action: string]: string
}

/**
 * 主题配置接口
 */
export interface ThemeConfig {
  name: string
  colors: {
    primary: string
    secondary: string
    background: string
    surface: string
    text: string
    textSecondary: string
    border: string
    accent: string
    success: string
    warning: string
    error: string
    info: string
  }
  fonts: {
    primary: string
    secondary: string
    monospace: string
  }
  spacing: {
    xs: number
    sm: number
    md: number
    lg: number
    xl: number
  }
  borderRadius: {
    sm: number
    md: number
    lg: number
  }
  shadows: {
    sm: string
    md: string
    lg: string
  }
}

export default AppConfig
