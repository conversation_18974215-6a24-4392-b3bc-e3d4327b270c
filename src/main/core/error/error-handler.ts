/**
 * 错误处理器
 * 负责应用的全局错误处理、分类、记录和恢复
 */

import { dialog } from 'electron'
import { EventEmitter } from 'events'
import type { LogManager } from '../logging/log-manager'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  SYSTEM = 'system',
  NETWORK = 'network',
  VALIDATION = 'validation',
  BUSINESS = 'business',
  UNKNOWN = 'unknown'
}

/**
 * 错误严重程度枚举
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 应用错误接口
 */
export interface AppError extends Error {
  id: string
  type: ErrorType
  severity: ErrorSeverity
  timestamp: Date
  details?: string
  context?: Record<string, any>
  recoverable?: boolean
  handled?: boolean
}

/**
 * 错误处理选项接口
 */
export interface ErrorHandleOptions {
  showDialog?: boolean
  logError?: boolean
  notifyUser?: boolean
  attemptRecovery?: boolean
}

/**
 * 错误统计接口
 */
export interface ErrorStats {
  total: number
  byType: Record<ErrorType, number>
  bySeverity: Record<ErrorSeverity, number>
  recent: AppError[]
}

/**
 * 错误处理器类
 */
export class ErrorHandler extends EventEmitter {
  private static instance: ErrorHandler | null = null
  private logManager?: LogManager
  private errorHistory: AppError[] = []
  private errorStats: ErrorStats
  private maxHistorySize = 1000
  private isInitialized = false

  /**
   * 私有构造函数，确保单例模式
   */
  private constructor() {
    super()
    
    this.errorStats = {
      total: 0,
      byType: {
        [ErrorType.SYSTEM]: 0,
        [ErrorType.NETWORK]: 0,
        [ErrorType.VALIDATION]: 0,
        [ErrorType.BUSINESS]: 0,
        [ErrorType.UNKNOWN]: 0
      },
      bySeverity: {
        [ErrorSeverity.LOW]: 0,
        [ErrorSeverity.MEDIUM]: 0,
        [ErrorSeverity.HIGH]: 0,
        [ErrorSeverity.CRITICAL]: 0
      },
      recent: []
    }
  }

  /**
   * 获取错误处理器实例（单例）
   */
  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 设置日志管理器
   */
  public setLogManager(logManager: LogManager): void {
    this.logManager = logManager
  }

  /**
   * 设置全局错误处理器
   */
  public setupGlobalErrorHandlers(): void {
    if (this.isInitialized) {
      return
    }

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      this.handleError(error, {
        showDialog: true,
        logError: true,
        notifyUser: true
      }).catch(console.error)
    })

    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      const error = reason instanceof Error ? reason : new Error(String(reason))
      this.handleError(error, {
        showDialog: false,
        logError: true,
        notifyUser: false
      }).catch(console.error)
    })

    this.isInitialized = true
    this.log('info', '全局错误处理器设置完成')
  }

  /**
   * 处理错误
   */
  public async handleError(error: Error | AppError, options: ErrorHandleOptions = {}): Promise<void> {
    try {
      // 转换为应用错误
      const appError = this.normalizeError(error)
      
      // 记录错误
      this.recordError(appError)
      
      // 更新统计
      this.updateStats(appError)
      
      // 日志记录
      if (options.logError !== false) {
        this.logError(appError)
      }
      
      // 用户通知
      if (options.notifyUser) {
        await this.notifyUser(appError)
      }
      
      // 显示对话框
      if (options.showDialog) {
        await this.showErrorDialog(appError)
      }
      
      // 尝试恢复
      if (options.attemptRecovery && appError.recoverable) {
        await this.attemptRecovery(appError)
      }
      
      // 发出事件
      this.emit('error', appError)
      
      // 标记为已处理
      appError.handled = true

    } catch (handlingError) {
      console.error('[ErrorHandler] 处理错误时发生异常:', handlingError)
    }
  }

  /**
   * 创建应用错误
   */
  public createError(
    type: ErrorType,
    severity: ErrorSeverity,
    message: string,
    details?: string,
    context?: Record<string, any>
  ): AppError {
    const error = new Error(message) as AppError
    error.id = this.generateErrorId()
    error.type = type
    error.severity = severity
    error.timestamp = new Date()
    error.details = details
    error.context = context
    error.recoverable = severity !== ErrorSeverity.CRITICAL
    error.handled = false
    
    return error
  }

  /**
   * 获取错误历史
   */
  public getErrorHistory(): AppError[] {
    return [...this.errorHistory]
  }

  /**
   * 获取错误统计
   */
  public getErrorStats(): ErrorStats {
    return {
      ...this.errorStats,
      recent: [...this.errorStats.recent]
    }
  }

  /**
   * 清空错误历史
   */
  public clearErrorHistory(): void {
    this.errorHistory = []
    this.errorStats.recent = []
    this.log('info', '错误历史已清空')
  }

  /**
   * 标准化错误对象
   */
  private normalizeError(error: Error | AppError): AppError {
    if (this.isAppError(error)) {
      return error
    }

    // 转换为应用错误
    const appError = error as AppError
    appError.id = this.generateErrorId()
    appError.type = this.classifyError(error)
    appError.severity = this.determineSeverity(error)
    appError.timestamp = new Date()
    appError.recoverable = appError.severity !== ErrorSeverity.CRITICAL
    appError.handled = false

    return appError
  }

  /**
   * 检查是否为应用错误
   */
  private isAppError(error: any): error is AppError {
    return error && typeof error === 'object' && 'id' in error && 'type' in error
  }

  /**
   * 分类错误
   */
  private classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase()
    const stack = error.stack?.toLowerCase() || ''

    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return ErrorType.NETWORK
    }

    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return ErrorType.VALIDATION
    }

    if (stack.includes('business') || stack.includes('service')) {
      return ErrorType.BUSINESS
    }

    if (message.includes('system') || message.includes('file') || message.includes('permission')) {
      return ErrorType.SYSTEM
    }

    return ErrorType.UNKNOWN
  }

  /**
   * 确定错误严重程度
   */
  private determineSeverity(error: Error): ErrorSeverity {
    const message = error.message.toLowerCase()

    if (message.includes('critical') || message.includes('fatal') || message.includes('crash')) {
      return ErrorSeverity.CRITICAL
    }

    if (message.includes('error') || message.includes('failed') || message.includes('exception')) {
      return ErrorSeverity.HIGH
    }

    if (message.includes('warning') || message.includes('deprecated')) {
      return ErrorSeverity.MEDIUM
    }

    return ErrorSeverity.LOW
  }

  /**
   * 记录错误
   */
  private recordError(error: AppError): void {
    // 添加到历史记录
    this.errorHistory.unshift(error)
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize)
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(error: AppError): void {
    this.errorStats.total++
    this.errorStats.byType[error.type]++
    this.errorStats.bySeverity[error.severity]++
    
    // 更新最近错误列表
    this.errorStats.recent.unshift(error)
    if (this.errorStats.recent.length > 10) {
      this.errorStats.recent = this.errorStats.recent.slice(0, 10)
    }
  }

  /**
   * 记录错误日志
   */
  private logError(error: AppError): void {
    const logMessage = `[${error.type}][${error.severity}] ${error.message}`
    
    if (this.logManager) {
      this.logManager.error('ErrorHandler', logMessage, error)
    } else {
      console.error(`[ErrorHandler] ${logMessage}`, error)
    }
  }

  /**
   * 通知用户
   */
  private async notifyUser(error: AppError): Promise<void> {
    // 这里可以实现用户通知逻辑
    // 例如：系统通知、应用内通知等
    this.log('info', `用户通知: ${error.message}`)
  }

  /**
   * 显示错误对话框
   */
  private async showErrorDialog(error: AppError): Promise<void> {
    try {
      const options = {
        type: 'error' as const,
        title: '应用错误',
        message: error.message,
        detail: error.details || `错误类型: ${error.type}\n严重程度: ${error.severity}\n时间: ${error.timestamp.toLocaleString()}`,
        buttons: ['确定', '查看详情']
      }

      const result = await dialog.showMessageBox(options)
      
      if (result.response === 1) {
        // 显示详细信息
        await dialog.showMessageBox({
          type: 'info',
          title: '错误详情',
          message: `错误ID: ${error.id}`,
          detail: JSON.stringify({
            type: error.type,
            severity: error.severity,
            timestamp: error.timestamp,
            stack: error.stack,
            context: error.context
          }, null, 2)
        })
      }

    } catch (dialogError) {
      console.error('[ErrorHandler] 显示错误对话框失败:', dialogError)
    }
  }

  /**
   * 尝试错误恢复
   */
  private async attemptRecovery(error: AppError): Promise<void> {
    this.log('info', `尝试恢复错误: ${error.id}`)
    
    // 这里可以实现具体的恢复逻辑
    // 例如：重试操作、重置状态、重新连接等
    
    this.emit('recovery', error)
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 日志记录
   */
  private log(level: 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    if (this.logManager) {
      const logLevel = level === 'info' ? 1 : level === 'warn' ? 2 : 3
      this.logManager.log(logLevel, 'ErrorHandler', message, ...args)
    } else {
      console[level](`[ErrorHandler] ${message}`, ...args)
    }
  }
}

export default ErrorHandler
