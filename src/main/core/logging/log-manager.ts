/**
 * 日志管理器
 * 负责应用的日志记录、格式化、输出和管理
 */

import { app } from 'electron'
import { writeFileSync, existsSync, mkdirSync, readdirSync, statSync, unlinkSync } from 'fs'
import { join, dirname } from 'path'
import { EventEmitter } from 'events'
import { promisify } from 'util'

/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

/**
 * 日志格式枚举
 */
export enum LogFormat {
  TEXT = 'text',
  JSON = 'json'
}

/**
 * 日志配置接口
 */
export interface LogConfig {
  level?: LogLevel
  enableConsole?: boolean
  enableFile?: boolean
  filePath?: string
  maxFileSize?: number
  maxFiles?: number
  format?: LogFormat
  includeTimestamp?: boolean
  includeLevel?: boolean
  includeCategory?: boolean
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  timestamp: Date
  level: LogLevel
  category: string
  message: string
  data?: any[]
  error?: Error
}

/**
 * 日志管理器类
 */
export class LogManager extends EventEmitter {
  private config: Required<LogConfig>
  private logQueue: LogEntry[] = []
  private isProcessing = false
  private flushTimer?: NodeJS.Timeout
  private currentLogFile?: string
  private isInitialized = false

  constructor() {
    super()

    // 设置控制台编码（Windows）
    this.setupConsoleEncoding()

    // 默认配置
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableFile: true,
      filePath: join(app.getPath('userData'), 'logs'),
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      format: LogFormat.TEXT,
      includeTimestamp: true,
      includeLevel: true,
      includeCategory: true
    }
  }

  /**
   * 初始化日志管理器
   */
  public async initialize(config?: Partial<LogConfig>): Promise<void> {
    if (this.isInitialized) {
      console.warn('[LogManager] 日志管理器已经初始化')
      return
    }

    try {
      // 合并配置
      if (config) {
        this.config = { ...this.config, ...config }
      }

      // 确保日志目录存在
      if (this.config.enableFile) {
        this.ensureLogDirectory()
        this.initializeLogFile()
        this.cleanupOldLogs()
      }

      // 启动日志处理
      this.startLogProcessing()

      this.isInitialized = true
      this.emit('initialized')
      this.log(LogLevel.INFO, 'LogManager', '日志管理器初始化完成')

    } catch (error) {
      console.error('[LogManager] 日志管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * 记录调试日志
   */
  public debug(category: string, message: string, ...data: any[]): void {
    this.log(LogLevel.DEBUG, category, message, ...data)
  }

  /**
   * 记录信息日志
   */
  public info(category: string, message: string, ...data: any[]): void {
    this.log(LogLevel.INFO, category, message, ...data)
  }

  /**
   * 记录警告日志
   */
  public warn(category: string, message: string, ...data: any[]): void {
    this.log(LogLevel.WARN, category, message, ...data)
  }

  /**
   * 记录错误日志
   */
  public error(category: string, message: string, error?: Error, ...data: any[]): void {
    this.log(LogLevel.ERROR, category, message, error, ...data)
  }

  /**
   * 记录日志
   */
  public log(level: LogLevel, category: string, message: string, ...data: any[]): void {
    // 检查日志级别
    if (level < this.config.level) {
      return
    }

    // 创建日志条目
    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      category,
      message,
      data: data.length > 0 ? data : undefined
    }

    // 处理错误对象
    if (data.length > 0 && data[0] instanceof Error) {
      entry.error = data[0]
      entry.data = data.slice(1)
    }

    // 添加到队列
    this.logQueue.push(entry)

    // 触发处理
    this.processLogQueue()

    // 发出事件
    this.emit('log', entry)
  }

  /**
   * 设置日志级别
   */
  public setLevel(level: LogLevel): void {
    this.config.level = level
    this.log(LogLevel.INFO, 'LogManager', `日志级别设置为: ${LogLevel[level]}`)
  }

  /**
   * 获取日志级别
   */
  public getLevel(): LogLevel {
    return this.config.level
  }

  /**
   * 刷新日志缓冲区
   */
  public async flush(): Promise<void> {
    return new Promise((resolve) => {
      if (this.logQueue.length === 0) {
        resolve()
        return
      }

      const onFlush = () => {
        this.off('flushed', onFlush)
        resolve()
      }

      this.on('flushed', onFlush)
      this.processLogQueue()
    })
  }

  /**
   * 销毁日志管理器
   */
  public async destroy(): Promise<void> {
    // 刷新剩余日志
    await this.flush()

    // 停止定时器
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
      this.flushTimer = undefined
    }

    // 清理事件监听器
    this.removeAllListeners()

    this.isInitialized = false
    console.log('[LogManager] 日志管理器销毁完成')
  }

  /**
   * 确保日志目录存在
   */
  private ensureLogDirectory(): void {
    if (!existsSync(this.config.filePath)) {
      mkdirSync(this.config.filePath, { recursive: true })
    }
  }

  /**
   * 初始化日志文件
   */
  private initializeLogFile(): void {
    const now = new Date()
    const dateStr = now.toISOString().split('T')[0] // YYYY-MM-DD
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-') // HH-MM-SS
    
    this.currentLogFile = join(this.config.filePath, `app-${dateStr}-${timeStr}.log`)
  }

  /**
   * 启动日志处理
   */
  private startLogProcessing(): void {
    // 每秒处理一次日志队列
    this.flushTimer = setInterval(() => {
      this.processLogQueue()
    }, 1000)
  }

  /**
   * 处理日志队列
   */
  private processLogQueue(): void {
    if (this.isProcessing || this.logQueue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      const entries = [...this.logQueue]
      this.logQueue = []

      for (const entry of entries) {
        // 控制台输出
        if (this.config.enableConsole) {
          this.outputToConsole(entry)
        }

        // 文件输出
        if (this.config.enableFile && this.currentLogFile) {
          this.outputToFile(entry)
        }
      }

      this.emit('flushed')

    } catch (error) {
      console.error('[LogManager] 处理日志队列失败:', error)
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 输出到控制台
   */
  private outputToConsole(entry: LogEntry): void {
    const message = this.formatLogEntry(entry, LogFormat.TEXT)
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message)
        break
      case LogLevel.INFO:
        console.info(message)
        break
      case LogLevel.WARN:
        console.warn(message)
        break
      case LogLevel.ERROR:
        console.error(message)
        break
    }
  }

  /**
   * 输出到文件
   */
  private outputToFile(entry: LogEntry): void {
    if (!this.currentLogFile) {
      return
    }

    try {
      // 检查文件大小
      if (existsSync(this.currentLogFile)) {
        const stats = statSync(this.currentLogFile)
        if (stats.size >= this.config.maxFileSize) {
          this.rotateLogFile()
        }
      }

      // 格式化日志
      const message = this.formatLogEntry(entry, this.config.format)

      // 写入文件，指定 UTF-8 编码
      writeFileSync(this.currentLogFile, message + '\n', {
        flag: 'a',
        encoding: 'utf8'
      })

    } catch (error) {
      console.error('[LogManager] 写入日志文件失败:', error)
    }
  }

  /**
   * 格式化日志条目
   */
  private formatLogEntry(entry: LogEntry, format: LogFormat): string {
    if (format === LogFormat.JSON) {
      return JSON.stringify({
        timestamp: entry.timestamp.toISOString(),
        level: LogLevel[entry.level],
        category: entry.category,
        message: entry.message,
        data: entry.data,
        error: entry.error ? {
          name: entry.error.name,
          message: entry.error.message,
          stack: entry.error.stack
        } : undefined
      })
    }

    // 文本格式
    const parts: string[] = []

    if (this.config.includeTimestamp) {
      parts.push(`[${entry.timestamp.toISOString()}]`)
    }

    if (this.config.includeLevel) {
      parts.push(`[${LogLevel[entry.level]}]`)
    }

    if (this.config.includeCategory) {
      parts.push(`[${entry.category}]`)
    }

    parts.push(entry.message)

    if (entry.data && entry.data.length > 0) {
      parts.push(JSON.stringify(entry.data))
    }

    if (entry.error) {
      parts.push(`Error: ${entry.error.message}`)
      if (entry.error.stack) {
        parts.push(`Stack: ${entry.error.stack}`)
      }
    }

    return parts.join(' ')
  }

  /**
   * 轮转日志文件
   */
  private rotateLogFile(): void {
    if (!this.currentLogFile) {
      return
    }

    try {
      // 创建新的日志文件
      this.initializeLogFile()
      
      // 清理旧日志文件
      this.cleanupOldLogs()
      
      this.log(LogLevel.INFO, 'LogManager', `日志文件轮转: ${this.currentLogFile}`)

    } catch (error) {
      console.error('[LogManager] 日志文件轮转失败:', error)
    }
  }

  /**
   * 清理旧日志文件
   */
  private cleanupOldLogs(): void {
    try {
      const files = readdirSync(this.config.filePath)
        .filter(file => file.startsWith('app-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: join(this.config.filePath, file),
          stats: statSync(join(this.config.filePath, file))
        }))
        .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime())

      // 删除超出数量限制的文件
      if (files.length > this.config.maxFiles) {
        const filesToDelete = files.slice(this.config.maxFiles)
        for (const file of filesToDelete) {
          unlinkSync(file.path)
          console.log(`[LogManager] 删除旧日志文件: ${file.name}`)
        }
      }

    } catch (error) {
      console.error('[LogManager] 清理旧日志文件失败:', error)
    }
  }

  /**
   * 设置控制台编码（Windows 系统）
   */
  private setupConsoleEncoding(): void {
    try {
      // 检查是否为 Windows 系统
      if (process.platform === 'win32') {
        // 设置控制台输出编码为 UTF-8
        if (process.stdout && process.stdout.setEncoding) {
          process.stdout.setEncoding('utf8')
        }
        if (process.stderr && process.stderr.setEncoding) {
          process.stderr.setEncoding('utf8')
        }

        // 设置环境变量
        process.env.PYTHONIOENCODING = 'utf-8'
        process.env.NODE_OPTIONS = (process.env.NODE_OPTIONS || '') + ' --input-type=module'

        // 尝试设置控制台代码页为 UTF-8 (65001)
        if (process.env.TERM_PROGRAM !== 'vscode') {
          try {
            const { spawn } = require('child_process')
            spawn('chcp', ['65001'], {
              stdio: 'ignore',
              shell: true,
              detached: true
            }).unref()
          } catch (error) {
            // 忽略错误，这只是一个优化
          }
        }
      }
    } catch (error) {
      // 忽略编码设置错误，不影响主要功能
      console.warn('[LogManager] 设置控制台编码失败:', error.message)
    }
  }
}

export default LogManager
