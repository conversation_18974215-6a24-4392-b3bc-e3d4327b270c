/**
 * 通用核心服务适配器
 * 提供基础的核心功能，如文件操作、设备信息等
 */

import { ipcMain } from 'electron'
import BaseService from '@main/services/base/base-service'
import type {
  IBaseService,
  ServiceConfig,
  ServiceHealth,
} from '@main/services/base/service-interface'
// IPC 通道常量
const IPC_COMMON_SELECT_FILE = 'common:select-file'
const IPC_COMMON_SAVE_FILE = 'common:save-file'
const IPC_COMMON_GET_DEVICE_INFO = 'common:get-device-info'

/**
 * 通用核心服务配置
 */
export interface CommonCoreConfig extends ServiceConfig {
  enableFileOperations?: boolean
  enableDeviceInfo?: boolean
  maxFileSize?: number
}

/**
 * 通用核心服务适配器
 */
export class CommonCoreAdapter extends BaseService implements IBaseService {
  private ipcHandlers: string[] = []

  constructor(config: CommonCoreConfig) {
    super({
      name: 'common-core',
      version: '1.0.0',
      dependencies: [],
      healthCheckInterval: 60000,
      enableFileOperations: true,
      enableDeviceInfo: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      ...config,
    })
  }

  /**
   * 初始化服务
   */
  protected async onInitialize(): Promise<void> {
    this.log('info', '初始化通用核心服务...')

    // 设置IPC处理器
    this.setupIpcHandlers()

    this.log('info', '通用核心服务初始化完成')
  }

  /**
   * 启动服务
   */
  protected async onStart(): Promise<void> {
    this.log('info', '启动通用核心服务...')

    // 这里可以添加启动逻辑

    this.log('info', '通用核心服务启动完成')
  }

  /**
   * 停止服务
   */
  protected async onStop(): Promise<void> {
    this.log('info', '停止通用核心服务...')

    // 清理IPC处理器
    this.cleanupIpcHandlers()

    this.log('info', '通用核心服务停止完成')
  }

  /**
   * 销毁服务
   */
  protected async onDestroy(): Promise<void> {
    this.log('info', '销毁通用核心服务...')

    // 确保清理所有资源
    this.cleanupIpcHandlers()

    this.log('info', '通用核心服务销毁完成')
  }

  /**
   * 健康检查
   */
  protected async onHealthCheck(): Promise<ServiceHealth> {
    const details: Record<string, any> = {}
    let healthy = true
    let status = 'healthy'

    try {
      // 检查IPC处理器
      details.ipcHandlers = this.ipcHandlers.length

      // 检查文件操作功能
      const config = this._config as CommonCoreConfig
      if (config.enableFileOperations) {
        details.fileOperations = 'enabled'
      }

      // 检查设备信息功能
      if (config.enableDeviceInfo) {
        details.deviceInfo = 'enabled'
      }

      if (healthy) {
        status = 'all systems operational'
      }
    } catch (error) {
      healthy = false
      status = 'health check failed'
      details.error = error instanceof Error ? error.message : '未知错误'
    }

    return {
      healthy,
      status,
      details,
      timestamp: new Date(),
    }
  }

  /**
   * 配置更新处理
   */
  protected async onConfigUpdate(
    _oldConfig: ServiceConfig,
    _newConfig: ServiceConfig
  ): Promise<void> {
    this.log('info', '更新通用核心服务配置')

    // 这里可以根据配置变化重新初始化相关组件
  }

  /**
   * 设置IPC处理器
   */
  private setupIpcHandlers(): void {
    const config = this._config as CommonCoreConfig

    if (config.enableFileOperations) {
      // 文件选择处理器
      ipcMain.handle(IPC_COMMON_SELECT_FILE, this.handleSelectFile.bind(this))
      this.ipcHandlers.push(IPC_COMMON_SELECT_FILE)

      // 文件保存处理器
      ipcMain.handle(IPC_COMMON_SAVE_FILE, this.handleSaveFile.bind(this))
      this.ipcHandlers.push(IPC_COMMON_SAVE_FILE)
    }

    if (config.enableDeviceInfo) {
      // 设备信息处理器
      ipcMain.handle(
        IPC_COMMON_GET_DEVICE_INFO,
        this.handleGetDeviceInfo.bind(this)
      )
      this.ipcHandlers.push(IPC_COMMON_GET_DEVICE_INFO)
    }

    this.log('info', `设置了 ${this.ipcHandlers.length} 个IPC处理器`)
  }

  /**
   * 清理IPC处理器
   */
  private cleanupIpcHandlers(): void {
    for (const channel of this.ipcHandlers) {
      ipcMain.removeHandler(channel)
    }
    this.ipcHandlers = []
    this.log('info', 'IPC处理器清理完成')
  }

  /**
   * 处理文件选择
   */
  private async handleSelectFile(
    _event: any,
    options: Electron.OpenDialogOptions
  ): Promise<any> {
    const startTime = Date.now()

    try {
      const { dialog } = await import('electron')
      const result = await dialog.showOpenDialog(options)

      this.recordRequest(Date.now() - startTime)

      return {
        success: true,
        data: result.filePaths,
        canceled: result.canceled,
      }
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()

      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 处理文件保存
   */
  private async handleSaveFile(
    _event: any,
    data: string | Buffer,
    options?: Electron.SaveDialogOptions
  ): Promise<any> {
    const startTime = Date.now()

    try {
      const { dialog } = await import('electron')
      const { writeFileSync } = await import('fs')

      // 显示保存对话框
      const result = await dialog.showSaveDialog(options || {})

      if (result.canceled || !result.filePath) {
        this.recordRequest(Date.now() - startTime)
        return {
          success: false,
          canceled: true,
        }
      }

      // 检查文件大小限制
      const config = this._config as CommonCoreConfig
      if (Buffer.isBuffer(data) && data.length > config.maxFileSize!) {
        throw new Error(`文件大小超过限制 (${config.maxFileSize} bytes)`)
      }

      if (
        typeof data === 'string' &&
        Buffer.byteLength(data, 'utf8') > config.maxFileSize!
      ) {
        throw new Error(`文件大小超过限制 (${config.maxFileSize} bytes)`)
      }

      // 保存文件
      writeFileSync(result.filePath, data)

      this.recordRequest(Date.now() - startTime)

      return {
        success: true,
        filePath: result.filePath,
      }
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()

      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 处理获取设备信息
   */
  private async handleGetDeviceInfo(_event: any): Promise<any> {
    const startTime = Date.now()

    try {
      const { app } = await import('electron')
      const os = await import('os')

      const deviceInfo = {
        platform: process.platform,
        arch: process.arch,
        version: process.version,
        electronVersion: process.versions.electron,
        chromeVersion: process.versions.chrome,
        nodeVersion: process.versions.node,
        appVersion: app.getVersion(),
        appName: app.getName(),
        osType: os.type(),
        osRelease: os.release(),
        osHostname: os.hostname(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpuCount: os.cpus().length,
        uptime: os.uptime(),
      }

      this.recordRequest(Date.now() - startTime)

      return {
        success: true,
        data: deviceInfo,
      }
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()

      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }
}

export default CommonCoreAdapter
