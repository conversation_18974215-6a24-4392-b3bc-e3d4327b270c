/**
 * 服务适配器
 * 为了兼容现有服务，提供适配器模式的服务注册
 */
import type { ServiceManager } from '@main/core/service/service-manager'
import type { IBaseService } from '@main/services/base/service-interface'

/**
 * 服务适配器类
 */
export class ServiceAdapters {
  /**
   * 注册所有服务
   */
  public async registerAll(serviceManager: ServiceManager): Promise<void> {
    // 按依赖顺序注册服务
    await this.registerCoreServices(serviceManager)
    await this.registerDataServices(serviceManager)
    await this.registerFeatureServices(serviceManager)
    await this.registerUIServices(serviceManager)
  }

  /**
   * 注册核心服务
   */
  private async registerCoreServices(
    serviceManager: ServiceManager
  ): Promise<void> {
    // 注册通用核心服务
    const commonCoreService = await this.createCommonCoreService()
    serviceManager.register(commonCoreService)
  }

  /**
   * 注册数据服务
   */
  private async registerDataServices(
    serviceManager: ServiceManager
  ): Promise<void> {
    // 注册数据库服务
    const databaseService = await this.createDatabaseService()
    serviceManager.register(databaseService)

    // 注册存储服务
    const storageService = await this.createStorageService()
    serviceManager.register(storageService)
  }

  /**
   * 注册功能服务
   */
  private async registerFeatureServices(
    serviceManager: ServiceManager
  ): Promise<void> {
    // 注册剪贴板服务
    const clipboardService = await this.createClipboardService()
    serviceManager.register(clipboardService)
  }

  /**
   * 注册UI服务
   */
  private async registerUIServices(
    serviceManager: ServiceManager
  ): Promise<void> {
    // 注册窗口服务
    const windowService = await this.createWindowService()
    serviceManager.register(windowService)
  }

  /**
   * 创建通用核心服务
   */
  private async createCommonCoreService(): Promise<IBaseService> {
    const { CommonCoreAdapter } = await import('./adapters/common-core.adapter')
    return new CommonCoreAdapter({
      name: 'common-core',
      version: '1.0.0',
    })
  }

  /**
   * 创建数据库服务
   */
  private async createDatabaseService(): Promise<IBaseService> {
    const { DatabaseService } = await import(
      '../../services/database/database-service'
    )
    return new DatabaseService({
      name: 'database',
      version: '1.0.0',
      dependencies: ['common-core'],
      type: 'sqlite',
      backup: {
        enabled: true,
        interval: 3600000,
        maxBackups: 10,
      },
    })
  }

  /**
   * 创建存储服务
   */
  private async createStorageService(): Promise<IBaseService> {
    const { StorageService } = await import(
      '../../services/storage/storage-service'
    )
    return new StorageService({
      name: 'storage',
      version: '1.0.0',
      dependencies: ['common-core'],
      cache: {
        enabled: true,
        maxSize: 1000,
        ttl: 3600000,
      },
    })
  }

  /**
   * 创建剪贴板服务
   */
  private async createClipboardService(): Promise<IBaseService> {
    const { ClipboardService } = await import(
      '../../services/clipboard/clipboard-service'
    )
    return new ClipboardService({
      name: 'clipboard',
      version: '1.0.0',
      dependencies: ['storage'],
      maxHistorySize: 100,
      enableHistory: true,
      autoWatch: true,
      filters: {
        maxTextLength: 10000,
        allowedFileTypes: ['*'],
        filterDuplicates: true,
      },
    })
  }

  /**
   * 创建窗口服务
   */
  private async createWindowService(): Promise<IBaseService> {
    const { WindowService } = await import(
      '../../services/window/window-service'
    )
    return new WindowService({
      name: 'window',
      version: '1.0.0',
      dependencies: ['common-core'],
      limits: {
        maxWindows: 50,
        minWidth: 300,
        minHeight: 200,
        maxWidth: 4000,
        maxHeight: 3000,
      },
      management: {
        autoCleanup: true,
        trackFocus: true,
        saveState: false,
      },
    })
  }
}

export default ServiceAdapters
