/**
 * 服务管理器
 * 负责服务的注册、初始化、启动、停止和销毁
 */

import { EventEmitter } from 'events'
import type { IBaseService } from '../../services/base/service-interface'

/**
 * 服务状态枚举
 */
export enum ServiceState {
  REGISTERED = 'registered',
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error',
  DESTROYED = 'destroyed'
}

/**
 * 服务元数据接口
 */
export interface ServiceMetadata {
  service: IBaseService
  state: ServiceState
  dependencies: string[]
  dependents: Set<string>
  initializeTime?: number
  startTime?: number
  errorCount: number
  lastError?: Error
}

/**
 * 服务管理器类
 */
export class ServiceManager extends EventEmitter {
  private services = new Map<string, ServiceMetadata>()
  private initializationOrder: string[] = []
  private isInitialized = false
  private isShuttingDown = false

  constructor() {
    super()
  }

  /**
   * 注册服务
   */
  public register(service: IBaseService): void {
    if (this.services.has(service.name)) {
      throw new Error(`服务 ${service.name} 已经注册`)
    }

    const metadata: ServiceMetadata = {
      service,
      state: ServiceState.REGISTERED,
      dependencies: service.dependencies || [],
      dependents: new Set(),
      errorCount: 0
    }

    this.services.set(service.name, metadata)
    
    // 更新依赖关系
    this.updateDependencyGraph(service.name, metadata.dependencies)
    
    this.log('info', `服务 ${service.name} 注册成功`)
    this.emit('serviceRegistered', service.name)
  }

  /**
   * 注销服务
   */
  public unregister(serviceName: string): void {
    const metadata = this.services.get(serviceName)
    if (!metadata) {
      throw new Error(`服务 ${serviceName} 未注册`)
    }

    // 检查是否有其他服务依赖此服务
    if (metadata.dependents.size > 0) {
      throw new Error(`服务 ${serviceName} 被其他服务依赖，无法注销`)
    }

    // 如果服务正在运行，先停止它
    if (metadata.state === ServiceState.RUNNING) {
      this.stopService(serviceName)
    }

    this.services.delete(serviceName)
    this.log('info', `服务 ${serviceName} 注销成功`)
    this.emit('serviceUnregistered', serviceName)
  }

  /**
   * 初始化所有服务
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.log('warn', '服务管理器已经初始化')
      return
    }

    try {
      this.log('info', '开始初始化所有服务...')
      
      // 计算初始化顺序
      this.calculateInitializationOrder()
      
      // 按依赖顺序初始化服务
      for (const serviceName of this.initializationOrder) {
        await this.initializeService(serviceName)
      }
      
      this.isInitialized = true
      this.log('info', '所有服务初始化完成')
      this.emit('allServicesInitialized')
    } catch (error) {
      this.log('error', '服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 启动所有服务
   */
  public async startAll(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('服务管理器未初始化')
    }

    try {
      this.log('info', '开始启动所有服务...')
      
      // 按初始化顺序启动服务
      for (const serviceName of this.initializationOrder) {
        await this.startService(serviceName)
      }
      
      this.log('info', '所有服务启动完成')
      this.emit('allServicesStarted')
    } catch (error) {
      this.log('error', '服务启动失败:', error)
      throw error
    }
  }

  /**
   * 停止所有服务
   */
  public async stopAll(): Promise<void> {
    try {
      this.log('info', '开始停止所有服务...')
      
      // 按初始化顺序的逆序停止服务
      const stopOrder = [...this.initializationOrder].reverse()
      for (const serviceName of stopOrder) {
        await this.stopService(serviceName)
      }
      
      this.log('info', '所有服务停止完成')
      this.emit('allServicesStopped')
    } catch (error) {
      this.log('error', '服务停止失败:', error)
      throw error
    }
  }

  /**
   * 销毁所有服务
   */
  public async destroy(): Promise<void> {
    if (this.isShuttingDown) {
      return
    }

    this.isShuttingDown = true

    try {
      this.log('info', '开始销毁所有服务...')
      
      // 先停止所有服务
      await this.stopAll()
      
      // 按初始化顺序的逆序销毁服务
      const destroyOrder = [...this.initializationOrder].reverse()
      for (const serviceName of destroyOrder) {
        await this.destroyService(serviceName)
      }
      
      this.services.clear()
      this.initializationOrder = []
      this.isInitialized = false
      
      this.log('info', '所有服务销毁完成')
      this.emit('allServicesDestroyed')
    } catch (error) {
      this.log('error', '服务销毁失败:', error)
      throw error
    }
  }

  /**
   * 获取服务实例
   */
  public getService<T extends IBaseService>(serviceName: string): T | null {
    const metadata = this.services.get(serviceName)
    return metadata ? (metadata.service as T) : null
  }

  /**
   * 获取服务状态
   */
  public getServiceState(serviceName: string): ServiceState | null {
    const metadata = this.services.get(serviceName)
    return metadata ? metadata.state : null
  }

  /**
   * 获取所有服务信息
   */
  public getAllServices(): Array<{ name: string; state: ServiceState; dependencies: string[] }> {
    return Array.from(this.services.entries()).map(([name, metadata]) => ({
      name,
      state: metadata.state,
      dependencies: metadata.dependencies
    }))
  }

  /**
   * 检查服务是否正在运行
   */
  public isServiceRunning(serviceName: string): boolean {
    const metadata = this.services.get(serviceName)
    return metadata ? metadata.state === ServiceState.RUNNING : false
  }

  /**
   * 重启服务
   */
  public async restartService(serviceName: string): Promise<void> {
    this.log('info', `重启服务: ${serviceName}`)
    
    await this.stopService(serviceName)
    await this.startService(serviceName)
    
    this.log('info', `服务 ${serviceName} 重启完成`)
  }

  /**
   * 初始化单个服务
   */
  private async initializeService(serviceName: string): Promise<void> {
    const metadata = this.services.get(serviceName)
    if (!metadata) {
      throw new Error(`服务 ${serviceName} 未注册`)
    }

    if (metadata.state !== ServiceState.REGISTERED) {
      this.log('warn', `服务 ${serviceName} 已经初始化，跳过`)
      return
    }

    try {
      this.setState(serviceName, ServiceState.INITIALIZING)
      this.log('info', `初始化服务: ${serviceName}`)
      
      const startTime = Date.now()
      await metadata.service.initialize()
      metadata.initializeTime = Date.now() - startTime
      
      this.setState(serviceName, ServiceState.INITIALIZED)
      this.log('info', `服务 ${serviceName} 初始化完成 (${metadata.initializeTime}ms)`)
      
    } catch (error) {
      this.setState(serviceName, ServiceState.ERROR)
      metadata.errorCount++
      metadata.lastError = error as Error
      this.log('error', `服务 ${serviceName} 初始化失败:`, error)
      throw error
    }
  }

  /**
   * 启动单个服务
   */
  private async startService(serviceName: string): Promise<void> {
    const metadata = this.services.get(serviceName)
    if (!metadata) {
      throw new Error(`服务 ${serviceName} 未注册`)
    }

    if (metadata.state === ServiceState.RUNNING) {
      this.log('warn', `服务 ${serviceName} 已经运行，跳过`)
      return
    }

    if (metadata.state !== ServiceState.INITIALIZED) {
      throw new Error(`服务 ${serviceName} 未初始化`)
    }

    try {
      this.setState(serviceName, ServiceState.STARTING)
      this.log('info', `启动服务: ${serviceName}`)
      
      const startTime = Date.now()
      await metadata.service.start()
      metadata.startTime = Date.now() - startTime
      
      this.setState(serviceName, ServiceState.RUNNING)
      this.log('info', `服务 ${serviceName} 启动完成 (${metadata.startTime}ms)`)
      
    } catch (error) {
      this.setState(serviceName, ServiceState.ERROR)
      metadata.errorCount++
      metadata.lastError = error as Error
      this.log('error', `服务 ${serviceName} 启动失败:`, error)
      throw error
    }
  }

  /**
   * 停止单个服务
   */
  private async stopService(serviceName: string): Promise<void> {
    const metadata = this.services.get(serviceName)
    if (!metadata) {
      throw new Error(`服务 ${serviceName} 未注册`)
    }

    if (metadata.state !== ServiceState.RUNNING) {
      this.log('warn', `服务 ${serviceName} 未运行，跳过停止`)
      return
    }

    try {
      this.setState(serviceName, ServiceState.STOPPING)
      this.log('info', `停止服务: ${serviceName}`)
      
      await metadata.service.stop()
      
      this.setState(serviceName, ServiceState.STOPPED)
      this.log('info', `服务 ${serviceName} 停止完成`)
      
    } catch (error) {
      this.setState(serviceName, ServiceState.ERROR)
      metadata.errorCount++
      metadata.lastError = error as Error
      this.log('error', `服务 ${serviceName} 停止失败:`, error)
      throw error
    }
  }

  /**
   * 销毁单个服务
   */
  private async destroyService(serviceName: string): Promise<void> {
    const metadata = this.services.get(serviceName)
    if (!metadata) {
      return
    }

    try {
      this.log('info', `销毁服务: ${serviceName}`)
      
      if (metadata.service.destroy) {
        await metadata.service.destroy()
      }
      
      this.setState(serviceName, ServiceState.DESTROYED)
      this.log('info', `服务 ${serviceName} 销毁完成`)
      
    } catch (error) {
      this.log('error', `服务 ${serviceName} 销毁失败:`, error)
    }
  }

  /**
   * 设置服务状态
   */
  private setState(serviceName: string, state: ServiceState): void {
    const metadata = this.services.get(serviceName)
    if (metadata) {
      const oldState = metadata.state
      metadata.state = state
      this.emit('serviceStateChanged', { serviceName, oldState, newState: state })
    }
  }

  /**
   * 更新依赖关系图
   */
  private updateDependencyGraph(serviceName: string, dependencies: string[]): void {
    for (const depName of dependencies) {
      const depMetadata = this.services.get(depName)
      if (depMetadata) {
        depMetadata.dependents.add(serviceName)
      }
    }
  }

  /**
   * 计算初始化顺序（拓扑排序）
   */
  private calculateInitializationOrder(): void {
    const visited = new Set<string>()
    const visiting = new Set<string>()
    const order: string[] = []

    const visit = (serviceName: string) => {
      if (visiting.has(serviceName)) {
        throw new Error(`检测到循环依赖: ${serviceName}`)
      }
      
      if (visited.has(serviceName)) {
        return
      }

      visiting.add(serviceName)
      
      const metadata = this.services.get(serviceName)
      if (metadata) {
        for (const depName of metadata.dependencies) {
          if (!this.services.has(depName)) {
            throw new Error(`服务 ${serviceName} 依赖的服务 ${depName} 未注册`)
          }
          visit(depName)
        }
      }
      
      visiting.delete(serviceName)
      visited.add(serviceName)
      order.push(serviceName)
    }

    for (const serviceName of this.services.keys()) {
      visit(serviceName)
    }

    this.initializationOrder = order
    this.log('info', `服务初始化顺序: ${order.join(' -> ')}`)
  }

  /**
   * 日志记录
   */
  private log(level: 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    console[level](`[ServiceManager] ${message}`, ...args)
  }
}

export default ServiceManager
