/**
 * Smart Toolbox 主进程入口文件
 *
 * 使用新的模块化架构，提供清晰的初始化流程和完善的错误处理
 *
 * @file index.ts
 * @description 主进程入口点
 * @version 1.0.0
 *
 * 架构组件：
 * - Application: 应用生命周期管理
 * - ConfigManager: 配置管理
 * - LogManager: 日志管理
 * - ServiceManager: 服务管理
 * - WindowManager: 窗口管理
 * - ErrorHandler: 错误处理
 */

import { Application } from './app/application'

/**
 * 主进程入口函数
 */
async function main(): Promise<void> {
  try {

    console.log('Smart Toolbox 启动中...')

    // 获取应用实例
    const app = Application.getInstance()

    // 监听应用事件
    app.on('stateChanged', ({ oldState, newState }) => {
      console.log(`应用状态变化: ${oldState} -> ${newState}`)
    })

    app.on('error', error => {
      console.error('应用错误:', error)
    })

    app.on('shortcut', action => {
      console.log(`快捷键触发: ${action}`)
    })

    // 应用会自动处理 Electron 的生命周期事件
    // 无需手动设置 app.whenReady() 等事件

    console.log('Smart Toolbox 主进程初始化完成')
  } catch (error) {
    console.error('Smart Toolbox 启动失败:', error)
    process.exit(1)
  }
}

// 启动应用
main().catch(error => {
  console.error('主进程启动异常:', error)
  process.exit(1)
})
