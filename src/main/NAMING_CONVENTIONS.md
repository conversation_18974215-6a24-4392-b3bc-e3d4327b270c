# Smart Toolbox 主进程命名规范

## 文件命名规范

### 1. 基本原则

- 使用 **kebab-case** (短横线分隔) 作为文件名格式
- 使用 **PascalCase** 作为类名格式
- 使用 **camelCase** 作为变量和函数名格式
- 使用 **SCREAMING_SNAKE_CASE** 作为常量名格式

### 2. 文件类型后缀

#### 服务文件
- **格式**: `{service-name}.service.ts`
- **示例**: `clipboard.service.ts`, `database.service.ts`
- **说明**: 所有服务实现文件使用 `.service.ts` 后缀

#### 管理器文件
- **格式**: `{manager-name}-manager.ts`
- **示例**: `config-manager.ts`, `log-manager.ts`, `window-manager.ts`
- **说明**: 所有管理器文件使用 `-manager.ts` 后缀

#### 适配器文件
- **格式**: `{adapter-name}.adapter.ts`
- **示例**: `common-core.adapter.ts`, `database.adapter.ts`
- **说明**: 所有适配器文件使用 `.adapter.ts` 后缀

#### 提供者文件
- **格式**: `{provider-name}.provider.ts`
- **示例**: `filesystem.provider.ts`, `terminal.provider.ts`
- **说明**: 所有提供者文件使用 `.provider.ts` 后缀

#### 工厂文件
- **格式**: `{factory-name}-factory.ts`
- **示例**: `service-factory.ts`, `window-factory.ts`
- **说明**: 所有工厂文件使用 `-factory.ts` 后缀

#### 类型定义文件
- **格式**: `{type-name}.types.ts`
- **示例**: `app.types.ts`, `service.types.ts`, `window.types.ts`
- **说明**: 所有类型定义文件使用 `.types.ts` 后缀

#### 常量文件
- **格式**: `{constant-name}.constants.ts`
- **示例**: `app.constants.ts`, `service.constants.ts`
- **说明**: 所有常量文件使用 `.constants.ts` 后缀

#### 工具函数文件
- **格式**: `{util-name}.utils.ts`
- **示例**: `path.utils.ts`, `file.utils.ts`, `validation.utils.ts`
- **说明**: 所有工具函数文件使用 `.utils.ts` 后缀

#### 配置文件
- **格式**: `{config-name}.config.ts` 或 `{config-name}-config.ts`
- **示例**: `app.config.ts`, `default-config.ts`
- **说明**: 配置文件使用 `.config.ts` 或 `-config.ts` 后缀

#### 测试文件
- **格式**: `{test-name}.test.ts` 或 `{test-name}.spec.ts`
- **示例**: `service-manager.test.ts`, `window-manager.spec.ts`
- **说明**: 测试文件使用 `.test.ts` 或 `.spec.ts` 后缀

### 3. 目录命名规范

#### 功能模块目录
- **格式**: `kebab-case`
- **示例**: `clipboard/`, `window-manager/`, `database/`
- **说明**: 功能模块目录使用短横线分隔

#### 分层目录
- **格式**: `单数名词`
- **示例**: `app/`, `core/`, `service/`, `window/`, `shared/`
- **说明**: 架构分层目录使用单数形式

#### 复数集合目录
- **格式**: `复数名词`
- **示例**: `services/`, `windows/`, `types/`, `constants/`, `utils/`
- **说明**: 包含多个同类文件的目录使用复数形式

### 4. 类命名规范

#### 服务类
- **格式**: `{ServiceName}Service`
- **示例**: `ClipboardService`, `DatabaseService`, `StorageService`
- **说明**: 服务类以 `Service` 结尾

#### 管理器类
- **格式**: `{ManagerName}Manager` 或 `{ManagerName}`
- **示例**: `ConfigManager`, `LogManager`, `Application`
- **说明**: 管理器类以 `Manager` 结尾或使用描述性名称

#### 适配器类
- **格式**: `{AdapterName}Adapter`
- **示例**: `CommonCoreAdapter`, `DatabaseAdapter`
- **说明**: 适配器类以 `Adapter` 结尾

#### 提供者类
- **格式**: `{ProviderName}Provider`
- **示例**: `FileSystemProvider`, `TerminalProvider`
- **说明**: 提供者类以 `Provider` 结尾

#### 工厂类
- **格式**: `{FactoryName}Factory`
- **示例**: `ServiceFactory`, `WindowFactory`
- **说明**: 工厂类以 `Factory` 结尾

### 5. 接口命名规范

#### 服务接口
- **格式**: `I{ServiceName}Service`
- **示例**: `IClipboardService`, `IDatabaseService`
- **说明**: 服务接口以 `I` 开头，以 `Service` 结尾

#### 配置接口
- **格式**: `{ConfigName}Config`
- **示例**: `AppConfig`, `ServiceConfig`, `WindowConfig`
- **说明**: 配置接口以 `Config` 结尾

#### 选项接口
- **格式**: `{OptionName}Options`
- **示例**: `StartupOptions`, `WindowOptions`
- **说明**: 选项接口以 `Options` 结尾

#### 信息接口
- **格式**: `{InfoName}Info`
- **示例**: `AppInfo`, `ServiceInfo`, `WindowInfo`
- **说明**: 信息接口以 `Info` 结尾

### 6. 枚举命名规范

#### 状态枚举
- **格式**: `{StateName}State`
- **示例**: `AppState`, `ServiceState`, `WindowState`
- **说明**: 状态枚举以 `State` 结尾

#### 类型枚举
- **格式**: `{TypeName}Type`
- **示例**: `ErrorType`, `WindowType`, `ServiceType`
- **说明**: 类型枚举以 `Type` 结尾

#### 级别枚举
- **格式**: `{LevelName}Level`
- **示例**: `LogLevel`, `ErrorLevel`
- **说明**: 级别枚举以 `Level` 结尾

### 7. 常量命名规范

#### 应用常量
- **格式**: `APP_{CONSTANT_NAME}`
- **示例**: `APP_NAME`, `APP_VERSION`, `APP_PATHS`
- **说明**: 应用级常量以 `APP_` 开头

#### 配置常量
- **格式**: `CONFIG_{CONSTANT_NAME}`
- **示例**: `CONFIG_KEYS`, `CONFIG_DEFAULTS`
- **说明**: 配置相关常量以 `CONFIG_` 开头

#### 事件常量
- **格式**: `EVENT_{CONSTANT_NAME}`
- **示例**: `EVENT_NAMES`, `EVENT_TYPES`
- **说明**: 事件相关常量以 `EVENT_` 开头

#### 错误常量
- **格式**: `ERROR_{CONSTANT_NAME}`
- **示例**: `ERROR_CODES`, `ERROR_MESSAGES`
- **说明**: 错误相关常量以 `ERROR_` 开头

### 8. 变量命名规范

#### 私有成员变量
- **格式**: `_{variableName}`
- **示例**: `_config`, `_services`, `_isInitialized`
- **说明**: 私有成员变量以下划线开头

#### 静态变量
- **格式**: `{VariableName}` (PascalCase)
- **示例**: `Instance`, `DefaultConfig`
- **说明**: 静态变量使用 PascalCase

#### 局部变量
- **格式**: `{variableName}` (camelCase)
- **示例**: `configManager`, `serviceInstance`
- **说明**: 局部变量使用 camelCase

### 9. 函数命名规范

#### 公共方法
- **格式**: `{actionName}` (camelCase)
- **示例**: `initialize()`, `start()`, `stop()`, `destroy()`
- **说明**: 公共方法使用动词开头的 camelCase

#### 私有方法
- **格式**: `{actionName}` (camelCase)
- **示例**: `setupHandlers()`, `validateConfig()`
- **说明**: 私有方法使用动词开头的 camelCase

#### 事件处理方法
- **格式**: `on{EventName}` 或 `handle{EventName}`
- **示例**: `onInitialize()`, `handleError()`
- **说明**: 事件处理方法以 `on` 或 `handle` 开头

#### 获取器方法
- **格式**: `get{PropertyName}`
- **示例**: `getConfig()`, `getService()`, `getInstance()`
- **说明**: 获取器方法以 `get` 开头

#### 设置器方法
- **格式**: `set{PropertyName}`
- **示例**: `setConfig()`, `setState()`
- **说明**: 设置器方法以 `set` 开头

#### 检查器方法
- **格式**: `is{PropertyName}` 或 `has{PropertyName}`
- **示例**: `isInitialized()`, `hasService()`
- **说明**: 检查器方法以 `is` 或 `has` 开头

### 10. 导入导出规范

#### 默认导出
```typescript
// 类的默认导出
export default class ServiceManager { }
export { ServiceManager }

// 函数的默认导出
export default function createService() { }
```

#### 命名导出
```typescript
// 多个导出
export { ServiceManager, ConfigManager, LogManager }

// 类型导出
export type { IService, ServiceConfig }

// 重新导出
export * from './service-manager'
export { ServiceManager } from './service-manager'
```

### 11. 文件头注释规范

```typescript
/**
 * 文件描述
 * 详细说明文件的功能和用途
 * 
 * @file filename.ts
 * @description 文件描述
 * @version 1.0.0
 * <AUTHOR> Toolbox Team
 * @since 2024-01-01
 */
```

### 12. 示例

#### 完整的服务文件示例
```typescript
/**
 * 剪贴板服务
 * 提供剪贴板监听和历史记录功能
 *
 * @file clipboard.service.ts
 * @description 剪贴板服务实现
 * @version 1.0.0
 */

import type { IClipboardService, ServiceConfig } from '../base/service-interface'
import BaseService from '../base/base-service'

export interface ClipboardServiceConfig extends ServiceConfig {
  historyLimit?: number
  autoClean?: boolean
}

export class ClipboardService extends BaseService implements IClipboardService {
  private _historyProvider?: HistoryProvider
  private _monitorProvider?: MonitorProvider

  constructor(config: ClipboardServiceConfig) {
    super(config)
  }

  public async initialize(): Promise<void> {
    // 实现初始化逻辑
  }

  private setupProviders(): void {
    // 设置提供者
  }
}

export default ClipboardService
```

这套命名规范确保了代码的一致性、可读性和可维护性，便于团队协作和代码理解。
