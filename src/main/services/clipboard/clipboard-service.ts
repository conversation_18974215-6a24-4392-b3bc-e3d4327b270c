/**
 * 剪贴板服务
 * 提供剪贴板监听、数据管理、历史记录等功能，符合新的服务规范
 */

import { BrowserWindow, ipcMain } from 'electron'
import BaseService from '@main/services/base/base-service'
import type {
  IClipboardService,
  ServiceConfig,
  ServiceHealth,
  ClipboardData
} from '../base/service-interface'
import {
  IPC_CLIPBOARD_WATCH_HANDLER,
  IPC_CLIPBOARD_HAS,
  IPC_CLIPBOARD_GET,
  IPC_CLIPBOARD_SET,
  IPC_CLIPBOARD_CLEAR,
} from '@shared/ipc-common'
import SmartClipboard from '@smart/clipboard'

/**
 * 剪贴板服务配置
 */
export interface ClipboardServiceConfig extends ServiceConfig {
  /** 历史记录最大数量 */
  maxHistorySize?: number
  /** 是否启用历史记录 */
  enableHistory?: boolean
  /** 监听间隔（毫秒） */
  watchInterval?: number
  /** 是否自动启动监听 */
  autoWatch?: boolean
  /** 数据过滤器 */
  filters?: {
    /** 最大文本长度 */
    maxTextLength?: number
    /** 允许的文件类型 */
    allowedFileTypes?: string[]
    /** 是否过滤重复数据 */
    filterDuplicates?: boolean
  }
}

/**
 * 剪贴板服务实现
 */
export class ClipboardService extends BaseService implements IClipboardService {
  private history: ClipboardData[] = []
  private isWatching = false
  private watchCallback?: (data: ClipboardData) => void
  private lastClipboardData?: ClipboardData
  private ipcHandlers: string[] = []

  constructor(config: ClipboardServiceConfig) {
    super({
      name: 'clipboard',
      version: '1.0.0',
      dependencies: ['storage'],
      healthCheckInterval: 60000, // 1分钟检查一次
      maxHistorySize: 100,
      enableHistory: true,
      watchInterval: 1000,
      autoWatch: true,
      filters: {
        maxTextLength: 10000,
        allowedFileTypes: ['*'],
        filterDuplicates: true
      },
      ...config
    })
  }

  /**
   * 获取剪贴板数据
   */
  public async getData(): Promise<ClipboardData> {
    const startTime = Date.now()
    
    try {
      const data: ClipboardData = {
        text: SmartClipboard.getText() || undefined,
        image: SmartClipboard.getImage() || undefined,
        files: SmartClipboard.getFiles() || undefined,
        html: SmartClipboard.getHtml() || undefined,
        contentType: SmartClipboard.getContentType() as any,
        timestamp: new Date(),
        source: 'system'
      }

      this.recordRequest(Date.now() - startTime)
      return data
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 设置剪贴板数据
   */
  public async setData(data: ClipboardData): Promise<void> {
    const startTime = Date.now()
    
    try {
      if (data.text) {
        SmartClipboard.setText(data.text)
      }
      if (data.image) {
        const imageData = typeof data.image === 'string' ? data.image : data.image.toString('base64')
        SmartClipboard.setImage(imageData)
      }
      if (data.files) {
        SmartClipboard.setFiles(data.files)
      }
      if (data.html) {
        SmartClipboard.setHtml(data.html)
      }

      // 添加到历史记录
      const clipboardConfig = this._config as ClipboardServiceConfig
      if (clipboardConfig.enableHistory) {
        this.addToHistory({
          ...data,
          timestamp: new Date(),
          source: 'application'
        })
      }

      this.recordRequest(Date.now() - startTime)
      this.log('info', '剪贴板数据已设置')
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 清空剪贴板
   */
  public async clear(): Promise<void> {
    const startTime = Date.now()
    
    try {
      SmartClipboard.clear()
      this.recordRequest(Date.now() - startTime)
      this.log('info', '剪贴板已清空')
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 检查是否有数据
   */
  public async hasData(): Promise<boolean> {
    const startTime = Date.now()
    
    try {
      const result = SmartClipboard.hasData()
      this.recordRequest(Date.now() - startTime)
      return result
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 监听剪贴板变化
   */
  public watch(callback: (data: ClipboardData) => void): () => void {
    this.watchCallback = callback
    
    if (!this.isWatching) {
      this.startWatching()
    }

    // 返回取消监听的函数
    return () => {
      this.watchCallback = undefined
      this.stopWatching()
    }
  }

  /**
   * 获取剪贴板历史
   */
  public getHistory(): ClipboardData[] {
    return [...this.history]
  }

  /**
   * 清空历史记录
   */
  public clearHistory(): void {
    this.history = []
    this.log('info', '剪贴板历史记录已清空')
  }

  /**
   * 初始化服务
   */
  protected async onInitialize(): Promise<void> {
    this.log('info', '正在初始化剪贴板服务...')

    // 设置IPC处理器
    this.setupIpcHandlers()

    // 加载历史记录（如果启用）
    const clipboardConfig = this._config as ClipboardServiceConfig
    if (clipboardConfig.enableHistory) {
      await this.loadHistory()
    }

    this.log('info', '剪贴板服务初始化完成')
  }

  /**
   * 启动服务
   */
  protected async onStart(): Promise<void> {
    this.log('info', '正在启动剪贴板服务...')

    // 自动启动监听
    const clipboardConfig = this._config as ClipboardServiceConfig
    if (clipboardConfig.autoWatch) {
      this.startWatching()
    }

    this.log('info', '剪贴板服务启动完成')
  }

  /**
   * 停止服务
   */
  protected async onStop(): Promise<void> {
    this.log('info', '正在停止剪贴板服务...')

    // 停止监听
    this.stopWatching()

    // 清理IPC处理器
    this.cleanupIpcHandlers()

    // 保存历史记录
    const clipboardConfig = this._config as ClipboardServiceConfig
    if (clipboardConfig.enableHistory) {
      await this.saveHistory()
    }

    this.log('info', '剪贴板服务停止完成')
  }

  /**
   * 销毁服务
   */
  protected async onDestroy(): Promise<void> {
    this.log('info', '正在销毁剪贴板服务...')

    // 清空历史记录
    this.history = []
    this.watchCallback = undefined
    this.lastClipboardData = undefined

    this.log('info', '剪贴板服务销毁完成')
  }

  /**
   * 健康检查
   */
  protected async onHealthCheck(): Promise<ServiceHealth> {
    const details: Record<string, any> = {}
    let healthy = true
    let status = 'healthy'

    try {
      // 检查剪贴板功能
      const hasData = await this.hasData()
      details.clipboardAccess = 'available'
      details.hasData = hasData

      // 检查监听状态
      details.watching = this.isWatching
      
      // 检查历史记录
      const clipboardConfig = this._config as ClipboardServiceConfig
      if (clipboardConfig.enableHistory) {
        details.historySize = this.history.length
        details.maxHistorySize = clipboardConfig.maxHistorySize
      }

      // 检查IPC处理器
      details.ipcHandlers = this.ipcHandlers.length

      status = 'all systems operational'

    } catch (error) {
      healthy = false
      status = 'clipboard access failed'
      details.error = error instanceof Error ? error.message : '未知错误'
    }

    return {
      healthy,
      status,
      details,
      timestamp: new Date()
    }
  }

  /**
   * 配置更新处理
   */
  protected async onConfigUpdate(oldConfig: ServiceConfig, newConfig: ServiceConfig): Promise<void> {
    this.log('info', '更新剪贴板服务配置')

    const oldClipboardConfig = oldConfig as ClipboardServiceConfig
    const newClipboardConfig = newConfig as ClipboardServiceConfig

    // 如果历史记录设置发生变化
    if (oldClipboardConfig.enableHistory !== newClipboardConfig.enableHistory) {
      if (!newClipboardConfig.enableHistory) {
        this.clearHistory()
      }
    }

    // 如果最大历史记录数量发生变化
    if (oldClipboardConfig.maxHistorySize !== newClipboardConfig.maxHistorySize) {
      this.trimHistory()
    }

    // 如果监听设置发生变化
    if (oldClipboardConfig.autoWatch !== newClipboardConfig.autoWatch) {
      if (newClipboardConfig.autoWatch && !this.isWatching) {
        this.startWatching()
      } else if (!newClipboardConfig.autoWatch && this.isWatching) {
        this.stopWatching()
      }
    }
  }

  /**
   * 开始监听剪贴板
   */
  private startWatching(): void {
    if (this.isWatching) {
      return
    }

    try {
      SmartClipboard.watchClipboard(() => {
        this.handleClipboardChange()
      })
      
      this.isWatching = true
      this.log('info', '剪贴板监听已启动')
    } catch (error) {
      this.log('error', '启动剪贴板监听失败:', error)
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 停止监听剪贴板
   */
  private stopWatching(): void {
    if (!this.isWatching) {
      return
    }

    try {
      SmartClipboard.unwatchClipboard()
      this.isWatching = false
      this.log('info', '剪贴板监听已停止')
    } catch (error) {
      this.log('error', '停止剪贴板监听失败:', error)
    }
  }

  /**
   * 处理剪贴板变化
   */
  private async handleClipboardChange(): Promise<void> {
    try {
      const data = await this.getData()
      const clipboardConfig = this._config as ClipboardServiceConfig

      // 过滤重复数据
      if (clipboardConfig.filters?.filterDuplicates && this.isDuplicateData(data)) {
        return
      }

      // 应用过滤器
      if (!this.applyFilters(data)) {
        return
      }

      this.lastClipboardData = data

      // 添加到历史记录
      if (clipboardConfig.enableHistory) {
        this.addToHistory(data)
      }

      // 通知回调
      if (this.watchCallback) {
        this.watchCallback(data)
      }

      // 通知所有窗口
      this.notifyWindows(data)

    } catch (error) {
      this.log('error', '处理剪贴板变化失败:', error)
      this.incrementErrorCount()
    }
  }

  /**
   * 检查是否为重复数据
   */
  private isDuplicateData(data: ClipboardData): boolean {
    if (!this.lastClipboardData) {
      return false
    }

    return (
      this.lastClipboardData.text === data.text &&
      this.lastClipboardData.html === data.html &&
      JSON.stringify(this.lastClipboardData.files) === JSON.stringify(data.files)
    )
  }

  /**
   * 应用过滤器
   */
  private applyFilters(data: ClipboardData): boolean {
    const clipboardConfig = this._config as ClipboardServiceConfig
    const filters = clipboardConfig.filters

    if (!filters) {
      return true
    }

    // 检查文本长度
    if (filters.maxTextLength && data.text && data.text.length > filters.maxTextLength) {
      this.log('warn', `文本长度超过限制: ${data.text.length} > ${filters.maxTextLength}`)
      return false
    }

    // 检查文件类型
    if (filters.allowedFileTypes && data.files) {
      const allowedTypes = filters.allowedFileTypes
      if (!allowedTypes.includes('*')) {
        const hasAllowedFile = data.files.some(file => {
          const ext = file.split('.').pop()?.toLowerCase()
          return ext && allowedTypes.includes(`.${ext}`)
        })
        if (!hasAllowedFile) {
          this.log('warn', '文件类型不被允许')
          return false
        }
      }
    }

    return true
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(data: ClipboardData): void {
    this.history.unshift(data)
    this.trimHistory()
  }

  /**
   * 修剪历史记录
   */
  private trimHistory(): void {
    const clipboardConfig = this._config as ClipboardServiceConfig
    const maxSize = clipboardConfig.maxHistorySize || 100
    if (this.history.length > maxSize) {
      this.history = this.history.slice(0, maxSize)
    }
  }

  /**
   * 通知所有窗口
   */
  private notifyWindows(data: ClipboardData): void {
    const windows = BrowserWindow.getAllWindows()
    windows.forEach(window => {
      try {
        window.webContents.send(IPC_CLIPBOARD_WATCH_HANDLER, data)
      } catch (error) {
        this.log('warn', '通知窗口失败:', error)
      }
    })
  }

  /**
   * 设置IPC处理器
   */
  private setupIpcHandlers(): void {
    const handlers = [
      { channel: IPC_CLIPBOARD_HAS, handler: this.handleHasData.bind(this) },
      { channel: IPC_CLIPBOARD_GET, handler: this.handleGetData.bind(this) },
      { channel: IPC_CLIPBOARD_SET, handler: this.handleSetData.bind(this) },
      { channel: IPC_CLIPBOARD_CLEAR, handler: this.handleClear.bind(this) },
    ]

    for (const { channel, handler } of handlers) {
      ipcMain.handle(channel, handler)
      this.ipcHandlers.push(channel)
    }

    this.log('info', `已注册 ${this.ipcHandlers.length} 个IPC处理器`)
  }

  /**
   * 清理IPC处理器
   */
  private cleanupIpcHandlers(): void {
    for (const channel of this.ipcHandlers) {
      ipcMain.removeHandler(channel)
    }
    this.ipcHandlers = []
    this.log('info', '已清理所有IPC处理器')
  }

  /**
   * 加载历史记录
   */
  private async loadHistory(): Promise<void> {
    // TODO: 从存储服务加载历史记录
    this.log('info', '历史记录加载完成')
  }

  /**
   * 保存历史记录
   */
  private async saveHistory(): Promise<void> {
    // TODO: 保存历史记录到存储服务
    this.log('info', '历史记录保存完成')
  }

  // IPC处理器方法
  private async handleHasData(_event: any): Promise<boolean> {
    try {
      return await this.hasData()
    } catch (error) {
      this.log('error', 'IPC处理失败 - hasData:', error)
      return false
    }
  }

  private async handleGetData(_event: any): Promise<ClipboardData | null> {
    try {
      return await this.getData()
    } catch (error) {
      this.log('error', 'IPC处理失败 - getData:', error)
      return null
    }
  }

  private async handleSetData(_event: any, data: ClipboardData): Promise<boolean> {
    try {
      await this.setData(data)
      return true
    } catch (error) {
      this.log('error', 'IPC处理失败 - setData:', error)
      return false
    }
  }

  private async handleClear(_event: any): Promise<boolean> {
    try {
      await this.clear()
      return true
    } catch (error) {
      this.log('error', 'IPC处理失败 - clear:', error)
      return false
    }
  }
}

export default ClipboardService
