/**
 * 存储服务
 * 提供文件存储、配置存储、缓存管理等功能，符合新的服务规范
 */

import { app } from 'electron'
import {
  readFileSync,
  writeFileSync,
  existsSync,
  mkdirSync,
  unlinkSync,
  readdirSync,
} from 'fs'
import { join, dirname } from 'path'
import BaseService from '../base/base-service'
import type {
  IStorageService,
  ServiceConfig,
  ServiceHealth,
} from '../base/service-interface'

/**
 * 存储服务配置
 */
export interface StorageServiceConfig extends ServiceConfig {
  /** 存储根目录 */
  rootPath?: string
  /** 缓存配置 */
  cache?: {
    enabled?: boolean
    maxSize?: number
    ttl?: number
  }
  /** 文件存储配置 */
  fileStorage?: {
    maxFileSize?: number
    allowedExtensions?: string[]
    compression?: boolean
  }
  /** 备份配置 */
  backup?: {
    enabled?: boolean
    interval?: number
    maxBackups?: number
  }
  /** 加密配置 */
  encryption?: {
    enabled?: boolean
    algorithm?: string
    key?: string
  }
}

/**
 * 缓存项接口
 */
interface CacheItem {
  value: any
  timestamp: number
  ttl: number
}

/**
 * 存储服务实现
 */
export class StorageService extends BaseService implements IStorageService {
  private storage = new Map<string, any>()
  private cache = new Map<string, CacheItem>()
  private rootPath: string
  private cacheCleanupTimer?: NodeJS.Timeout

  constructor(config: StorageServiceConfig) {
    super({
      name: 'storage',
      version: '1.0.0',
      dependencies: [],
      healthCheckInterval: 60000,
      rootPath: join(app.getPath('userData'), 'storage'),
      cache: {
        enabled: true,
        maxSize: 1000,
        ttl: 3600000, // 1小时
      },
      fileStorage: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedExtensions: ['*'],
        compression: false,
      },
      backup: {
        enabled: false,
        interval: 3600000, // 1小时
        maxBackups: 5,
      },
      encryption: {
        enabled: false,
        algorithm: 'aes-256-gcm',
      },
      ...config,
    })

    this.rootPath =
      this._config.rootPath || join(app.getPath('userData'), 'storage')
  }

  /**
   * 获取存储值
   */
  public get<T = any>(key: string, defaultValue?: T): T | undefined {
    const startTime = Date.now()

    try {
      // 先检查缓存
      if (this._config.cache?.enabled) {
        const cached = this.getFromCache(key)
        if (cached !== undefined) {
          this.recordRequest(Date.now() - startTime)
          return cached
        }
      }

      // 从内存存储获取
      if (this.storage.has(key)) {
        const value = this.storage.get(key)
        this.setToCache(key, value)
        this.recordRequest(Date.now() - startTime)
        return value
      }

      // 从文件存储获取
      const value = this.getFromFile(key)
      if (value !== undefined) {
        this.storage.set(key, value)
        this.setToCache(key, value)
        this.recordRequest(Date.now() - startTime)
        return value
      }

      this.recordRequest(Date.now() - startTime)
      return defaultValue
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      this.log('error', `获取存储值失败: ${key}`, error)
      return defaultValue
    }
  }

  /**
   * 设置存储值
   */
  public set(key: string, value: any): void {
    const startTime = Date.now()

    try {
      // 验证值
      this.validateValue(key, value)

      // 设置到内存存储
      this.storage.set(key, value)

      // 设置到缓存
      if (this._config.cache?.enabled) {
        this.setToCache(key, value)
      }

      // 持久化到文件
      this.saveToFile(key, value)

      this.recordRequest(Date.now() - startTime)
      this.log('debug', `存储值已设置: ${key}`)
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      this.log('error', `设置存储值失败: ${key}`, error)
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 删除存储值
   */
  public delete(key: string): void {
    const startTime = Date.now()

    try {
      // 从内存存储删除
      this.storage.delete(key)

      // 从缓存删除
      this.cache.delete(key)

      // 从文件删除
      this.deleteFromFile(key)

      this.recordRequest(Date.now() - startTime)
      this.log('debug', `存储值已删除: ${key}`)
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      this.log('error', `删除存储值失败: ${key}`, error)
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 检查键是否存在
   */
  public has(key: string): boolean {
    const startTime = Date.now()

    try {
      const exists = this.storage.has(key) || this.fileExists(key)
      this.recordRequest(Date.now() - startTime)
      return exists
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      this.log('error', `检查键存在性失败: ${key}`, error)
      return false
    }
  }

  /**
   * 清空存储
   */
  public clear(): void {
    const startTime = Date.now()

    try {
      // 清空内存存储
      this.storage.clear()

      // 清空缓存
      this.cache.clear()

      // 清空文件存储
      this.clearFiles()

      this.recordRequest(Date.now() - startTime)
      this.log('info', '存储已清空')
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      this.log('error', '清空存储失败:', error)
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 获取所有键
   */
  public keys(): string[] {
    const startTime = Date.now()

    try {
      const memoryKeys = Array.from(this.storage.keys())
      const fileKeys = this.getFileKeys()
      const allKeys = [...new Set([...memoryKeys, ...fileKeys])]

      this.recordRequest(Date.now() - startTime)
      return allKeys
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      this.log('error', '获取所有键失败:', error)
      return []
    }
  }

  /**
   * 获取存储大小
   */
  public size(): number {
    const startTime = Date.now()

    try {
      const keys = this.keys()
      this.recordRequest(Date.now() - startTime)
      return keys.length
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      this.log('error', '获取存储大小失败:', error)
      return 0
    }
  }

  /**
   * 获取存储统计信息
   */
  public getStats(): {
    memorySize: number
    cacheSize: number
    fileCount: number
    totalSize: number
  } {
    return {
      memorySize: this.storage.size,
      cacheSize: this.cache.size,
      fileCount: this.getFileKeys().length,
      totalSize: this.size(),
    }
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.cache.clear()
    this.log('info', '缓存已清理')
  }

  /**
   * 初始化服务
   */
  protected async onInitialize(): Promise<void> {
    this.log('info', '正在初始化存储服务...')

    // 确保存储目录存在
    this.ensureStorageDirectory()

    // 加载现有数据
    await this.loadExistingData()

    // 启动缓存清理任务
    if (this._config.cache?.enabled) {
      this.startCacheCleanup()
    }

    this.log('info', '存储服务初始化完成')
  }

  /**
   * 启动服务
   */
  protected async onStart(): Promise<void> {
    this.log('info', '正在启动存储服务...')

    // 启动备份任务（如果启用）
    if (this._config.backup?.enabled) {
      this.startBackupTask()
    }

    this.log('info', '存储服务启动完成')
  }

  /**
   * 停止服务
   */
  protected async onStop(): Promise<void> {
    this.log('info', '正在停止存储服务...')

    // 停止缓存清理任务
    this.stopCacheCleanup()

    // 停止备份任务
    this.stopBackupTask()

    // 保存所有数据
    await this.saveAllData()

    this.log('info', '存储服务停止完成')
  }

  /**
   * 销毁服务
   */
  protected async onDestroy(): Promise<void> {
    this.log('info', '正在销毁存储服务...')

    // 清空内存数据
    this.storage.clear()
    this.cache.clear()

    this.log('info', '存储服务销毁完成')
  }

  /**
   * 健康检查
   */
  protected async onHealthCheck(): Promise<ServiceHealth> {
    const details: Record<string, any> = {}
    let healthy = true
    let status = 'healthy'

    try {
      // 检查存储目录
      if (!existsSync(this.rootPath)) {
        healthy = false
        status = 'storage directory not found'
      } else {
        details.storageDirectory = 'exists'
      }

      // 检查读写权限
      try {
        const testKey = 'health-check-test'
        const testValue = { timestamp: Date.now() }
        this.set(testKey, testValue)
        const retrieved = this.get(testKey)
        this.delete(testKey)

        if (JSON.stringify(retrieved) === JSON.stringify(testValue)) {
          details.readWriteTest = 'passed'
        } else {
          healthy = false
          status = 'read/write test failed'
        }
      } catch (error) {
        healthy = false
        status = 'read/write test failed'
        details.readWriteError =
          error instanceof Error ? error.message : '未知错误'
      }

      // 检查存储统计
      const stats = this.getStats()
      details.stats = stats

      // 检查缓存状态
      if (this._config.cache?.enabled) {
        details.cache = {
          enabled: true,
          size: this.cache.size,
          maxSize: this._config.cache.maxSize,
        }
      }

      if (healthy) {
        status = 'all systems operational'
      }
    } catch (error) {
      healthy = false
      status = 'health check failed'
      details.error = error instanceof Error ? error.message : '未知错误'
    }

    return {
      healthy,
      status,
      details,
      timestamp: new Date(),
    }
  }

  /**
   * 配置更新处理
   */
  protected async onConfigUpdate(
    oldConfig: ServiceConfig,
    newConfig: ServiceConfig
  ): Promise<void> {
    this.log('info', '更新存储服务配置')

    const oldStorageConfig = oldConfig as StorageServiceConfig
    const newStorageConfig = newConfig as StorageServiceConfig

    // 如果缓存配置发生变化
    if (oldStorageConfig.cache?.enabled !== newStorageConfig.cache?.enabled) {
      if (newStorageConfig.cache?.enabled) {
        this.startCacheCleanup()
      } else {
        this.stopCacheCleanup()
        this.clearCache()
      }
    }

    // 如果备份配置发生变化
    if (oldStorageConfig.backup?.enabled !== newStorageConfig.backup?.enabled) {
      if (newStorageConfig.backup?.enabled) {
        this.startBackupTask()
      } else {
        this.stopBackupTask()
      }
    }
  }

  /**
   * 从缓存获取
   */
  private getFromCache(key: string): any {
    const item = this.cache.get(key)
    if (!item) {
      return undefined
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return undefined
    }

    return item.value
  }

  /**
   * 设置到缓存
   */
  private setToCache(key: string, value: any): void {
    const storageConfig = this._config as StorageServiceConfig
    if (!storageConfig.cache?.enabled) {
      return
    }

    const ttl = storageConfig.cache.ttl || 3600000
    const maxSize = storageConfig.cache.maxSize || 1000

    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= maxSize) {
      const oldestKey = this.cache.keys().next().value
      if (oldestKey) {
        this.cache.delete(oldestKey)
      }
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl,
    })
  }

  /**
   * 从文件获取
   */
  private getFromFile(key: string): any {
    const filePath = this.getFilePath(key)
    if (!existsSync(filePath)) {
      return undefined
    }

    try {
      const content = readFileSync(filePath, 'utf-8')
      return JSON.parse(content)
    } catch (error) {
      this.log('error', `从文件读取失败: ${key}`, error)
      return undefined
    }
  }

  /**
   * 保存到文件
   */
  private saveToFile(key: string, value: any): void {
    const filePath = this.getFilePath(key)
    const dir = dirname(filePath)

    // 确保目录存在
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true })
    }

    try {
      const content = JSON.stringify(value, null, 2)
      writeFileSync(filePath, content, 'utf-8')
    } catch (error) {
      this.log('error', `保存到文件失败: ${key}`, error)
      throw error
    }
  }

  /**
   * 从文件删除
   */
  private deleteFromFile(key: string): void {
    const filePath = this.getFilePath(key)
    if (existsSync(filePath)) {
      try {
        unlinkSync(filePath)
      } catch (error) {
        this.log('error', `删除文件失败: ${key}`, error)
      }
    }
  }

  /**
   * 检查文件是否存在
   */
  private fileExists(key: string): boolean {
    const filePath = this.getFilePath(key)
    return existsSync(filePath)
  }

  /**
   * 获取文件路径
   */
  private getFilePath(key: string): string {
    // 将键转换为安全的文件路径
    const safeKey = key.replace(/[^a-zA-Z0-9-_]/g, '_')
    return join(this.rootPath, `${safeKey}.json`)
  }

  /**
   * 获取文件键列表
   */
  private getFileKeys(): string[] {
    if (!existsSync(this.rootPath)) {
      return []
    }

    try {
      return readdirSync(this.rootPath)
        .filter(file => file.endsWith('.json'))
        .map(file => file.replace('.json', ''))
        .map(safeKey => safeKey.replace(/_/g, '.')) // 简单的反转换
    } catch (error) {
      this.log('error', '获取文件键列表失败:', error)
      return []
    }
  }

  /**
   * 清空文件
   */
  private clearFiles(): void {
    if (!existsSync(this.rootPath)) {
      return
    }

    try {
      const files = readdirSync(this.rootPath)
      for (const file of files) {
        if (file.endsWith('.json')) {
          unlinkSync(join(this.rootPath, file))
        }
      }
    } catch (error) {
      this.log('error', '清空文件失败:', error)
    }
  }

  /**
   * 确保存储目录存在
   */
  private ensureStorageDirectory(): void {
    if (!existsSync(this.rootPath)) {
      mkdirSync(this.rootPath, { recursive: true })
      this.log('info', `创建存储目录: ${this.rootPath}`)
    }
  }

  /**
   * 加载现有数据
   */
  private async loadExistingData(): Promise<void> {
    const keys = this.getFileKeys()
    let loadedCount = 0

    for (const key of keys) {
      try {
        const value = this.getFromFile(key)
        if (value !== undefined) {
          this.storage.set(key, value)
          loadedCount++
        }
      } catch (error) {
        this.log('warn', `加载数据失败: ${key}`, error)
      }
    }

    this.log('info', `已加载 ${loadedCount} 个存储项`)
  }

  /**
   * 保存所有数据
   */
  private async saveAllData(): Promise<void> {
    let savedCount = 0

    for (const [key, value] of this.storage) {
      try {
        this.saveToFile(key, value)
        savedCount++
      } catch (error) {
        this.log('warn', `保存数据失败: ${key}`, error)
      }
    }

    this.log('info', `已保存 ${savedCount} 个存储项`)
  }

  /**
   * 验证值
   */
  private validateValue(key: string, value: any): void {
    if (key === null || key === undefined || key === '') {
      throw this.createError('CONFIGURATION', '键不能为空')
    }

    try {
      JSON.stringify(value)
    } catch (error) {
      throw this.createError('CONFIGURATION', '值必须可序列化为JSON')
    }
  }

  /**
   * 启动缓存清理任务
   */
  private startCacheCleanup(): void {
    this.cacheCleanupTimer = setInterval(() => {
      this.cleanupExpiredCache()
    }, 60000) // 每分钟清理一次
  }

  /**
   * 停止缓存清理任务
   */
  private stopCacheCleanup(): void {
    if (this.cacheCleanupTimer) {
      clearInterval(this.cacheCleanupTimer)
      this.cacheCleanupTimer = undefined
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanupExpiredCache(): void {
    const now = Date.now()
    let cleanedCount = 0

    for (const [key, item] of this.cache) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      this.log('debug', `清理了 ${cleanedCount} 个过期缓存项`)
    }
  }

  /**
   * 启动备份任务
   */
  private startBackupTask(): void {
    // TODO: 实现备份任务
    this.log('info', '备份任务已启动')
  }

  /**
   * 停止备份任务
   */
  private stopBackupTask(): void {
    // TODO: 停止备份任务
    this.log('info', '备份任务已停止')
  }
}

export default StorageService
