import Store, { type Options } from 'electron-store'
import { defaultConfig } from '@main/common/configs/config'
import { scheme } from './schema'
export default class Storage {
  private store: Store<SchemaType>
  constructor(options?: Options<SchemaType>) {
    this.store = new Store({
      schema: scheme,
      defaults: defaultConfig,
      accessPropertiesByDotNotation: true,
      encryptionKey: '1234567890',
      ...options,
    })
  }
  // 设置值，支持自动处理特殊类型如Date
  set(
    key: DotNotationKeyOf<SchemaType>,
    value: DotNotationValueOf<SchemaType, typeof key>
  ) {
    if (value instanceof Date) {
      value = value.toISOString()
    }
    return this.store.set(key, value)
  }

  // 获取值，支持类型转换和默认值
  get(key: DotNotationKeyOf<SchemaType>, defaultValue: any = null) {
    const rawValue = this.store.get(key)
    if (rawValue === undefined && defaultValue !== null) {
      return defaultValue
    }

    try {
      if (
        typeof defaultValue === 'string' &&
        defaultValue.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z$/)
      ) {
        return new Date(rawValue as string)
      }
    } catch (error) {
      console.error(`Error converting value for key ${key}:`, error)
    }

    return rawValue
  }

  // 检查是否存在某个键
  has(key: DotNotationKeyOf<SchemaType>) {
    return this.store.has(key)
  }

  // 删除键值对
  delete(key: DotNotationKeyOf<SchemaType>) {
    return this.store.delete(key)
  }

  reset() {
    return this.store.reset()
  }

  // 清空所有存储
  clear() {
    return this.store.clear()
  }
}
