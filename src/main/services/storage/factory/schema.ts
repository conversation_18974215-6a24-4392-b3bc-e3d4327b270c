export const scheme = {
  appearance: {
    type: 'object',
    properties: {
      theme: {
        type: 'string',
        enum: ['system', 'light', 'dark'],
      },
      fontFamily: {
        type: 'string',
      },
    },
    required: ['theme', 'fontFamily'],
  },
  base: {
    type: 'object',
    properties: {
      dataPath: {
        type: 'string',
      }
    },
    required: ['dataPath'],
  },
}

export const STORE_KEYS: { [key: string]: keyof SchemaType } = {
  APPEARANCE: 'appearance',
  BASE: 'base',
}
