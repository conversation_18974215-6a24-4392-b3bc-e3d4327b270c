/**
 * 数据库服务
 * 提供数据库连接管理、事务处理、数据迁移等功能，符合新的服务规范
 */

import { ipcMain } from 'electron'
import BaseService from '../base/base-service'
import type {
  IDatabaseService,
  ServiceConfig,
  ServiceHealth
} from '../base/service-interface'
import {
  IPC_DATABASE_PUT,
  IPC_DATABASE_GET,
  IPC_DATABASE_REMOVE,
  IPC_DATABASE_BULK_DOCS,
  IPC_DATABASE_ALL_DOCS,
  IPC_DATABASE_CLEAR,
  IPC_DATABASE_CLOSE,
} from '@shared/ipc-common'
import DataBaseFactory, { initializeDatabase } from './factory'
import { modelProviders } from '@main/shared/configs/model-config'

/**
 * 数据库服务配置
 */
export interface DatabaseServiceConfig extends ServiceConfig {
  /** 数据库类型 */
  type?: 'sqlite' | 'mysql' | 'postgresql'
  /** 数据库路径或连接字符串 */
  path?: string
  /** 连接池配置 */
  pool?: {
    min?: number
    max?: number
    idle?: number
  }
  /** 事务配置 */
  transaction?: {
    timeout?: number
    retryAttempts?: number
  }
  /** 备份配置 */
  backup?: {
    enabled?: boolean
    interval?: number
    maxBackups?: number
  }
  /** 性能配置 */
  performance?: {
    cacheSize?: number
    batchSize?: number
  }
}

/**
 * 数据库连接状态
 */
export enum DatabaseConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

/**
 * 数据库服务实现
 */
export class DatabaseService extends BaseService implements IDatabaseService {
  private databaseCore?: DataBaseFactory
  private connectionStatus: DatabaseConnectionStatus = DatabaseConnectionStatus.DISCONNECTED
  private ipcHandlers: string[] = []
  private transactionCount = 0
  private activeTransactions = new Set<string>()

  constructor(config: DatabaseServiceConfig) {
    super({
      name: 'database',
      version: '1.0.0',
      dependencies: [],
      healthCheckInterval: 30000,
      type: 'sqlite',
      pool: {
        min: 1,
        max: 10,
        idle: 30000
      },
      transaction: {
        timeout: 30000,
        retryAttempts: 3
      },
      backup: {
        enabled: false,
        interval: 3600000, // 1小时
        maxBackups: 10
      },
      performance: {
        cacheSize: 1000,
        batchSize: 100
      },
      ...config
    })
  }

  /**
   * 获取数据
   */
  public async get(id: string): Promise<any> {
    const startTime = Date.now()
    
    try {
      if (!this.databaseCore) {
        throw this.createError('RUNTIME', '数据库核心未初始化')
      }

      const result = await this.databaseCore.get(id)
      this.recordRequest(Date.now() - startTime)
      return result
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 保存数据
   */
  public async put(doc: any): Promise<any> {
    const startTime = Date.now()
    
    try {
      if (!this.databaseCore) {
        throw this.createError('RUNTIME', '数据库核心未初始化')
      }

      const result = await this.databaseCore.put(doc)
      this.recordRequest(Date.now() - startTime)
      return result
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 删除数据
   */
  public async remove(docOrId: any): Promise<any> {
    const startTime = Date.now()
    
    try {
      if (!this.databaseCore) {
        throw this.createError('RUNTIME', '数据库核心未初始化')
      }

      const result = await this.databaseCore.remove(docOrId)
      this.recordRequest(Date.now() - startTime)
      return result
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 批量操作
   */
  public async bulkDocs(docs: any[]): Promise<any[]> {
    const startTime = Date.now()
    
    try {
      if (!this.databaseCore) {
        throw this.createError('RUNTIME', '数据库核心未初始化')
      }

      const result = await this.databaseCore.bulkDocs(docs)
      this.recordRequest(Date.now() - startTime)
      return result
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 查询所有文档
   */
  public async allDocs(options?: any): Promise<any[]> {
    const startTime = Date.now()
    
    try {
      if (!this.databaseCore) {
        throw this.createError('RUNTIME', '数据库核心未初始化')
      }

      const result = await this.databaseCore.allDocs(options)
      this.recordRequest(Date.now() - startTime)
      return result
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 清空数据库
   */
  public async clear(): Promise<void> {
    const startTime = Date.now()
    
    try {
      if (!this.databaseCore) {
        throw this.createError('RUNTIME', '数据库核心未初始化')
      }

      await this.databaseCore.clear()
      this.recordRequest(Date.now() - startTime)
      this.log('info', '数据库已清空')
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 执行事务
   */
  public async transaction<T>(callback: () => Promise<T>): Promise<T> {
    const transactionId = this.generateTransactionId()
    const startTime = Date.now()
    
    try {
      this.activeTransactions.add(transactionId)
      this.transactionCount++
      
      this.log('info', `开始事务: ${transactionId}`)
      
      // TODO: 实现真正的事务逻辑
      const result = await callback()
      
      this.activeTransactions.delete(transactionId)
      this.recordRequest(Date.now() - startTime)
      
      this.log('info', `事务完成: ${transactionId}`)
      return result
      
    } catch (error) {
      this.activeTransactions.delete(transactionId)
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      
      this.log('error', `事务失败: ${transactionId}`, error)
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus(): DatabaseConnectionStatus {
    return this.connectionStatus
  }

  /**
   * 获取活动事务数量
   */
  public getActiveTransactionCount(): number {
    return this.activeTransactions.size
  }

  /**
   * 初始化服务
   */
  protected async onInitialize(): Promise<void> {
    this.log('info', '正在初始化数据库服务...')

    try {
      this.connectionStatus = DatabaseConnectionStatus.CONNECTING
      
      // 初始化数据库连接
      const dataSource = await initializeDatabase()
      this.databaseCore = new DataBaseFactory(dataSource)
      
      this.connectionStatus = DatabaseConnectionStatus.CONNECTED
      
      // 初始化默认数据
      await this.initializeDefaultData()
      
      // 设置IPC处理器
      this.setupIpcHandlers()
      
      this.log('info', '数据库服务初始化完成')
      
    } catch (error) {
      this.connectionStatus = DatabaseConnectionStatus.ERROR
      throw this.wrapError(error, 'INITIALIZATION')
    }
  }

  /**
   * 启动服务
   */
  protected async onStart(): Promise<void> {
    this.log('info', '正在启动数据库服务...')

    if (this.connectionStatus !== DatabaseConnectionStatus.CONNECTED) {
      throw this.createError('RUNTIME', '数据库连接未建立')
    }

    // 启动备份任务（如果启用）
    const dbConfig = this._config as DatabaseServiceConfig
    if (dbConfig.backup?.enabled) {
      this.startBackupTask()
    }

    this.log('info', '数据库服务启动完成')
  }

  /**
   * 停止服务
   */
  protected async onStop(): Promise<void> {
    this.log('info', '正在停止数据库服务...')

    // 等待所有活动事务完成
    await this.waitForTransactions()

    // 清理IPC处理器
    this.cleanupIpcHandlers()

    // 停止备份任务
    this.stopBackupTask()

    this.log('info', '数据库服务停止完成')
  }

  /**
   * 销毁服务
   */
  protected async onDestroy(): Promise<void> {
    this.log('info', '正在销毁数据库服务...')

    // 关闭数据库连接
    if (this.databaseCore) {
      // TODO: 实现数据库连接关闭
      this.databaseCore = undefined
    }

    this.connectionStatus = DatabaseConnectionStatus.DISCONNECTED
    this.activeTransactions.clear()

    this.log('info', '数据库服务销毁完成')
  }

  /**
   * 健康检查
   */
  protected async onHealthCheck(): Promise<ServiceHealth> {
    const details: Record<string, any> = {}
    let healthy = true
    let status = 'healthy'

    try {
      // 检查连接状态
      details.connectionStatus = this.connectionStatus
      if (this.connectionStatus !== DatabaseConnectionStatus.CONNECTED) {
        healthy = false
        status = 'database connection failed'
      }

      // 检查数据库核心
      if (!this.databaseCore) {
        healthy = false
        status = 'database core not initialized'
      } else {
        // 执行简单查询测试连接
        try {
          await this.databaseCore.get('health-check-test')
          details.queryTest = 'passed'
        } catch (error) {
          // 查询失败不一定是问题（可能是数据不存在）
          details.queryTest = 'completed'
        }
      }

      // 检查活动事务
      details.activeTransactions = this.activeTransactions.size
      details.totalTransactions = this.transactionCount

      // 检查IPC处理器
      details.ipcHandlers = this.ipcHandlers.length

      if (healthy) {
        status = 'all systems operational'
      }

    } catch (error) {
      healthy = false
      status = 'health check failed'
      details.error = error instanceof Error ? error.message : '未知错误'
    }

    return {
      healthy,
      status,
      details,
      timestamp: new Date()
    }
  }

  /**
   * 配置更新处理
   */
  protected async onConfigUpdate(oldConfig: ServiceConfig, newConfig: ServiceConfig): Promise<void> {
    this.log('info', '更新数据库服务配置')

    const oldDbConfig = oldConfig as DatabaseServiceConfig
    const newDbConfig = newConfig as DatabaseServiceConfig

    // 如果备份配置发生变化
    if (oldDbConfig.backup?.enabled !== newDbConfig.backup?.enabled) {
      if (newDbConfig.backup?.enabled) {
        this.startBackupTask()
      } else {
        this.stopBackupTask()
      }
    }
  }

  /**
   * 初始化默认数据
   */
  private async initializeDefaultData(): Promise<void> {
    try {
      // 检查模型提供商数据是否存在
      const modelService = await this.get('model-providers')
      if (!modelService) {
        await this.put({
          _id: 'model-providers',
          providers: modelProviders,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        this.log('info', '已初始化默认模型提供商数据')
      }
    } catch (error) {
      this.log('error', '初始化默认数据失败:', error)
      throw this.wrapError(error, 'INITIALIZATION')
    }
  }

  /**
   * 等待所有事务完成
   */
  private async waitForTransactions(timeout = 30000): Promise<void> {
    const startTime = Date.now()
    
    while (this.activeTransactions.size > 0) {
      if (Date.now() - startTime > timeout) {
        this.log('warn', `等待事务超时，强制终止 ${this.activeTransactions.size} 个活动事务`)
        this.activeTransactions.clear()
        break
      }
      
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  /**
   * 生成事务ID
   */
  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 启动备份任务
   */
  private startBackupTask(): void {
    // TODO: 实现备份任务
    this.log('info', '备份任务已启动')
  }

  /**
   * 停止备份任务
   */
  private stopBackupTask(): void {
    // TODO: 停止备份任务
    this.log('info', '备份任务已停止')
  }

  /**
   * 设置IPC处理器
   */
  private setupIpcHandlers(): void {
    const handlers = [
      { channel: IPC_DATABASE_PUT, handler: this.handlePut.bind(this) },
      { channel: IPC_DATABASE_GET, handler: this.handleGet.bind(this) },
      { channel: IPC_DATABASE_REMOVE, handler: this.handleRemove.bind(this) },
      { channel: IPC_DATABASE_BULK_DOCS, handler: this.handleBulkDocs.bind(this) },
      { channel: IPC_DATABASE_ALL_DOCS, handler: this.handleAllDocs.bind(this) },
      { channel: IPC_DATABASE_CLEAR, handler: this.handleClear.bind(this) },
      { channel: IPC_DATABASE_CLOSE, handler: this.handleClose.bind(this) },
    ]

    for (const { channel, handler } of handlers) {
      ipcMain.handle(channel, handler)
      this.ipcHandlers.push(channel)
    }

    this.log('info', `已注册 ${this.ipcHandlers.length} 个IPC处理器`)
  }

  /**
   * 清理IPC处理器
   */
  private cleanupIpcHandlers(): void {
    for (const channel of this.ipcHandlers) {
      ipcMain.removeHandler(channel)
    }
    this.ipcHandlers = []
    this.log('info', '已清理所有IPC处理器')
  }

  // IPC处理器方法
  private async handlePut(_event: any, doc: any) {
    try {
      return await this.put(doc)
    } catch (error) {
      this.log('error', 'IPC处理失败 - put:', error)
      return {
        id: doc._id || '',
        error: true,
        name: 'DatabaseError',
        message: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  private async handleGet(_event: any, id: string) {
    try {
      return await this.get(id)
    } catch (error) {
      this.log('error', 'IPC处理失败 - get:', error)
      return null
    }
  }

  private async handleRemove(_event: any, docOrId: any) {
    try {
      return await this.remove(docOrId)
    } catch (error) {
      this.log('error', 'IPC处理失败 - remove:', error)
      const id = typeof docOrId === 'string' ? docOrId : docOrId._id
      return {
        id: id || '',
        error: true,
        name: 'DatabaseError',
        message: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  private async handleBulkDocs(_event: any, docs: any[]) {
    try {
      return await this.bulkDocs(docs)
    } catch (error) {
      this.log('error', 'IPC处理失败 - bulkDocs:', error)
      return docs.map(doc => ({
        id: doc._id || '',
        error: true,
        name: 'DatabaseError',
        message: error instanceof Error ? error.message : '未知错误',
      }))
    }
  }

  private async handleAllDocs(_event: any, options?: any) {
    try {
      return await this.allDocs(options)
    } catch (error) {
      this.log('error', 'IPC处理失败 - allDocs:', error)
      return []
    }
  }

  private async handleClear(_event: any) {
    try {
      await this.clear()
      return { ok: true }
    } catch (error) {
      this.log('error', 'IPC处理失败 - clear:', error)
      return {
        error: true,
        name: 'DatabaseError',
        message: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  private async handleClose(_event: any) {
    try {
      // TODO: 实现数据库关闭逻辑
      return { ok: true }
    } catch (error) {
      this.log('error', 'IPC处理失败 - close:', error)
      return {
        error: true,
        name: 'DatabaseError',
        message: error instanceof Error ? error.message : '未知错误',
      }
    }
  }
}

export default DatabaseService
