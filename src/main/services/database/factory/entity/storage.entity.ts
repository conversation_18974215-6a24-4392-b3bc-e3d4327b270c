import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm'

@Entity('storage')
export default class StorageEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'uuid',
    length: 36,
    comment: '版本标识符',
  })
  rev: string

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: '存储键名',
  })
  key: string

  @Column({
    type: 'text',
    comment: '存储值',
  })
  value: string

  @Column({
    type: 'varchar',
    length: 50,
    default: 'string',
    comment: '数据类型',
  })
  type: string

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  delete: boolean

  @CreateDateColumn({
    type: 'datetime',
    comment: '创建时间',
  })
  createdAt: Date

  @UpdateDateColumn({
    type: 'datetime',
    comment: '更新时间',
  })
  updatedAt: Date
}
