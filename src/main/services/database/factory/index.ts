import { DataSource, Repository } from 'typeorm'
import { AppDataSource } from './data-source'
import StorageEntity from './entity/storage.entity'
import { v4 as uuidv4 } from 'uuid'

/**
 * 初始化数据库连接
 * initialize database connection
 */
export async function initializeDatabase(): Promise<DataSource> {
  if (!AppDataSource.isInitialized) {
    await AppDataSource.initialize()
  }
  return AppDataSource
}

/**
 * 通用存储服务类
 * 提供类似 uTools 的文档存储功能
 */
export default class DataBaseFactory {
  private repository: Repository<StorageEntity>

  constructor(dataSource: DataSource) {
    this.repository = dataSource.getRepository(StorageEntity)
  }

  /**
   * 创建或更新数据库文档
   * @param doc 文档对象
   * @returns 操作结果
   */
  async put(doc: DbDoc): Promise<DbResult> {
    try {
      const { _id, _rev, ...data } = doc

      if (!_id) {
        return {
          id: '',
          error: true,
          name: 'InvalidId',
          message: '文档 ID 不能为空',
        }
      }

      // 检查是否为更新操作
      const existing = await this.repository.findOne({ where: { key: _id } })

      if (existing) {
        // 更新操作需要检查版本
        if (_rev && existing.rev !== _rev) {
          return {
            id: _id,
            error: true,
            name: 'RevisionConflict',
            message: '文档版本冲突',
          }
        }

        // 更新现有文档
        const newRev = uuidv4()
        existing.value = JSON.stringify(data)
        existing.rev = newRev
        existing.type = 'json'

        await this.repository.save(existing)

        return {
          id: _id,
          rev: newRev,
          ok: true,
        }
      } else {
        // 创建新文档
        const newRev = uuidv4()
        const entity = this.repository.create({
          key: _id,
          value: JSON.stringify(data),
          type: 'json',
          rev: newRev,
          delete: false,
        })

        await this.repository.save(entity)

        return {
          id: _id,
          rev: newRev,
          ok: true,
        }
      }
    } catch (error) {
      return {
        id: doc._id || '',
        error: true,
        name: 'DatabaseError',
        message: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 根据文档 ID 获取文档
   * @param id 文档 ID
   * @returns 文档对象或 null
   */
  async get(id: string): Promise<DbDoc | null> {
    try {
      const record = await this.repository.findOne({
        where: { key: id, delete: false },
        order: { updatedAt: 'DESC' },
      })

      if (!record) {
        return null
      }

      let data: any = {}
      try {
        data = JSON.parse(record.value)
      } catch {
        data = { value: record.value }
      }

      return {
        _id: record.key,
        _rev: record.rev,
        ...data,
      }
    } catch {
      return null
    }
  }

  /**
   * 删除数据库文档
   * @param docOrId 文档对象或文档 ID
   * @returns 操作结果
   */
  async remove(docOrId: DbDoc | string): Promise<DbResult> {
    try {
      const id = typeof docOrId === 'string' ? docOrId : docOrId._id
      const rev = typeof docOrId === 'string' ? undefined : docOrId._rev

      if (!id) {
        return {
          id: '',
          error: true,
          name: 'InvalidId',
          message: '文档 ID 不能为空',
        }
      }

      const existing = await this.repository.findOne({ where: { key: id } })

      if (!existing) {
        return {
          id,
          error: true,
          name: 'NotFound',
          message: '文档不存在',
        }
      }

      // 如果提供了版本号，需要检查版本
      if (rev && existing.rev !== rev) {
        return {
          id,
          error: true,
          name: 'RevisionConflict',
          message: '文档版本冲突',
        }
      }

      await this.repository.remove(existing)

      return {
        id,
        rev: existing.rev,
        ok: true,
      }
    } catch (error) {
      const id = typeof docOrId === 'string' ? docOrId : docOrId._id
      return {
        id: id || '',
        error: true,
        name: 'DatabaseError',
        message: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 批量操作文档
   * @param docs 文档数组
   * @returns 操作结果数组
   */
  async bulkDocs(docs: DbDoc[]): Promise<DbResult[]> {
    const results: DbResult[] = []

    for (const doc of docs) {
      const result = await this.put(doc)
      results.push(result)
    }

    return results
  }

  /**
   * 获取文档数组
   * @param idStartsWithOrIds 文档 ID 前缀或 ID 数组
   * @returns 文档数组
   */
  async allDocs(idStartsWithOrIds?: string | string[]): Promise<DbDoc[]> {
    try {
      let records: StorageEntity[]

      if (!idStartsWithOrIds) {
        // 获取所有文档
        records = await this.repository.find({
          where: { delete: false },
          order: { updatedAt: 'DESC' },
        })
      } else if (Array.isArray(idStartsWithOrIds)) {
        // 根据 ID 数组获取
        records = await this.repository
          .createQueryBuilder('storage')
          .where('storage.key IN (:...ids)', { ids: idStartsWithOrIds })
          .andWhere('storage.delete = :delete', { delete: false })
          .orderBy('storage.updatedAt', 'DESC')
          .getMany()
      } else {
        // 根据前缀获取
        records = await this.repository
          .createQueryBuilder('storage')
          .where('storage.key LIKE :prefix', {
            prefix: `${idStartsWithOrIds}%`,
          })
          .andWhere('storage.delete = :delete', { delete: false })
          .orderBy('storage.updatedAt', 'DESC')
          .getMany()
      }

      return records.map(record => {
        let data: any = {}
        try {
          data = JSON.parse(record.value)
        } catch {
          data = { value: record.value }
        }

        return {
          _id: record.key,
          _rev: record.rev,
          ...data,
        }
      })
    } catch {
      return []
    }
  }

  /**
   * 清空所有数据
   */
  async clear(): Promise<void> {
    await this.repository.clear()
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy()
    }
  }
}
