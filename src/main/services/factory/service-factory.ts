/**
 * 服务工厂
 * 提供服务的创建、注册和管理功能，简化服务的使用
 */

import type { IBaseService, ServiceConfig } from '../base/service-interface'
import ClipboardService, { type ClipboardServiceConfig } from '../clipboard/clipboard-service'
import DatabaseService, { type DatabaseServiceConfig } from '../database/database-service'
import StorageService, { type StorageServiceConfig } from '../storage/storage-service'
import WindowService, { type WindowServiceConfig } from '../window/window-service'

/**
 * 服务类型枚举
 */
export enum ServiceType {
  CLIPBOARD = 'clipboard',
  DATABASE = 'database',
  STORAGE = 'storage',
  WINDOW = 'window'
}

/**
 * 服务构造函数类型
 */
export type ServiceConstructor<T extends IBaseService = IBaseService> = new (config: ServiceConfig) => T

/**
 * 服务注册信息
 */
export interface ServiceRegistration<T extends IBaseService = IBaseService> {
  type: ServiceType
  constructor: ServiceConstructor<T>
  defaultConfig: ServiceConfig
  dependencies: ServiceType[]
  description: string
  version: string
}

/**
 * 服务创建选项
 */
export interface ServiceCreateOptions {
  type: ServiceType
  config?: Partial<ServiceConfig>
  autoStart?: boolean
}

/**
 * 服务工厂类
 */
export class ServiceFactory {
  private static instance: ServiceFactory | null = null
  private registrations = new Map<ServiceType, ServiceRegistration>()
  private instances = new Map<string, IBaseService>()

  /**
   * 私有构造函数，确保单例模式
   */
  private constructor() {
    this.registerBuiltinServices()
  }

  /**
   * 获取服务工厂实例（单例）
   */
  public static getInstance(): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory()
    }
    return ServiceFactory.instance
  }

  /**
   * 注册服务
   */
  public register<T extends IBaseService>(registration: ServiceRegistration<T>): void {
    if (this.registrations.has(registration.type)) {
      throw new Error(`服务类型 ${registration.type} 已经注册`)
    }

    this.registrations.set(registration.type, registration)
    console.log(`[ServiceFactory] 服务类型 ${registration.type} 注册成功`)
  }

  /**
   * 注销服务
   */
  public unregister(type: ServiceType): void {
    this.registrations.delete(type)
    console.log(`[ServiceFactory] 服务类型 ${type} 注销成功`)
  }

  /**
   * 创建服务实例
   */
  public create<T extends IBaseService>(options: ServiceCreateOptions): T {
    const registration = this.registrations.get(options.type)
    if (!registration) {
      throw new Error(`未注册的服务类型: ${options.type}`)
    }

    // 合并配置
    const config: ServiceConfig = {
      ...registration.defaultConfig,
      ...options.config
    }

    // 生成实例ID
    const instanceId = this.generateInstanceId(options.type, config)

    // 检查是否已存在实例
    if (this.instances.has(instanceId)) {
      console.log(`[ServiceFactory] 返回现有服务实例: ${instanceId}`)
      return this.instances.get(instanceId) as T
    }

    // 创建新实例
    const instance = new registration.constructor(config) as T
    this.instances.set(instanceId, instance)

    console.log(`[ServiceFactory] 创建服务实例: ${instanceId}`)

    // 自动启动（如果启用）
    if (options.autoStart) {
      this.autoStartService(instance)
    }

    return instance
  }

  /**
   * 获取服务实例
   */
  public getInstance<T extends IBaseService>(type: ServiceType, instanceId?: string): T | null {
    if (instanceId) {
      return (this.instances.get(instanceId) as T) || null
    }

    // 查找第一个匹配类型的实例
    for (const [id, instance] of this.instances) {
      if (id.startsWith(`${type}_`)) {
        return instance as T
      }
    }

    return null
  }

  /**
   * 获取所有服务实例
   */
  public getAllInstances(): Map<string, IBaseService> {
    return new Map(this.instances)
  }

  /**
   * 销毁服务实例
   */
  public async destroy(instanceId: string): Promise<void> {
    const instance = this.instances.get(instanceId)
    if (instance) {
      try {
        await instance.destroy()
        this.instances.delete(instanceId)
        console.log(`[ServiceFactory] 服务实例已销毁: ${instanceId}`)
      } catch (error) {
        console.error(`[ServiceFactory] 销毁服务实例失败: ${instanceId}`, error)
        throw error
      }
    }
  }

  /**
   * 销毁所有服务实例
   */
  public async destroyAll(): Promise<void> {
    const instances = Array.from(this.instances.entries())
    
    for (const [instanceId, instance] of instances) {
      try {
        await instance.destroy()
        this.instances.delete(instanceId)
        console.log(`[ServiceFactory] 服务实例已销毁: ${instanceId}`)
      } catch (error) {
        console.error(`[ServiceFactory] 销毁服务实例失败: ${instanceId}`, error)
      }
    }
  }

  /**
   * 获取服务注册信息
   */
  public getRegistration(type: ServiceType): ServiceRegistration | null {
    return this.registrations.get(type) || null
  }

  /**
   * 获取所有注册信息
   */
  public getAllRegistrations(): ServiceRegistration[] {
    return Array.from(this.registrations.values())
  }

  /**
   * 检查服务类型是否已注册
   */
  public isRegistered(type: ServiceType): boolean {
    return this.registrations.has(type)
  }

  /**
   * 获取服务依赖关系
   */
  public getDependencies(type: ServiceType): ServiceType[] {
    const registration = this.registrations.get(type)
    return registration ? registration.dependencies : []
  }

  /**
   * 计算服务启动顺序
   */
  public calculateStartOrder(types: ServiceType[]): ServiceType[] {
    const visited = new Set<ServiceType>()
    const visiting = new Set<ServiceType>()
    const order: ServiceType[] = []

    const visit = (type: ServiceType): void => {
      if (visiting.has(type)) {
        throw new Error(`检测到循环依赖: ${type}`)
      }
      if (visited.has(type)) {
        return
      }

      visiting.add(type)
      const dependencies = this.getDependencies(type)
      
      for (const dep of dependencies) {
        if (!types.includes(dep)) {
          throw new Error(`服务 ${type} 的依赖 ${dep} 不在启动列表中`)
        }
        visit(dep)
      }
      
      visiting.delete(type)
      visited.add(type)
      order.push(type)
    }

    for (const type of types) {
      visit(type)
    }

    return order
  }

  /**
   * 批量创建服务
   */
  public createBatch(options: ServiceCreateOptions[]): Map<ServiceType, IBaseService> {
    const services = new Map<ServiceType, IBaseService>()
    
    // 计算启动顺序
    const types = options.map(opt => opt.type)
    const startOrder = this.calculateStartOrder(types)
    
    // 按顺序创建服务
    for (const type of startOrder) {
      const option = options.find(opt => opt.type === type)
      if (option) {
        const service = this.create(option)
        services.set(type, service)
      }
    }
    
    return services
  }

  /**
   * 注册内置服务
   */
  private registerBuiltinServices(): void {
    // 注册剪贴板服务
    this.register({
      type: ServiceType.CLIPBOARD,
      constructor: ClipboardService,
      defaultConfig: {
        name: 'clipboard',
        version: '1.0.0',
        dependencies: ['storage']
      } as ClipboardServiceConfig,
      dependencies: [ServiceType.STORAGE],
      description: '剪贴板服务，提供剪贴板监听和历史记录功能',
      version: '1.0.0'
    })

    // 注册数据库服务
    this.register({
      type: ServiceType.DATABASE,
      constructor: DatabaseService,
      defaultConfig: {
        name: 'database',
        version: '1.0.0',
        dependencies: []
      } as DatabaseServiceConfig,
      dependencies: [],
      description: '数据库服务，提供数据持久化功能',
      version: '1.0.0'
    })

    // 注册存储服务
    this.register({
      type: ServiceType.STORAGE,
      constructor: StorageService,
      defaultConfig: {
        name: 'storage',
        version: '1.0.0',
        dependencies: []
      } as StorageServiceConfig,
      dependencies: [],
      description: '存储服务，提供文件存储和缓存功能',
      version: '1.0.0'
    })

    // 注册窗口服务
    this.register({
      type: ServiceType.WINDOW,
      constructor: WindowService,
      defaultConfig: {
        name: 'window',
        version: '1.0.0',
        dependencies: []
      } as WindowServiceConfig,
      dependencies: [],
      description: '窗口服务，提供窗口管理功能',
      version: '1.0.0'
    })

    console.log('[ServiceFactory] 内置服务注册完成')
  }

  /**
   * 生成实例ID
   */
  private generateInstanceId(type: ServiceType, config: ServiceConfig): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 11)
    return `${type}_${config.name || 'default'}_${timestamp}_${random}`
  }

  /**
   * 自动启动服务
   */
  private async autoStartService(instance: IBaseService): Promise<void> {
    try {
      await instance.initialize()
      await instance.start()
      console.log(`[ServiceFactory] 服务自动启动成功: ${instance.config.name}`)
    } catch (error) {
      console.error(`[ServiceFactory] 服务自动启动失败: ${instance.config.name}`, error)
    }
  }
}

/**
 * 便捷函数：创建剪贴板服务
 */
export function createClipboardService(config?: Partial<ClipboardServiceConfig>): ClipboardService {
  const factory = ServiceFactory.getInstance()
  return factory.create<ClipboardService>({
    type: ServiceType.CLIPBOARD,
    config
  })
}

/**
 * 便捷函数：创建数据库服务
 */
export function createDatabaseService(config?: Partial<DatabaseServiceConfig>): DatabaseService {
  const factory = ServiceFactory.getInstance()
  return factory.create<DatabaseService>({
    type: ServiceType.DATABASE,
    config
  })
}

/**
 * 便捷函数：创建存储服务
 */
export function createStorageService(config?: Partial<StorageServiceConfig>): StorageService {
  const factory = ServiceFactory.getInstance()
  return factory.create<StorageService>({
    type: ServiceType.STORAGE,
    config
  })
}

/**
 * 便捷函数：创建窗口服务
 */
export function createWindowService(config?: Partial<WindowServiceConfig>): WindowService {
  const factory = ServiceFactory.getInstance()
  return factory.create<WindowService>({
    type: ServiceType.WINDOW,
    config
  })
}

export default ServiceFactory
