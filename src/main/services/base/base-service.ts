/**
 * 基础服务抽象类
 * 提供服务的通用功能实现，包括生命周期管理、错误处理、指标收集等
 */

import { EventEmitter } from 'events'
import type {
  IBaseService,
  ServiceConfig,
  ServiceHealth,
  ServiceMetrics,
  ServiceError
} from './service-interface'
import { ServiceStatus } from './service-interface'

/**
 * 基础服务抽象类
 */
export abstract class BaseService extends EventEmitter implements IBaseService {
  protected _config: ServiceConfig
  protected _status: ServiceStatus = ServiceStatus.CREATED
  protected _metrics: ServiceMetrics = {}
  protected _healthCheckTimer?: NodeJS.Timeout
  protected _startTime?: Date
  protected _requestCount = 0
  protected _errorCount = 0
  protected _responseTimes: number[] = []

  constructor(config: ServiceConfig) {
    super()
    this._config = { ...config }
    this.setupMetrics()
  }

  /**
   * 获取服务配置
   */
  public get config(): ServiceConfig {
    return { ...this._config }
  }

  /**
   * 获取服务状态
   */
  public get status(): ServiceStatus {
    return this._status
  }

  /**
   * 获取服务指标
   */
  public get metrics(): ServiceMetrics {
    return { ...this._metrics }
  }

  /**
   * 初始化服务
   */
  public async initialize(config?: Partial<ServiceConfig>): Promise<void> {
    if (this._status !== ServiceStatus.CREATED) {
      throw this.createError('INITIALIZATION', `服务 ${this._config.name} 已经初始化`)
    }

    try {
      this.setStatus(ServiceStatus.INITIALIZING)
      
      // 合并配置
      if (config) {
        this._config = { ...this._config, ...config }
      }

      // 验证配置
      this.validateConfig()

      // 执行具体的初始化逻辑
      await this.onInitialize()

      this.setStatus(ServiceStatus.INITIALIZED)
      this.emit('initialized')
      
      this.log('info', `服务 ${this._config.name} 初始化完成`)
    } catch (error) {
      this.setStatus(ServiceStatus.ERROR)
      this.incrementErrorCount()
      const serviceError = this.wrapError(error, 'INITIALIZATION')
      this.emit('error', serviceError)
      throw serviceError
    }
  }

  /**
   * 启动服务
   */
  public async start(): Promise<void> {
    if (this._status !== ServiceStatus.INITIALIZED) {
      throw this.createError('RUNTIME', `服务 ${this._config.name} 未初始化或状态不正确`)
    }

    try {
      this.setStatus(ServiceStatus.STARTING)
      
      // 执行具体的启动逻辑
      await this.onStart()

      this._startTime = new Date()
      this.setStatus(ServiceStatus.RUNNING)
      
      // 启动健康检查
      this.startHealthCheck()
      
      this.emit('started')
      this.log('info', `服务 ${this._config.name} 启动完成`)
    } catch (error) {
      this.setStatus(ServiceStatus.ERROR)
      this.incrementErrorCount()
      const serviceError = this.wrapError(error, 'RUNTIME')
      this.emit('error', serviceError)
      throw serviceError
    }
  }

  /**
   * 停止服务
   */
  public async stop(): Promise<void> {
    if (this._status !== ServiceStatus.RUNNING) {
      return
    }

    try {
      this.setStatus(ServiceStatus.STOPPING)
      
      // 停止健康检查
      this.stopHealthCheck()
      
      // 执行具体的停止逻辑
      await this.onStop()

      this.setStatus(ServiceStatus.STOPPED)
      this.emit('stopped')
      
      this.log('info', `服务 ${this._config.name} 停止完成`)
    } catch (error) {
      this.setStatus(ServiceStatus.ERROR)
      this.incrementErrorCount()
      const serviceError = this.wrapError(error, 'RUNTIME')
      this.emit('error', serviceError)
      throw serviceError
    }
  }

  /**
   * 销毁服务
   */
  public async destroy(): Promise<void> {
    try {
      // 先停止服务
      if (this._status === ServiceStatus.RUNNING) {
        await this.stop()
      }

      // 执行具体的销毁逻辑
      await this.onDestroy()

      this.setStatus(ServiceStatus.DESTROYED)
      this.removeAllListeners()
      
      this.log('info', `服务 ${this._config.name} 销毁完成`)
    } catch (error) {
      this.incrementErrorCount()
      const serviceError = this.wrapError(error, 'RUNTIME')
      this.emit('error', serviceError)
      throw serviceError
    }
  }

  /**
   * 重启服务
   */
  public async restart(): Promise<void> {
    this.log('info', `重启服务 ${this._config.name}`)
    await this.stop()
    await this.start()
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<ServiceHealth> {
    const startTime = Date.now()
    
    try {
      const health = await this.onHealthCheck()
      const responseTime = Date.now() - startTime
      
      return {
        ...health,
        timestamp: new Date(),
        responseTime
      }
    } catch (error) {
      const responseTime = Date.now() - startTime
      this.incrementErrorCount()
      
      return {
        healthy: false,
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : '未知错误'
        },
        timestamp: new Date(),
        responseTime
      }
    }
  }

  /**
   * 获取服务信息
   */
  public getInfo() {
    return {
      name: this._config.name,
      version: this._config.version,
      status: this._status,
      dependencies: this._config.dependencies || [],
      config: this.config
    }
  }

  /**
   * 更新配置
   */
  public async updateConfig(config: Partial<ServiceConfig>): Promise<void> {
    const oldConfig = { ...this._config }
    this._config = { ...this._config, ...config }
    
    try {
      this.validateConfig()
      await this.onConfigUpdate(oldConfig, this._config)
      this.emit('configUpdated', { oldConfig, newConfig: this._config })
      this.log('info', `服务 ${this._config.name} 配置已更新`)
    } catch (error) {
      // 回滚配置
      this._config = oldConfig
      throw this.wrapError(error, 'CONFIGURATION')
    }
  }

  /**
   * 获取指标
   */
  public getMetrics(): ServiceMetrics {
    this.updateMetrics()
    return { ...this._metrics }
  }

  /**
   * 重置指标
   */
  public resetMetrics(): void {
    this._requestCount = 0
    this._errorCount = 0
    this._responseTimes = []
    this.setupMetrics()
    this.log('info', `服务 ${this._config.name} 指标已重置`)
  }

  /**
   * 记录请求
   */
  protected recordRequest(responseTime?: number): void {
    this._requestCount++
    if (responseTime !== undefined) {
      this._responseTimes.push(responseTime)
      // 保持最近1000个响应时间
      if (this._responseTimes.length > 1000) {
        this._responseTimes = this._responseTimes.slice(-1000)
      }
    }
  }

  /**
   * 增加错误计数
   */
  protected incrementErrorCount(): void {
    this._errorCount++
  }

  /**
   * 设置状态
   */
  protected setStatus(status: ServiceStatus): void {
    const oldStatus = this._status
    this._status = status
    this.emit('statusChanged', { oldStatus, newStatus: status })
  }

  /**
   * 创建服务错误
   */
  protected createError(
    type: ServiceError['type'],
    message: string,
    code?: string,
    recoverable = false,
    context?: Record<string, any>
  ): ServiceError {
    const error = new Error(message) as ServiceError
    error.code = code
    error.type = type
    error.recoverable = recoverable
    error.context = context
    return error
  }

  /**
   * 包装错误
   */
  protected wrapError(error: any, type: ServiceError['type']): ServiceError {
    if (error instanceof Error) {
      const serviceError = error as ServiceError
      serviceError.type = serviceError.type || type
      return serviceError
    }
    return this.createError(type, String(error))
  }

  /**
   * 日志记录
   */
  protected log(level: 'debug' | 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    // TODO: 集成日志管理器
    console[level](`[${this._config.name}] ${message}`, ...args)
  }

  /**
   * 设置指标
   */
  private setupMetrics(): void {
    this._metrics = {
      startTime: this._startTime,
      uptime: 0,
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      memoryUsage: 0,
      custom: {}
    }
  }

  /**
   * 更新指标
   */
  private updateMetrics(): void {
    this._metrics.uptime = this._startTime ? Date.now() - this._startTime.getTime() : 0
    this._metrics.requestCount = this._requestCount
    this._metrics.errorCount = this._errorCount
    this._metrics.averageResponseTime = this._responseTimes.length > 0
      ? this._responseTimes.reduce((sum, time) => sum + time, 0) / this._responseTimes.length
      : 0
    this._metrics.memoryUsage = process.memoryUsage().heapUsed
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    const interval = this._config.healthCheckInterval || 30000 // 默认30秒
    if (interval > 0) {
      this._healthCheckTimer = setInterval(async () => {
        try {
          const health = await this.healthCheck()
          this.emit('healthCheck', health)
          if (!health.healthy) {
            this.log('warn', `健康检查失败: ${health.status}`)
          }
        } catch (error) {
          this.log('error', '健康检查异常:', error)
        }
      }, interval)
    }
  }

  /**
   * 停止健康检查
   */
  private stopHealthCheck(): void {
    if (this._healthCheckTimer) {
      clearInterval(this._healthCheckTimer)
      this._healthCheckTimer = undefined
    }
  }

  /**
   * 验证配置
   */
  private validateConfig(): void {
    if (!this._config.name) {
      throw this.createError('CONFIGURATION', '服务名称不能为空')
    }
  }

  // 抽象方法，子类必须实现
  protected abstract onInitialize(): Promise<void>
  protected abstract onStart(): Promise<void>
  protected abstract onStop(): Promise<void>
  protected abstract onDestroy(): Promise<void>
  protected abstract onHealthCheck(): Promise<ServiceHealth>
  protected abstract onConfigUpdate(oldConfig: ServiceConfig, newConfig: ServiceConfig): Promise<void>
}

export default BaseService
