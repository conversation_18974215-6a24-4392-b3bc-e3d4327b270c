/**
 * 统一的服务接口规范
 * 定义所有服务必须实现的标准接口和生命周期管理
 */

import { EventEmitter } from 'events'

/**
 * 服务状态枚举
 */
export enum ServiceStatus {
  CREATED = 'created',
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error',
  DESTROYED = 'destroyed'
}

/**
 * 服务配置接口
 */
export interface ServiceConfig {
  /** 服务名称 */
  name: string
  /** 服务版本 */
  version?: string
  /** 是否启用 */
  enabled?: boolean
  /** 服务依赖 */
  dependencies?: string[]
  /** 服务配置 */
  config?: Record<string, any>
  /** 健康检查间隔（毫秒） */
  healthCheckInterval?: number
  /** 重试配置 */
  retry?: {
    maxAttempts: number
    delay: number
    backoff?: 'linear' | 'exponential'
  }
}

/**
 * 服务健康状态
 */
export interface ServiceHealth {
  /** 是否健康 */
  healthy: boolean
  /** 状态描述 */
  status: string
  /** 详细信息 */
  details?: Record<string, any>
  /** 检查时间 */
  timestamp: Date
  /** 响应时间（毫秒） */
  responseTime?: number
}

/**
 * 服务指标
 */
export interface ServiceMetrics {
  /** 启动时间 */
  startTime?: Date
  /** 运行时间（毫秒） */
  uptime?: number
  /** 请求计数 */
  requestCount?: number
  /** 错误计数 */
  errorCount?: number
  /** 平均响应时间 */
  averageResponseTime?: number
  /** 内存使用 */
  memoryUsage?: number
  /** 自定义指标 */
  custom?: Record<string, any>
}

/**
 * 服务错误接口
 */
export interface ServiceError extends Error {
  /** 错误代码 */
  code?: string
  /** 错误类型 */
  type?: 'INITIALIZATION' | 'RUNTIME' | 'CONFIGURATION' | 'DEPENDENCY' | 'NETWORK'
  /** 是否可恢复 */
  recoverable?: boolean
  /** 错误上下文 */
  context?: Record<string, any>
}

/**
 * 基础服务接口
 */
export interface IBaseService extends EventEmitter {
  /** 服务配置 */
  readonly config: ServiceConfig
  /** 服务状态 */
  readonly status: ServiceStatus
  /** 服务指标 */
  readonly metrics: ServiceMetrics

  /**
   * 初始化服务
   * @param config 服务配置
   */
  initialize(config?: Partial<ServiceConfig>): Promise<void>

  /**
   * 启动服务
   */
  start(): Promise<void>

  /**
   * 停止服务
   */
  stop(): Promise<void>

  /**
   * 销毁服务
   */
  destroy(): Promise<void>

  /**
   * 重启服务
   */
  restart(): Promise<void>

  /**
   * 健康检查
   */
  healthCheck(): Promise<ServiceHealth>

  /**
   * 获取服务信息
   */
  getInfo(): {
    name: string
    version?: string
    status: ServiceStatus
    dependencies: string[]
    config: ServiceConfig
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  updateConfig(config: Partial<ServiceConfig>): Promise<void>

  /**
   * 获取指标
   */
  getMetrics(): ServiceMetrics

  /**
   * 重置指标
   */
  resetMetrics(): void
}

/**
 * 数据库服务接口
 */
export interface IDatabaseService extends IBaseService {
  /**
   * 获取数据
   * @param id 数据ID
   */
  get(id: string): Promise<any>

  /**
   * 保存数据
   * @param doc 文档数据
   */
  put(doc: any): Promise<any>

  /**
   * 删除数据
   * @param docOrId 文档或ID
   */
  remove(docOrId: any): Promise<any>

  /**
   * 批量操作
   * @param docs 文档数组
   */
  bulkDocs(docs: any[]): Promise<any[]>

  /**
   * 查询所有文档
   * @param options 查询选项
   */
  allDocs(options?: any): Promise<any[]>

  /**
   * 清空数据库
   */
  clear(): Promise<void>

  /**
   * 执行事务
   * @param callback 事务回调
   */
  transaction<T>(callback: () => Promise<T>): Promise<T>
}

/**
 * 存储服务接口
 */
export interface IStorageService extends IBaseService {
  /**
   * 获取存储值
   * @param key 键
   * @param defaultValue 默认值
   */
  get<T = any>(key: string, defaultValue?: T): T | undefined

  /**
   * 设置存储值
   * @param key 键
   * @param value 值
   */
  set(key: string, value: any): void

  /**
   * 删除存储值
   * @param key 键
   */
  delete(key: string): void

  /**
   * 检查键是否存在
   * @param key 键
   */
  has(key: string): boolean

  /**
   * 清空存储
   */
  clear(): void

  /**
   * 获取所有键
   */
  keys(): string[]

  /**
   * 获取存储大小
   */
  size(): number
}

/**
 * 剪贴板服务接口
 */
export interface IClipboardService extends IBaseService {
  /**
   * 获取剪贴板数据
   */
  getData(): Promise<ClipboardData>

  /**
   * 设置剪贴板数据
   * @param data 剪贴板数据
   */
  setData(data: ClipboardData): Promise<void>

  /**
   * 清空剪贴板
   */
  clear(): Promise<void>

  /**
   * 检查是否有数据
   */
  hasData(): Promise<boolean>

  /**
   * 监听剪贴板变化
   * @param callback 回调函数
   */
  watch(callback: (data: ClipboardData) => void): () => void

  /**
   * 获取剪贴板历史
   */
  getHistory(): ClipboardData[]

  /**
   * 清空历史记录
   */
  clearHistory(): void
}



/**
 * 窗口服务接口
 */
export interface IWindowService extends IBaseService {
  /**
   * 创建窗口
   * @param options 窗口选项
   */
  createWindow(options: any): Promise<string>

  /**
   * 获取窗口信息
   * @param windowId 窗口ID
   */
  getWindowInfo(windowId: string): Promise<any>

  /**
   * 获取所有窗口
   */
  getAllWindows(): Promise<any[]>

  /**
   * 关闭窗口
   * @param windowId 窗口ID
   */
  closeWindow(windowId: string): Promise<void>

  /**
   * 显示窗口
   * @param windowId 窗口ID
   */
  showWindow(windowId: string): Promise<void>

  /**
   * 隐藏窗口
   * @param windowId 窗口ID
   */
  hideWindow(windowId: string): Promise<void>

  /**
   * 设置窗口边界
   * @param windowId 窗口ID
   * @param bounds 边界信息
   */
  setWindowBounds(windowId: string, bounds: any): Promise<void>

  /**
   * 获取窗口边界
   * @param windowId 窗口ID
   */
  getWindowBounds(windowId: string): Promise<any>
}

/**
 * 剪贴板数据接口
 */
export interface ClipboardData {
  text?: string
  image?: Buffer | string
  files?: string[]
  html?: string
  contentType: 'text' | 'image' | 'files' | 'html' | 'unknown'
  timestamp?: Date
  source?: string
}
