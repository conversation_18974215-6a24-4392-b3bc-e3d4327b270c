/**
 * 窗口服务
 * 提供窗口创建、状态管理、事件处理等功能，符合新的服务规范
 * 注意：这个服务与WindowManager不同，主要提供IPC接口给渲染进程使用
 */

import { BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import BaseService from '../base/base-service'
import type {
  IWindowService,
  ServiceConfig,
  ServiceHealth
} from '../base/service-interface'
import {
  IPC_WINDOW_CREATE,
  IPC_WINDOW_GET_INFO,
  IPC_WINDOW_GET_ALL,
  IPC_WINDOW_GET_CURRENT,
  IPC_WINDOW_CLOSE,
  IPC_WINDOW_HIDE,
  IPC_WINDOW_SHOW,
  IPC_WINDOW_MINIMIZE,
  IPC_WINDOW_MAXIMIZE,
  IPC_WINDOW_RESTORE,
  IPC_WINDOW_FOCUS,
  IPC_WINDOW_SET_BOUNDS,
  IPC_WINDOW_GET_BOUNDS,
  IPC_WINDOW_SET_ALWAYS_ON_TOP,
  IPC_WINDOW_GET_STATUS
} from '@shared/ipc-common'


/**
 * 窗口服务配置
 */
export interface WindowServiceConfig extends ServiceConfig {
  /** 默认窗口选项 */
  defaultOptions?: Electron.BrowserWindowConstructorOptions
  /** 窗口限制 */
  limits?: {
    maxWindows?: number
    minWidth?: number
    minHeight?: number
    maxWidth?: number
    maxHeight?: number
  }
  /** 窗口管理 */
  management?: {
    autoCleanup?: boolean
    trackFocus?: boolean
    saveState?: boolean
  }
}

/**
 * 窗口信息接口
 */
export interface WindowInfo {
  id: number
  title: string
  visible: boolean
  minimized: boolean
  maximized: boolean
  fullscreen: boolean
  focused: boolean
  bounds: Electron.Rectangle
  type: string
  url: string
  createdAt: Date
  lastActiveAt: Date
}

/**
 * 窗口创建选项
 */
export interface WindowCreateOptions extends Electron.BrowserWindowConstructorOptions {
  /** 窗口类型 */
  type?: string
  /** 加载的URL或文件路径 */
  url?: string
  /** 是否自动显示 */
  autoShow?: boolean
  /** 窗口数据 */
  data?: any
}

/**
 * 窗口操作结果
 */
export interface WindowOperationResult {
  success: boolean
  data?: any
  error?: string
}

/**
 * 窗口服务实现
 */
export class WindowService extends BaseService implements IWindowService {
  private windows = new Map<number, BrowserWindow>()
  private windowMetadata = new Map<number, {
    type: string
    createdAt: Date
    lastActiveAt: Date
    data?: any
  }>()
  private ipcHandlers: string[] = []
  private focusedWindowId?: number

  constructor(config: WindowServiceConfig) {
    super({
      name: 'window-service',
      version: '1.0.0',
      dependencies: [],
      healthCheckInterval: 60000,
      defaultOptions: {
        width: 800,
        height: 600,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          enableRemoteModule: false,
          webSecurity: true
        }
      },
      limits: {
        maxWindows: 50,
        minWidth: 300,
        minHeight: 200,
        maxWidth: 4000,
        maxHeight: 3000
      },
      management: {
        autoCleanup: true,
        trackFocus: true,
        saveState: false
      },
      ...config
    })
  }

  /**
   * 创建窗口
   */
  public async createWindow(options: WindowCreateOptions): Promise<string> {
    const startTime = Date.now()
    
    try {
      // 检查窗口数量限制
      if (this.windows.size >= (this._config.limits?.maxWindows || 50)) {
        throw this.createError('RUNTIME', '窗口数量已达到上限')
      }

      // 合并默认选项
      const windowOptions: Electron.BrowserWindowConstructorOptions = {
        ...this._config.defaultOptions,
        ...options
      }

      // 应用尺寸限制
      this.applyLimits(windowOptions)

      // 创建窗口
      const window = new BrowserWindow(windowOptions)
      const windowId = window.id

      // 存储窗口引用和元数据
      this.windows.set(windowId, window)
      this.windowMetadata.set(windowId, {
        type: options.type || 'unknown',
        createdAt: new Date(),
        lastActiveAt: new Date(),
        data: options.data
      })

      // 设置窗口事件监听
      this.setupWindowEvents(window)

      // 加载内容
      if (options.url) {
        if (options.url.startsWith('http')) {
          await window.loadURL(options.url)
        } else {
          await window.loadFile(options.url)
        }
      } else {
        // 加载默认页面
        const indexPath = join(__dirname, '..', '..', '..', 'renderer', 'index.html')
        await window.loadFile(indexPath)
      }

      // 自动显示
      if (options.autoShow !== false) {
        window.show()
      }

      this.recordRequest(Date.now() - startTime)
      this.log('info', `窗口创建成功: ${windowId} (${options.type || 'unknown'})`)
      
      return windowId.toString()
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 获取窗口信息
   */
  public async getWindowInfo(windowId: string): Promise<WindowInfo | null> {
    const startTime = Date.now()
    
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)
      const metadata = this.windowMetadata.get(id)

      if (!window || !metadata) {
        this.recordRequest(Date.now() - startTime)
        return null
      }

      const info: WindowInfo = {
        id,
        title: window.getTitle(),
        visible: window.isVisible(),
        minimized: window.isMinimized(),
        maximized: window.isMaximized(),
        fullscreen: window.isFullScreen(),
        focused: window.isFocused(),
        bounds: window.getBounds(),
        type: metadata.type,
        url: window.webContents.getURL(),
        createdAt: metadata.createdAt,
        lastActiveAt: metadata.lastActiveAt
      }

      this.recordRequest(Date.now() - startTime)
      return info
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 获取所有窗口
   */
  public async getAllWindows(): Promise<WindowInfo[]> {
    const startTime = Date.now()
    
    try {
      const windows: WindowInfo[] = []
      
      for (const [id, window] of this.windows) {
        const metadata = this.windowMetadata.get(id)
        if (metadata) {
          windows.push({
            id,
            title: window.getTitle(),
            visible: window.isVisible(),
            minimized: window.isMinimized(),
            maximized: window.isMaximized(),
            fullscreen: window.isFullScreen(),
            focused: window.isFocused(),
            bounds: window.getBounds(),
            type: metadata.type,
            url: window.webContents.getURL(),
            createdAt: metadata.createdAt,
            lastActiveAt: metadata.lastActiveAt
          })
        }
      }

      this.recordRequest(Date.now() - startTime)
      return windows
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 关闭窗口
   */
  public async closeWindow(windowId: string): Promise<void> {
    const startTime = Date.now()
    
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)

      if (window) {
        window.close()
        // 清理会在窗口关闭事件中处理
      }

      this.recordRequest(Date.now() - startTime)
      this.log('info', `窗口关闭: ${windowId}`)
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 显示窗口
   */
  public async showWindow(windowId: string): Promise<void> {
    const startTime = Date.now()
    
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)

      if (window) {
        window.show()
        this.updateLastActive(id)
      }

      this.recordRequest(Date.now() - startTime)
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 隐藏窗口
   */
  public async hideWindow(windowId: string): Promise<void> {
    const startTime = Date.now()
    
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)

      if (window) {
        window.hide()
      }

      this.recordRequest(Date.now() - startTime)
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 设置窗口边界
   */
  public async setWindowBounds(windowId: string, bounds: Electron.Rectangle): Promise<void> {
    const startTime = Date.now()
    
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)

      if (window) {
        // 应用尺寸限制
        const limitedBounds = this.applyBoundsLimits(bounds)
        window.setBounds(limitedBounds)
        this.updateLastActive(id)
      }

      this.recordRequest(Date.now() - startTime)
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 获取窗口边界
   */
  public async getWindowBounds(windowId: string): Promise<Electron.Rectangle | null> {
    const startTime = Date.now()
    
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)

      if (window) {
        const bounds = window.getBounds()
        this.recordRequest(Date.now() - startTime)
        return bounds
      }

      this.recordRequest(Date.now() - startTime)
      return null
    } catch (error) {
      this.recordRequest(Date.now() - startTime)
      this.incrementErrorCount()
      throw this.wrapError(error, 'RUNTIME')
    }
  }

  /**
   * 初始化服务
   */
  protected async onInitialize(): Promise<void> {
    this.log('info', '正在初始化窗口服务...')

    // 设置IPC处理器
    this.setupIpcHandlers()

    // 监听现有窗口
    this.trackExistingWindows()

    this.log('info', '窗口服务初始化完成')
  }

  /**
   * 启动服务
   */
  protected async onStart(): Promise<void> {
    this.log('info', '正在启动窗口服务...')

    // 启动自动清理任务（如果启用）
    if (this._config.management?.autoCleanup) {
      this.startCleanupTask()
    }

    this.log('info', '窗口服务启动完成')
  }

  /**
   * 停止服务
   */
  protected async onStop(): Promise<void> {
    this.log('info', '正在停止窗口服务...')

    // 停止清理任务
    this.stopCleanupTask()

    // 清理IPC处理器
    this.cleanupIpcHandlers()

    this.log('info', '窗口服务停止完成')
  }

  /**
   * 销毁服务
   */
  protected async onDestroy(): Promise<void> {
    this.log('info', '正在销毁窗口服务...')

    // 清理窗口引用
    this.windows.clear()
    this.windowMetadata.clear()

    this.log('info', '窗口服务销毁完成')
  }

  /**
   * 健康检查
   */
  protected async onHealthCheck(): Promise<ServiceHealth> {
    const details: Record<string, any> = {}
    let healthy = true
    let status = 'healthy'

    try {
      // 检查窗口数量
      details.windowCount = this.windows.size
      details.maxWindows = this._config.limits?.maxWindows

      // 检查是否有僵尸窗口
      let zombieCount = 0
      for (const [id, window] of this.windows) {
        if (window.isDestroyed()) {
          zombieCount++
        }
      }
      details.zombieWindows = zombieCount

      if (zombieCount > 0) {
        healthy = false
        status = `found ${zombieCount} zombie windows`
      }

      // 检查IPC处理器
      details.ipcHandlers = this.ipcHandlers.length

      // 检查焦点窗口
      if (this._config.management?.trackFocus) {
        details.focusedWindow = this.focusedWindowId
      }

      if (healthy) {
        status = 'all systems operational'
      }

    } catch (error) {
      healthy = false
      status = 'health check failed'
      details.error = error instanceof Error ? error.message : '未知错误'
    }

    return {
      healthy,
      status,
      details,
      timestamp: new Date()
    }
  }

  /**
   * 配置更新处理
   */
  protected async onConfigUpdate(oldConfig: ServiceConfig, newConfig: ServiceConfig): Promise<void> {
    this.log('info', '更新窗口服务配置')

    const oldWindowConfig = oldConfig as WindowServiceConfig
    const newWindowConfig = newConfig as WindowServiceConfig

    // 如果自动清理配置发生变化
    if (oldWindowConfig.management?.autoCleanup !== newWindowConfig.management?.autoCleanup) {
      if (newWindowConfig.management?.autoCleanup) {
        this.startCleanupTask()
      } else {
        this.stopCleanupTask()
      }
    }
  }

  /**
   * 应用尺寸限制
   */
  private applyLimits(options: Electron.BrowserWindowConstructorOptions): void {
    const windowConfig = this._config as WindowServiceConfig
    const limits = windowConfig.limits
    if (!limits) return

    if (limits.minWidth && options.width && options.width < limits.minWidth) {
      options.width = limits.minWidth
    }
    if (limits.minHeight && options.height && options.height < limits.minHeight) {
      options.height = limits.minHeight
    }
    if (limits.maxWidth && options.width && options.width > limits.maxWidth) {
      options.width = limits.maxWidth
    }
    if (limits.maxHeight && options.height && options.height > limits.maxHeight) {
      options.height = limits.maxHeight
    }

    options.minWidth = limits.minWidth
    options.minHeight = limits.minHeight
    options.maxWidth = limits.maxWidth
    options.maxHeight = limits.maxHeight
  }

  /**
   * 应用边界限制
   */
  private applyBoundsLimits(bounds: Electron.Rectangle): Electron.Rectangle {
    const windowConfig = this._config as WindowServiceConfig
    const limits = windowConfig.limits
    if (!limits) return bounds

    const result = { ...bounds }

    if (limits.minWidth && result.width < limits.minWidth) {
      result.width = limits.minWidth
    }
    if (limits.minHeight && result.height < limits.minHeight) {
      result.height = limits.minHeight
    }
    if (limits.maxWidth && result.width > limits.maxWidth) {
      result.width = limits.maxWidth
    }
    if (limits.maxHeight && result.height > limits.maxHeight) {
      result.height = limits.maxHeight
    }

    return result
  }

  /**
   * 设置窗口事件监听
   */
  private setupWindowEvents(window: BrowserWindow): void {
    const windowId = window.id

    window.on('closed', () => {
      this.windows.delete(windowId)
      this.windowMetadata.delete(windowId)
      this.log('debug', `窗口已关闭: ${windowId}`)
    })

    const windowConfig = this._config as WindowServiceConfig
    if (windowConfig.management?.trackFocus) {
      window.on('focus', () => {
        this.focusedWindowId = windowId
        this.updateLastActive(windowId)
      })

      window.on('blur', () => {
        if (this.focusedWindowId === windowId) {
          this.focusedWindowId = undefined
        }
      })
    }

    window.on('show', () => {
      this.updateLastActive(windowId)
    })

    window.on('hide', () => {
      // 窗口隐藏时不更新活动时间
    })
  }

  /**
   * 更新最后活动时间
   */
  private updateLastActive(windowId: number): void {
    const metadata = this.windowMetadata.get(windowId)
    if (metadata) {
      metadata.lastActiveAt = new Date()
    }
  }

  /**
   * 监听现有窗口
   */
  private trackExistingWindows(): void {
    const existingWindows = BrowserWindow.getAllWindows()
    for (const window of existingWindows) {
      if (!this.windows.has(window.id)) {
        this.windows.set(window.id, window)
        this.windowMetadata.set(window.id, {
          type: 'existing',
          createdAt: new Date(),
          lastActiveAt: new Date()
        })
        this.setupWindowEvents(window)
      }
    }
    this.log('info', `已跟踪 ${existingWindows.length} 个现有窗口`)
  }

  /**
   * 启动清理任务
   */
  private startCleanupTask(): void {
    // TODO: 实现定期清理僵尸窗口的任务
    this.log('info', '窗口清理任务已启动')
  }

  /**
   * 停止清理任务
   */
  private stopCleanupTask(): void {
    // TODO: 停止清理任务
    this.log('info', '窗口清理任务已停止')
  }

  /**
   * 设置IPC处理器
   */
  private setupIpcHandlers(): void {
    const handlers = [
      { channel: IPC_WINDOW_CREATE, handler: this.handleCreateWindow.bind(this) },
      { channel: IPC_WINDOW_GET_INFO, handler: this.handleGetWindowInfo.bind(this) },
      { channel: IPC_WINDOW_GET_ALL, handler: this.handleGetAllWindows.bind(this) },
      { channel: IPC_WINDOW_GET_CURRENT, handler: this.handleGetCurrentWindow.bind(this) },
      { channel: IPC_WINDOW_CLOSE, handler: this.handleCloseWindow.bind(this) },
      { channel: IPC_WINDOW_HIDE, handler: this.handleHideWindow.bind(this) },
      { channel: IPC_WINDOW_SHOW, handler: this.handleShowWindow.bind(this) },
      { channel: IPC_WINDOW_MINIMIZE, handler: this.handleMinimizeWindow.bind(this) },
      { channel: IPC_WINDOW_MAXIMIZE, handler: this.handleMaximizeWindow.bind(this) },
      { channel: IPC_WINDOW_RESTORE, handler: this.handleRestoreWindow.bind(this) },
      { channel: IPC_WINDOW_FOCUS, handler: this.handleFocusWindow.bind(this) },
      { channel: IPC_WINDOW_SET_BOUNDS, handler: this.handleSetWindowBounds.bind(this) },
      { channel: IPC_WINDOW_GET_BOUNDS, handler: this.handleGetWindowBounds.bind(this) },
      { channel: IPC_WINDOW_SET_ALWAYS_ON_TOP, handler: this.handleSetAlwaysOnTop.bind(this) },
      { channel: IPC_WINDOW_GET_STATUS, handler: this.handleGetWindowStatus.bind(this) },
    ]

    for (const { channel, handler } of handlers) {
      ipcMain.handle(channel, handler)
      this.ipcHandlers.push(channel)
    }

    this.log('info', `已注册 ${this.ipcHandlers.length} 个IPC处理器`)
  }

  /**
   * 清理IPC处理器
   */
  private cleanupIpcHandlers(): void {
    for (const channel of this.ipcHandlers) {
      ipcMain.removeHandler(channel)
    }
    this.ipcHandlers = []
    this.log('info', '已清理所有IPC处理器')
  }

  // IPC处理器方法
  private async handleCreateWindow(_event: any, options: WindowCreateOptions): Promise<WindowOperationResult> {
    try {
      const windowId = await this.createWindow(options)
      return { success: true, data: windowId }
    } catch (error) {
      this.log('error', 'IPC处理失败 - createWindow:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleGetWindowInfo(_event: any, windowId: string): Promise<WindowOperationResult> {
    try {
      const info = await this.getWindowInfo(windowId)
      return { success: true, data: info }
    } catch (error) {
      this.log('error', 'IPC处理失败 - getWindowInfo:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleGetAllWindows(_event: any): Promise<WindowOperationResult> {
    try {
      const windows = await this.getAllWindows()
      return { success: true, data: windows }
    } catch (error) {
      this.log('error', 'IPC处理失败 - getAllWindows:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleGetCurrentWindow(event: any): Promise<WindowOperationResult> {
    try {
      const window = BrowserWindow.fromWebContents(event.sender)
      if (window) {
        const info = await this.getWindowInfo(window.id.toString())
        return { success: true, data: info }
      }
      return { success: false, error: '无法获取当前窗口' }
    } catch (error) {
      this.log('error', 'IPC处理失败 - getCurrentWindow:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleCloseWindow(_event: any, windowId: string): Promise<WindowOperationResult> {
    try {
      await this.closeWindow(windowId)
      return { success: true }
    } catch (error) {
      this.log('error', 'IPC处理失败 - closeWindow:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleHideWindow(_event: any, windowId: string): Promise<WindowOperationResult> {
    try {
      await this.hideWindow(windowId)
      return { success: true }
    } catch (error) {
      this.log('error', 'IPC处理失败 - hideWindow:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleShowWindow(_event: any, windowId: string): Promise<WindowOperationResult> {
    try {
      await this.showWindow(windowId)
      return { success: true }
    } catch (error) {
      this.log('error', 'IPC处理失败 - showWindow:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleMinimizeWindow(_event: any, windowId: string): Promise<WindowOperationResult> {
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)
      if (window) {
        window.minimize()
        return { success: true }
      }
      return { success: false, error: '窗口不存在' }
    } catch (error) {
      this.log('error', 'IPC处理失败 - minimizeWindow:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleMaximizeWindow(_event: any, windowId: string): Promise<WindowOperationResult> {
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)
      if (window) {
        window.maximize()
        return { success: true }
      }
      return { success: false, error: '窗口不存在' }
    } catch (error) {
      this.log('error', 'IPC处理失败 - maximizeWindow:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleRestoreWindow(_event: any, windowId: string): Promise<WindowOperationResult> {
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)
      if (window) {
        window.restore()
        return { success: true }
      }
      return { success: false, error: '窗口不存在' }
    } catch (error) {
      this.log('error', 'IPC处理失败 - restoreWindow:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleFocusWindow(_event: any, windowId: string): Promise<WindowOperationResult> {
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)
      if (window) {
        window.focus()
        this.updateLastActive(id)
        return { success: true }
      }
      return { success: false, error: '窗口不存在' }
    } catch (error) {
      this.log('error', 'IPC处理失败 - focusWindow:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleSetWindowBounds(_event: any, windowId: string, bounds: Electron.Rectangle): Promise<WindowOperationResult> {
    try {
      await this.setWindowBounds(windowId, bounds)
      return { success: true }
    } catch (error) {
      this.log('error', 'IPC处理失败 - setWindowBounds:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleGetWindowBounds(_event: any, windowId: string): Promise<WindowOperationResult> {
    try {
      const bounds = await this.getWindowBounds(windowId)
      return { success: true, data: bounds }
    } catch (error) {
      this.log('error', 'IPC处理失败 - getWindowBounds:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleSetAlwaysOnTop(_event: any, windowId: string, flag: boolean): Promise<WindowOperationResult> {
    try {
      const id = parseInt(windowId)
      const window = this.windows.get(id)
      if (window) {
        window.setAlwaysOnTop(flag)
        return { success: true }
      }
      return { success: false, error: '窗口不存在' }
    } catch (error) {
      this.log('error', 'IPC处理失败 - setAlwaysOnTop:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  private async handleGetWindowStatus(_event: any): Promise<WindowOperationResult> {
    try {
      const windowConfig = this._config as WindowServiceConfig
      const status = {
        windowCount: this.windows.size,
        focusedWindow: this.focusedWindowId,
        maxWindows: windowConfig.limits?.maxWindows
      }
      return { success: true, data: status }
    } catch (error) {
      this.log('error', 'IPC处理失败 - getWindowStatus:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }
}

export default WindowService
