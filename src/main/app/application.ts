/**
 * 应用管理器
 * 负责应用的整体生命周期管理和核心组件协调
 */
import { app, globalShortcut } from 'electron'
import { EventEmitter } from 'events'
import type { ConfigManager } from '@main/core/config/config-manager'
import type { LogManager } from '@main/core/logging/log-manager'
import type { ServiceManager } from '@main/core/service/service-manager'
import type { WindowManager } from '@main/windows/manager/window-manager'
import type { ErrorHandler } from '@main/core/error/error-handler'
import { EncodingUtils } from '@main/shared/utils/encoding-utils'

/**
 * 应用状态枚举
 */
export enum ApplicationState {
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error',
}

/**
 * 应用配置接口
 */
export interface ApplicationConfig {
  name: string
  version: string
  isDevelopment: boolean
  shortcuts: Record<string, string>
}

/**
 * 应用管理器类
 */
export class Application extends EventEmitter {
  private static instance: Application | null = null
  private state: ApplicationState = ApplicationState.INITIALIZING
  private config: ApplicationConfig | null = null

  // 核心组件
  private configManager?: ConfigManager
  private logManager?: LogManager
  private serviceManager?: ServiceManager
  private windowManager?: WindowManager
  private errorHandler?: ErrorHandler

  /**
   * 私有构造函数，确保单例模式
   */
  private constructor() {
    super()
    // 初始化编码设置
    EncodingUtils.initialize()
    this.setupElectronEvents()
  }

  /**
   * 获取应用管理器实例（单例）
   */
  public static getInstance(): Application {
    if (!Application.instance) {
      Application.instance = new Application()
    }
    return Application.instance
  }

  /**
   * 初始化应用
   */
  public async initialize(): Promise<void> {
    try {
      this.setState(ApplicationState.INITIALIZING)
      this.log('info', '开始初始化应用...')

      // 1. 初始化错误处理器
      await this.initializeErrorHandler()

      // 2. 初始化配置管理器
      await this.initializeConfigManager()

      // 3. 初始化日志管理器
      await this.initializeLogManager()

      // 4. 初始化服务管理器
      await this.initializeServiceManager()

      // 5. 初始化窗口管理器
      await this.initializeWindowManager()

      // 6. 注册全局快捷键
      this.registerGlobalShortcuts()

      this.setState(ApplicationState.INITIALIZED)
      this.log('info', '应用初始化完成')
      this.emit('initialized')
    } catch (error) {
      this.setState(ApplicationState.ERROR)
      this.log('error', '应用初始化失败:', error)
      this.emit('error', error)
      throw error
    }
  }

  /**
   * 启动应用
   */
  public async start(): Promise<void> {
    if (this.state !== ApplicationState.INITIALIZED) {
      throw new Error('应用未正确初始化')
    }

    try {
      this.setState(ApplicationState.STARTING)
      this.log('info', '启动应用...')

      // 启动所有服务
      if (this.serviceManager) {
        await this.serviceManager.startAll()
      }

      this.setState(ApplicationState.RUNNING)
      this.log('info', '应用启动完成')
      this.emit('started')
    } catch (error) {
      this.setState(ApplicationState.ERROR)
      this.log('error', '应用启动失败:', error)
      this.emit('error', error)
      throw error
    }
  }

  /**
   * 停止应用
   */
  public async shutdown(): Promise<void> {
    try {
      this.setState(ApplicationState.STOPPING)
      this.log('info', '关闭应用...')

      // 注销全局快捷键
      globalShortcut.unregisterAll()

      // 停止所有服务
      if (this.serviceManager) {
        await this.serviceManager.stopAll()
      }

      // 销毁窗口管理器
      if (this.windowManager) {
        await this.windowManager.destroy()
      }

      // 销毁其他组件
      if (this.logManager) {
        await this.logManager.destroy()
      }

      if (this.configManager) {
        await this.configManager.destroy()
      }

      this.setState(ApplicationState.STOPPED)
      this.log('info', '应用关闭完成')
      this.emit('stopped')
    } catch (error) {
      this.setState(ApplicationState.ERROR)
      this.log('error', '应用关闭失败:', error)
      this.emit('error', error)
      throw error
    }
  }

  /**
   * 获取应用状态
   */
  public getState(): ApplicationState {
    return this.state
  }

  /**
   * 获取应用配置
   */
  public getConfig(): ApplicationConfig | null {
    return this.config
  }

  /**
   * 获取配置管理器
   */
  public getConfigManager(): ConfigManager | undefined {
    return this.configManager
  }

  /**
   * 获取日志管理器
   */
  public getLogManager(): LogManager | undefined {
    return this.logManager
  }

  /**
   * 获取服务管理器
   */
  public getServiceManager(): ServiceManager | undefined {
    return this.serviceManager
  }

  /**
   * 获取窗口管理器
   */
  public getWindowManager(): WindowManager | undefined {
    return this.windowManager
  }

  /**
   * 设置应用状态
   */
  private setState(state: ApplicationState): void {
    const oldState = this.state
    this.state = state
    this.emit('stateChanged', { oldState, newState: state })
  }

  /**
   * 设置Electron事件监听
   */
  private setupElectronEvents(): void {
    app.whenReady().then(() => {
      this.initialize().catch(error => {
        console.error('应用初始化失败:', error)
        app.quit()
      })
    })

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        this.shutdown().then(() => {
          app.quit()
        })
      }
    })

    app.on('activate', () => {
      if (this.windowManager) {
        this.windowManager.showMainWindow().catch(error => {
          this.log('error', '显示主窗口失败:', error)
        })
      }
    })

    app.on('before-quit', async event => {
      if (this.state === ApplicationState.RUNNING) {
        event.preventDefault()
        await this.shutdown()
        app.quit()
      }
    })
  }

  /**
   * 初始化错误处理器
   */
  private async initializeErrorHandler(): Promise<void> {
    const { ErrorHandler } = await import('../core/error/error-handler')
    this.errorHandler = ErrorHandler.getInstance()
    this.errorHandler.setupGlobalErrorHandlers()
    this.log('info', '错误处理器初始化完成')
  }

  /**
   * 初始化配置管理器
   */
  private async initializeConfigManager(): Promise<void> {
    const { ConfigManager } = await import('../core/config/config-manager')
    this.configManager = new ConfigManager()
    await this.configManager.initialize()

    // 更新应用配置
    this.config = {
      name: this.configManager.get('app.name', 'Smart Toolbox'),
      version: this.configManager.get('app.version', '1.0.0'),
      isDevelopment: this.configManager.get('app.isDevelopment', false),
      shortcuts: this.configManager.get('shortcuts', {}),
    }

    this.log('info', '配置管理器初始化完成')
  }

  /**
   * 初始化日志管理器
   */
  private async initializeLogManager(): Promise<void> {
    const { LogManager, LogLevel } = await import('../core/logging/log-manager')
    this.logManager = new LogManager()

    // 获取日志级别配置并映射到 LogLevel 枚举
    const logLevelConfig = this.configManager?.get(
      'app.logLevel',
      'info'
    ) as string
    const logLevelMap: Record<
      string,
      (typeof LogLevel)[keyof typeof LogLevel]
    > = {
      debug: LogLevel.DEBUG,
      info: LogLevel.INFO,
      warn: LogLevel.WARN,
      error: LogLevel.ERROR,
    }

    const logConfig = {
      level: logLevelMap[logLevelConfig] || LogLevel.INFO,
      enableConsole: this.configManager?.get('app.isDevelopment', true),
      enableFile: true,
    }

    await this.logManager.initialize(logConfig)

    // 设置错误处理器的日志管理器
    if (this.errorHandler) {
      this.errorHandler.setLogManager(this.logManager)
    }

    this.log('info', '日志管理器初始化完成')
  }

  /**
   * 初始化服务管理器
   */
  private async initializeServiceManager(): Promise<void> {
    const { ServiceManager } = await import('../core/service/service-manager')
    this.serviceManager = new ServiceManager()

    // 注册服务
    await this.registerServices()

    // 初始化所有服务
    await this.serviceManager.initialize()

    this.log('info', '服务管理器初始化完成')
  }

  /**
   * 初始化窗口管理器
   */
  private async initializeWindowManager(): Promise<void> {
    const { WindowManager } = await import('../windows/manager/window-manager')
    this.windowManager = new WindowManager()
    await this.windowManager.initialize()
    this.log('info', '窗口管理器初始化完成')
  }

  /**
   * 注册服务
   */
  private async registerServices(): Promise<void> {
    if (!this.serviceManager) {
      throw new Error('服务管理器未初始化')
    }

    // 导入服务适配器
    const { ServiceAdapters } = await import('../core/service/service-adapters')

    // 注册服务（按依赖顺序）
    const adapters = new ServiceAdapters()
    await adapters.registerAll(this.serviceManager)

    this.log('info', '服务注册完成')
  }

  /**
   * 注册全局快捷键
   */
  private registerGlobalShortcuts(): void {
    if (!this.config?.shortcuts) {
      return
    }

    for (const [action, shortcut] of Object.entries(this.config.shortcuts)) {
      try {
        globalShortcut.register(shortcut, () => {
          this.emit('shortcut', action)
          this.handleShortcut(action)
        })
        this.log('info', `注册快捷键: ${action} -> ${shortcut}`)
      } catch (error) {
        this.log('error', `注册快捷键失败: ${action} -> ${shortcut}`, error)
      }
    }
  }

  /**
   * 处理快捷键
   */
  private handleShortcut(action: string): void {
    if (!this.windowManager) {
      this.log('warn', '窗口管理器未初始化')
      return
    }

    switch (action) {
      case 'show-toolbar':
        this.windowManager.showToolbar().catch(error => {
          this.log('error', '显示工具栏失败:', error)
        })
        break

      case 'show-search':
        this.windowManager.showSearchWindow().catch(error => {
          this.log('error', '显示搜索窗口失败:', error)
        })
        break

      case 'show-main':
        this.windowManager.showMainWindow().catch(error => {
          this.log('error', '显示主窗口失败:', error)
        })
        break

      default:
        this.log('warn', `未知的快捷键动作: ${action}`)
    }
  }

  /**
   * 日志记录
   */
  private log(
    level: 'info' | 'warn' | 'error',
    message: string,
    ...args: any[]
  ): void {
    if (this.logManager) {
      // 使用 LogManager 的便捷方法，避免硬编码数字
      switch (level) {
        case 'info':
          this.logManager.info('Application', message, ...args)
          break
        case 'warn':
          this.logManager.warn('Application', message, ...args)
          break
        case 'error':
          this.logManager.error('Application', message, ...args)
          break
      }
    } else {
      // 使用安全的控制台输出
      EncodingUtils.safeConsoleLog(level, `[Application] ${message}`, ...args)
    }
  }
}

export default Application
