/**
 * 应用常量定义
 * 定义应用层面的常量和配置值
 */

/**
 * 应用基本信息
 */
export const APP_INFO = {
  NAME: 'Smart Toolbox',
  VERSION: '1.0.0',
  DESCRIPTION: '智能工具箱 - 提升工作效率的桌面应用',
  AUTHOR: 'Smart Toolbox Team',
  HOMEPAGE: 'https://smart-toolbox.com',
  REPOSITORY: 'https://github.com/smart-toolbox/smart-toolbox',
  LICENSE: 'MIT'
} as const

/**
 * 应用路径常量
 */
export const APP_PATHS = {
  CONFIG_FILE: 'config.json',
  LOG_DIR: 'logs',
  CACHE_DIR: 'cache',
  TEMP_DIR: 'temp',
  BACKUP_DIR: 'backups',
  PLUGINS_DIR: 'plugins',
  THEMES_DIR: 'themes',
  DATA_DIR: 'data',
  STORAGE_DIR: 'storage'
} as const

/**
 * 应用配置键
 */
export const CONFIG_KEYS = {
  APP: {
    NAME: 'app.name',
    VERSION: 'app.version',
    ENVIRONMENT: 'app.environment',
    DEBUG: 'app.debug',
    LOG_LEVEL: 'app.logLevel',
    DATA_PATH: 'app.dataPath',
    AUTO_START: 'app.autoStart',
    MINIMIZE_TO_TRAY: 'app.minimizeToTray',
    CLOSE_TO_TRAY: 'app.closeToTray'
  },
  WINDOW: {
    MAIN: {
      WIDTH: 'window.main.width',
      HEIGHT: 'window.main.height',
      X: 'window.main.x',
      Y: 'window.main.y',
      MAXIMIZED: 'window.main.maximized',
      MINIMIZED: 'window.main.minimized'
    },
    SEARCH: {
      WIDTH: 'window.search.width',
      HEIGHT: 'window.search.height',
      X: 'window.search.x',
      Y: 'window.search.y'
    },
    TOOLBAR: {
      WIDTH: 'window.toolbar.width',
      HEIGHT: 'window.toolbar.height',
      X: 'window.toolbar.x',
      Y: 'window.toolbar.y',
      AUTO_HIDE: 'window.toolbar.autoHide'
    }
  },
  SERVICES: {
    DATABASE: {
      TYPE: 'services.database.type',
      PATH: 'services.database.path',
      BACKUP_ENABLED: 'services.database.backup.enabled',
      BACKUP_INTERVAL: 'services.database.backup.interval'
    },
    STORAGE: {
      ROOT_PATH: 'services.storage.rootPath',
      CACHE_ENABLED: 'services.storage.cache.enabled',
      CACHE_SIZE: 'services.storage.cache.maxSize'
    },
    CLIPBOARD: {
      ENABLED: 'services.clipboard.enabled',
      HISTORY_SIZE: 'services.clipboard.maxHistorySize',
      AUTO_WATCH: 'services.clipboard.autoWatch'
    }
  },
  UI: {
    THEME: 'ui.theme',
    LANGUAGE: 'ui.language',
    FONT_SIZE: 'ui.fontSize',
    ANIMATIONS: 'ui.animations'
  }
} as const

/**
 * 默认配置值
 */
export const DEFAULT_VALUES = {
  WINDOW: {
    MIN_WIDTH: 300,
    MIN_HEIGHT: 200,
    DEFAULT_WIDTH: 1200,
    DEFAULT_HEIGHT: 800,
    OPACITY: 1.0,
    BACKGROUND_COLOR: '#ffffff'
  },
  PERFORMANCE: {
    MAX_MEMORY_USAGE: 512 * 1024 * 1024, // 512MB
    GC_INTERVAL: 60000, // 1分钟
    HEALTH_CHECK_INTERVAL: 30000, // 30秒
    METRICS_COLLECTION_INTERVAL: 5000 // 5秒
  },
  SECURITY: {
    MAX_REQUEST_SIZE: 10 * 1024 * 1024, // 10MB
    RATE_LIMIT_WINDOW: 60000, // 1分钟
    RATE_LIMIT_MAX_REQUESTS: 100,
    SESSION_TIMEOUT: 30 * 60 * 1000 // 30分钟
  },
  LOGGING: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_FILES: 5,
    FLUSH_INTERVAL: 1000, // 1秒
    COMPRESSION: true
  }
} as const

/**
 * 事件名称常量
 */
export const EVENT_NAMES = {
  APP: {
    STATE_CHANGED: 'app:state-changed',
    CONFIG_CHANGED: 'app:config-changed',
    ERROR: 'app:error',
    WARNING: 'app:warning',
    READY: 'app:ready',
    SHUTDOWN: 'app:shutdown'
  },
  WINDOW: {
    CREATED: 'window:created',
    DESTROYED: 'window:destroyed',
    FOCUSED: 'window:focused',
    BLURRED: 'window:blurred',
    MINIMIZED: 'window:minimized',
    MAXIMIZED: 'window:maximized',
    RESTORED: 'window:restored'
  },
  SERVICE: {
    REGISTERED: 'service:registered',
    UNREGISTERED: 'service:unregistered',
    STARTED: 'service:started',
    STOPPED: 'service:stopped',
    ERROR: 'service:error',
    HEALTH_CHECK: 'service:health-check'
  },
  IPC: {
    REQUEST: 'ipc:request',
    RESPONSE: 'ipc:response',
    ERROR: 'ipc:error',
    TIMEOUT: 'ipc:timeout'
  }
} as const

/**
 * 错误代码常量
 */
export const ERROR_CODES = {
  GENERAL: {
    UNKNOWN: 'E_UNKNOWN',
    INVALID_ARGUMENT: 'E_INVALID_ARGUMENT',
    NOT_FOUND: 'E_NOT_FOUND',
    PERMISSION_DENIED: 'E_PERMISSION_DENIED',
    TIMEOUT: 'E_TIMEOUT'
  },
  APP: {
    INITIALIZATION_FAILED: 'E_APP_INIT_FAILED',
    ALREADY_RUNNING: 'E_APP_ALREADY_RUNNING',
    SHUTDOWN_FAILED: 'E_APP_SHUTDOWN_FAILED',
    CONFIG_INVALID: 'E_APP_CONFIG_INVALID'
  },
  SERVICE: {
    NOT_REGISTERED: 'E_SERVICE_NOT_REGISTERED',
    ALREADY_REGISTERED: 'E_SERVICE_ALREADY_REGISTERED',
    START_FAILED: 'E_SERVICE_START_FAILED',
    STOP_FAILED: 'E_SERVICE_STOP_FAILED',
    DEPENDENCY_MISSING: 'E_SERVICE_DEPENDENCY_MISSING',
    CIRCULAR_DEPENDENCY: 'E_SERVICE_CIRCULAR_DEPENDENCY'
  },
  WINDOW: {
    CREATION_FAILED: 'E_WINDOW_CREATION_FAILED',
    NOT_FOUND: 'E_WINDOW_NOT_FOUND',
    INVALID_TYPE: 'E_WINDOW_INVALID_TYPE',
    LOAD_FAILED: 'E_WINDOW_LOAD_FAILED'
  },
  IPC: {
    CHANNEL_NOT_FOUND: 'E_IPC_CHANNEL_NOT_FOUND',
    HANDLER_NOT_FOUND: 'E_IPC_HANDLER_NOT_FOUND',
    INVALID_MESSAGE: 'E_IPC_INVALID_MESSAGE',
    TIMEOUT: 'E_IPC_TIMEOUT'
  },
  DATABASE: {
    CONNECTION_FAILED: 'E_DB_CONNECTION_FAILED',
    QUERY_FAILED: 'E_DB_QUERY_FAILED',
    TRANSACTION_FAILED: 'E_DB_TRANSACTION_FAILED',
    BACKUP_FAILED: 'E_DB_BACKUP_FAILED'
  },
  STORAGE: {
    READ_FAILED: 'E_STORAGE_READ_FAILED',
    WRITE_FAILED: 'E_STORAGE_WRITE_FAILED',
    DELETE_FAILED: 'E_STORAGE_DELETE_FAILED',
    QUOTA_EXCEEDED: 'E_STORAGE_QUOTA_EXCEEDED'
  },
  CLIPBOARD: {
    ACCESS_DENIED: 'E_CLIPBOARD_ACCESS_DENIED',
    FORMAT_UNSUPPORTED: 'E_CLIPBOARD_FORMAT_UNSUPPORTED',
    WATCH_FAILED: 'E_CLIPBOARD_WATCH_FAILED'
  }
} as const

/**
 * 超时常量（毫秒）
 */
export const TIMEOUTS = {
  APP_STARTUP: 30000, // 30秒
  APP_SHUTDOWN: 10000, // 10秒
  SERVICE_START: 5000, // 5秒
  SERVICE_STOP: 3000, // 3秒
  WINDOW_LOAD: 10000, // 10秒
  IPC_REQUEST: 5000, // 5秒
  DATABASE_QUERY: 30000, // 30秒
  FILE_OPERATION: 10000, // 10秒
  NETWORK_REQUEST: 15000 // 15秒
} as const

/**
 * 限制常量
 */
export const LIMITS = {
  MAX_WINDOWS: 50,
  MAX_SERVICES: 100,
  MAX_IPC_HANDLERS: 1000,
  MAX_LOG_ENTRIES: 10000,
  MAX_ERROR_HISTORY: 1000,
  MAX_CLIPBOARD_HISTORY: 100,
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_MESSAGE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_CONCURRENT_REQUESTS: 50
} as const

/**
 * 正则表达式常量
 */
export const REGEX_PATTERNS = {
  VERSION: /^\d+\.\d+\.\d+(-[a-zA-Z0-9]+)?$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/.+/,
  FILE_PATH: /^[a-zA-Z]:[\\\/]|^\/|^\.\.?[\\\/]/,
  WINDOW_ID: /^[a-zA-Z0-9_-]+$/,
  SERVICE_NAME: /^[a-zA-Z][a-zA-Z0-9_-]*$/,
  CONFIG_KEY: /^[a-zA-Z][a-zA-Z0-9_.]*$/
} as const

/**
 * MIME类型常量
 */
export const MIME_TYPES = {
  JSON: 'application/json',
  TEXT: 'text/plain',
  HTML: 'text/html',
  CSS: 'text/css',
  JAVASCRIPT: 'application/javascript',
  PNG: 'image/png',
  JPEG: 'image/jpeg',
  GIF: 'image/gif',
  SVG: 'image/svg+xml',
  PDF: 'application/pdf',
  ZIP: 'application/zip'
} as const

/**
 * 快捷键常量
 */
export const SHORTCUTS = {
  SHOW_MAIN: 'Ctrl+Shift+M',
  SHOW_SEARCH: 'Ctrl+Shift+F',
  SHOW_TOOLBAR: 'Ctrl+Space',
  TOGGLE_CLIPBOARD: 'Ctrl+Shift+V',
  TOGGLE_DEVTOOLS: 'F12',
  RELOAD: 'Ctrl+R',
  FORCE_RELOAD: 'Ctrl+Shift+R',
  QUIT: 'Ctrl+Q',
  MINIMIZE: 'Ctrl+M',
  CLOSE: 'Ctrl+W'
} as const

export default {
  APP_INFO,
  APP_PATHS,
  CONFIG_KEYS,
  DEFAULT_VALUES,
  EVENT_NAMES,
  ERROR_CODES,
  TIMEOUTS,
  LIMITS,
  REGEX_PATTERNS,
  MIME_TYPES,
  SHORTCUTS
}
