/**
 * 应用类型定义
 * 定义应用层面的通用类型和接口
 */

/**
 * 应用状态枚举
 */
export enum AppState {
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error'
}

/**
 * 应用环境枚举
 */
export enum AppEnvironment {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  STAGING = 'staging',
  PRODUCTION = 'production'
}

/**
 * 应用信息接口
 */
export interface AppInfo {
  name: string
  version: string
  description?: string
  author?: string
  homepage?: string
  repository?: string
  license?: string
  buildTime?: string
  commitHash?: string
}

/**
 * 应用统计接口
 */
export interface AppStats {
  startTime: Date
  uptime: number
  memoryUsage: NodeJS.MemoryUsage
  cpuUsage: NodeJS.CpuUsage
  eventLoopDelay: number
  activeHandles: number
  activeRequests: number
}

/**
 * 应用配置接口
 */
export interface AppConfiguration {
  environment: AppEnvironment
  debug: boolean
  logLevel: string
  dataPath: string
  configPath: string
  cachePath: string
  tempPath: string
}

/**
 * 应用事件类型
 */
export type AppEventType = 
  | 'state-changed'
  | 'config-changed'
  | 'error'
  | 'warning'
  | 'info'
  | 'debug'
  | 'performance'
  | 'security'

/**
 * 应用事件接口
 */
export interface AppEvent {
  type: AppEventType
  timestamp: Date
  source: string
  data?: any
  error?: Error
}

/**
 * 应用错误接口
 */
export interface AppError extends Error {
  code?: string
  category?: string
  severity?: 'low' | 'medium' | 'high' | 'critical'
  context?: Record<string, any>
  timestamp?: Date
  handled?: boolean
}

/**
 * 应用性能指标接口
 */
export interface AppPerformanceMetrics {
  timestamp: Date
  memory: {
    used: number
    total: number
    percentage: number
  }
  cpu: {
    user: number
    system: number
    percentage: number
  }
  eventLoop: {
    delay: number
    utilization: number
  }
  gc: {
    collections: number
    duration: number
  }
}

/**
 * 应用安全事件接口
 */
export interface AppSecurityEvent {
  type: 'access-denied' | 'invalid-request' | 'rate-limit' | 'suspicious-activity'
  timestamp: Date
  source: string
  details: Record<string, any>
  severity: 'low' | 'medium' | 'high' | 'critical'
}

/**
 * 应用更新信息接口
 */
export interface AppUpdateInfo {
  available: boolean
  version?: string
  releaseDate?: Date
  downloadUrl?: string
  changelog?: string
  mandatory?: boolean
  size?: number
}

/**
 * 应用备份信息接口
 */
export interface AppBackupInfo {
  id: string
  timestamp: Date
  type: 'manual' | 'automatic'
  size: number
  path: string
  checksum: string
  metadata: Record<string, any>
}

/**
 * 应用恢复选项接口
 */
export interface AppRestoreOptions {
  backupId: string
  restoreData: boolean
  restoreConfig: boolean
  restoreCache: boolean
  createBackup: boolean
}

/**
 * 应用导出选项接口
 */
export interface AppExportOptions {
  includeData: boolean
  includeConfig: boolean
  includeCache: boolean
  includeLogs: boolean
  format: 'zip' | 'tar' | 'json'
  encryption?: {
    enabled: boolean
    algorithm: string
    password?: string
  }
}

/**
 * 应用导入选项接口
 */
export interface AppImportOptions {
  filePath: string
  overwriteExisting: boolean
  createBackup: boolean
  validateData: boolean
  encryption?: {
    password?: string
  }
}

/**
 * 应用健康检查结果接口
 */
export interface AppHealthCheck {
  healthy: boolean
  timestamp: Date
  checks: {
    name: string
    status: 'pass' | 'fail' | 'warn'
    message?: string
    duration: number
  }[]
  overall: {
    status: 'healthy' | 'degraded' | 'unhealthy'
    score: number
    message?: string
  }
}

/**
 * 应用诊断信息接口
 */
export interface AppDiagnostics {
  timestamp: Date
  system: {
    platform: string
    arch: string
    version: string
    memory: number
    cpu: string
  }
  process: {
    pid: number
    uptime: number
    memory: NodeJS.MemoryUsage
    versions: NodeJS.ProcessVersions
  }
  application: {
    version: string
    environment: string
    state: AppState
    services: string[]
    windows: string[]
  }
  performance: AppPerformanceMetrics
  errors: AppError[]
}

export default {
  AppState,
  AppEnvironment
}
