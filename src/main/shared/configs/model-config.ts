export const modelProviders: ModelProvider[] = [
  {
    id: 'openrouter',
    name: 'Open Router',
    icon: '',
    webSite: 'https://openrouter.ai/',
    baseUrl: 'https://openrouter.ai/api/v1',
    apiKey: '',
    type: 'openai',
    enabled: false,
    models: [
      {
        id: 'deepseek/deepseek-chat',
        name: 'DeepSeek: V3',
        group: 'deepseek',
        tools: ['function'],
        description: '深度求索对话模型',
        enabled: false,
      },
      {
        id: 'google/gemini-2.0-flash-exp:free',
        name: 'Google: Gemini 2.0 Flash Experimental',
        group: 'Google',
        tools: ['function'],
        description: 'Google Gemini 2.0 Flash Experimental',
        enabled: false,
      },
    ],
  },
]
