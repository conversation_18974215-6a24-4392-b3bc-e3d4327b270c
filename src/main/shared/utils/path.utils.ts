/**
 * 路径工具函数
 * 提供路径处理相关的工具函数
 */

import {
  join,
  resolve,
  dirname,
  basename,
  extname,
  relative,
  isAbsolute,
  normalize,
  sep,
} from 'node:path'
import { existsSync, statSync, mkdirSync } from 'node:fs'
import { app } from 'electron'

/**
 * 获取应用数据目录
 */
export function getAppDataPath(): string {
  return app.getPath('userData')
}

/**
 * 获取应用配置目录
 */
export function getConfigPath(): string {
  return join(getAppDataPath(), 'config')
}

/**
 * 获取应用日志目录
 */
export function getLogPath(): string {
  return join(getAppDataPath(), 'logs')
}

/**
 * 获取应用缓存目录
 */
export function getCachePath(): string {
  return join(getAppDataPath(), 'cache')
}

/**
 * 获取应用临时目录
 */
export function getTempPath(): string {
  return join(getAppDataPath(), 'temp')
}

/**
 * 获取应用备份目录
 */
export function getBackupPath(): string {
  return join(getAppDataPath(), 'backups')
}

/**
 * 获取应用存储目录
 */
export function getStoragePath(): string {
  return join(getAppDataPath(), 'storage')
}

/**
 * 获取应用数据库目录
 */
export function getDatabasePath(): string {
  return join(getAppDataPath(), 'database')
}

/**
 * 获取应用插件目录
 */
export function getPluginsPath(): string {
  return join(getAppDataPath(), 'plugins')
}

/**
 * 获取应用主题目录
 */
export function getThemesPath(): string {
  return join(getAppDataPath(), 'themes')
}

/**
 * 确保目录存在
 */
export function ensureDir(dirPath: string): void {
  if (!existsSync(dirPath)) {
    mkdirSync(dirPath, { recursive: true })
  }
}

/**
 * 确保所有应用目录存在
 */
export function ensureAppDirs(): void {
  const dirs = [
    getAppDataPath(),
    getConfigPath(),
    getLogPath(),
    getCachePath(),
    getTempPath(),
    getBackupPath(),
    getStoragePath(),
    getDatabasePath(),
    getPluginsPath(),
    getThemesPath(),
  ]

  for (const dir of dirs) {
    ensureDir(dir)
  }
}

/**
 * 检查路径是否存在
 */
export function pathExists(path: string): boolean {
  return existsSync(path)
}

/**
 * 检查路径是否为文件
 */
export function isFile(path: string): boolean {
  try {
    return existsSync(path) && statSync(path).isFile()
  } catch {
    return false
  }
}

/**
 * 检查路径是否为目录
 */
export function isDirectory(path: string): boolean {
  try {
    return existsSync(path) && statSync(path).isDirectory()
  } catch {
    return false
  }
}

/**
 * 获取文件大小
 */
export function getFileSize(filePath: string): number {
  try {
    return statSync(filePath).size
  } catch {
    return 0
  }
}

/**
 * 获取文件修改时间
 */
export function getFileModTime(filePath: string): Date | null {
  try {
    return statSync(filePath).mtime
  } catch {
    return null
  }
}

/**
 * 规范化路径
 */
export function normalizePath(path: string): string {
  return normalize(path)
}

/**
 * 解析绝对路径
 */
export function resolvePath(...paths: string[]): string {
  return resolve(...paths)
}

/**
 * 连接路径
 */
export function joinPath(...paths: string[]): string {
  return join(...paths)
}

/**
 * 获取目录名
 */
export function getDirName(path: string): string {
  return dirname(path)
}

/**
 * 获取文件名（包含扩展名）
 */
export function getFileName(path: string): string {
  return basename(path)
}

/**
 * 获取文件名（不包含扩展名）
 */
export function getFileNameWithoutExt(path: string): string {
  return basename(path, extname(path))
}

/**
 * 获取文件扩展名
 */
export function getFileExt(path: string): string {
  return extname(path)
}

/**
 * 获取相对路径
 */
export function getRelativePath(from: string, to: string): string {
  return relative(from, to)
}

/**
 * 检查是否为绝对路径
 */
export function isAbsolutePath(path: string): boolean {
  return isAbsolute(path)
}

/**
 * 获取路径分隔符
 */
export function getPathSeparator(): string {
  return sep
}

/**
 * 转换路径分隔符为当前平台格式
 */
export function convertPathSeparator(path: string): string {
  return path.replace(/[/\\]/g, sep)
}

/**
 * 转换为Unix风格路径
 */
export function toUnixPath(path: string): string {
  return path.replace(/\\/g, '/')
}

/**
 * 转换为Windows风格路径
 */
export function toWindowsPath(path: string): string {
  return path.replace(/\//g, '\\')
}

/**
 * 安全地连接路径（防止路径遍历攻击）
 */
export function safeJoin(basePath: string, ...paths: string[]): string {
  const fullPath = join(basePath, ...paths)
  const resolvedBase = resolve(basePath)
  const resolvedFull = resolve(fullPath)

  if (!resolvedFull.startsWith(resolvedBase)) {
    throw new Error('路径遍历攻击检测')
  }

  return resolvedFull
}

/**
 * 验证路径是否安全
 */
export function isPathSafe(path: string, basePath?: string): boolean {
  try {
    const resolvedPath = resolve(path)

    // 检查是否包含危险字符
    if (resolvedPath.includes('..') || resolvedPath.includes('~')) {
      return false
    }

    // 如果提供了基础路径，检查是否在允许范围内
    if (basePath) {
      const resolvedBase = resolve(basePath)
      if (!resolvedPath.startsWith(resolvedBase)) {
        return false
      }
    }

    return true
  } catch {
    return false
  }
}

/**
 * 生成唯一文件名
 */
export function generateUniqueFileName(
  baseName: string,
  extension: string
): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `${baseName}_${timestamp}_${random}${extension}`
}

/**
 * 生成备份文件名
 */
export function generateBackupFileName(originalPath: string): string {
  const dir = dirname(originalPath)
  const name = getFileNameWithoutExt(originalPath)
  const ext = getFileExt(originalPath)
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')

  return join(dir, `${name}_backup_${timestamp}${ext}`)
}

/**
 * 获取文件的MIME类型（基于扩展名）
 */
export function getMimeType(filePath: string): string {
  const ext = getFileExt(filePath).toLowerCase()

  const mimeTypes: Record<string, string> = {
    '.txt': 'text/plain',
    '.json': 'application/json',
    '.js': 'application/javascript',
    '.ts': 'application/typescript',
    '.html': 'text/html',
    '.css': 'text/css',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.pdf': 'application/pdf',
    '.zip': 'application/zip',
    '.tar': 'application/x-tar',
    '.gz': 'application/gzip',
  }

  return mimeTypes[ext] || 'application/octet-stream'
}

/**
 * 检查文件扩展名是否被允许
 */
export function isExtensionAllowed(
  filePath: string,
  allowedExtensions: string[]
): boolean {
  if (allowedExtensions.includes('*')) {
    return true
  }

  const ext = getFileExt(filePath).toLowerCase()
  return allowedExtensions.includes(ext)
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`
}

/**
 * 解析文件大小字符串
 */
export function parseFileSize(sizeStr: string): number {
  const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)?$/i)
  if (!match) {
    throw new Error('无效的文件大小格式')
  }

  const size = parseFloat(match[1])
  const unit = (match[2] || 'B').toUpperCase()

  const multipliers: Record<string, number> = {
    B: 1,
    KB: 1024,
    MB: 1024 * 1024,
    GB: 1024 * 1024 * 1024,
    TB: 1024 * 1024 * 1024 * 1024,
  }

  return size * (multipliers[unit] || 1)
}

export default {
  getAppDataPath,
  getConfigPath,
  getLogPath,
  getCachePath,
  getTempPath,
  getBackupPath,
  getStoragePath,
  getDatabasePath,
  getPluginsPath,
  getThemesPath,
  ensureDir,
  ensureAppDirs,
  pathExists,
  isFile,
  isDirectory,
  getFileSize,
  getFileModTime,
  normalizePath,
  resolvePath,
  joinPath,
  getDirName,
  getFileName,
  getFileNameWithoutExt,
  getFileExt,
  getRelativePath,
  isAbsolutePath,
  getPathSeparator,
  convertPathSeparator,
  toUnixPath,
  toWindowsPath,
  safeJoin,
  isPathSafe,
  generateUniqueFileName,
  generateBackupFileName,
  getMimeType,
  isExtensionAllowed,
  formatFileSize,
  parseFileSize,
}
