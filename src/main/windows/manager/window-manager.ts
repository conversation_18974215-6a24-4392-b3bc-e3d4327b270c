/**
 * 窗口管理器
 * 负责应用所有窗口的创建、管理和销毁
 */

import { BrowserWindow, screen } from 'electron'
import { EventEmitter } from 'events'
import { join } from 'path'
import type { WindowConfig } from '../../core/config/config-schema'

/**
 * 窗口类型枚举
 */
export enum WindowType {
  MAIN = 'main',
  SEARCH = 'search',
  TOOLBAR = 'toolbar',
  SETTINGS = 'settings'
}

/**
 * 窗口状态枚举
 */
export enum WindowState {
  CREATING = 'creating',
  READY = 'ready',
  VISIBLE = 'visible',
  HIDDEN = 'hidden',
  MINIMIZED = 'minimized',
  MAXIMIZED = 'maximized',
  CLOSING = 'closing',
  CLOSED = 'closed'
}

/**
 * 窗口信息接口
 */
export interface WindowInfo {
  id: string
  type: WindowType
  window: BrowserWindow
  state: WindowState
  config: WindowConfig
  createdAt: Date
  lastActiveAt: Date
}

/**
 * 窗口管理器类
 */
export class WindowManager extends EventEmitter {
  private windows = new Map<string, WindowInfo>()
  private focusedWindowId?: string
  private isInitialized = false

  constructor() {
    super()
  }

  /**
   * 初始化窗口管理器
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('[WindowManager] 窗口管理器已经初始化')
      return
    }

    try {
      this.log('info', '初始化窗口管理器...')
      
      // 设置默认窗口行为
      this.setupDefaultBehavior()
      
      this.isInitialized = true
      this.emit('initialized')
      this.log('info', '窗口管理器初始化完成')

    } catch (error) {
      this.log('error', '窗口管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * 显示主窗口
   */
  public async showMainWindow(): Promise<BrowserWindow> {
    const existingWindow = this.getWindowByType(WindowType.MAIN)
    if (existingWindow) {
      this.showWindow(existingWindow.id)
      return existingWindow.window
    }

    return this.createMainWindow()
  }

  /**
   * 显示搜索窗口
   */
  public async showSearchWindow(): Promise<BrowserWindow> {
    const existingWindow = this.getWindowByType(WindowType.SEARCH)
    if (existingWindow) {
      this.showWindow(existingWindow.id)
      return existingWindow.window
    }

    return this.createSearchWindow()
  }

  /**
   * 显示工具栏
   */
  public async showToolbar(): Promise<BrowserWindow> {
    const existingWindow = this.getWindowByType(WindowType.TOOLBAR)
    if (existingWindow) {
      this.showWindow(existingWindow.id)
      return existingWindow.window
    }

    return this.createToolbarWindow()
  }

  /**
   * 创建窗口
   */
  public async createWindow(type: WindowType, config: WindowConfig): Promise<BrowserWindow> {
    const windowId = this.generateWindowId(type)
    
    try {
      this.log('info', `创建窗口: ${type} (${windowId})`)
      
      // 创建窗口
      const window = new BrowserWindow(config)
      
      // 创建窗口信息
      const windowInfo: WindowInfo = {
        id: windowId,
        type,
        window,
        state: WindowState.CREATING,
        config,
        createdAt: new Date(),
        lastActiveAt: new Date()
      }
      
      // 注册窗口
      this.windows.set(windowId, windowInfo)
      
      // 设置窗口事件
      this.setupWindowEvents(windowInfo)
      
      // 加载页面
      await this.loadWindowContent(windowInfo)
      
      // 更新状态
      this.setState(windowId, WindowState.READY)
      
      this.emit('windowCreated', windowInfo)
      this.log('info', `窗口创建完成: ${type} (${windowId})`)
      
      return window
      
    } catch (error) {
      this.log('error', `创建窗口失败: ${type}`, error)
      this.windows.delete(windowId)
      throw error
    }
  }

  /**
   * 显示窗口
   */
  public showWindow(windowId: string): void {
    const windowInfo = this.windows.get(windowId)
    if (!windowInfo) {
      throw new Error(`窗口不存在: ${windowId}`)
    }

    const { window } = windowInfo
    
    if (window.isMinimized()) {
      window.restore()
    }
    
    window.show()
    window.focus()
    
    this.setState(windowId, WindowState.VISIBLE)
    this.updateLastActive(windowId)
    
    this.log('info', `显示窗口: ${windowInfo.type} (${windowId})`)
  }

  /**
   * 隐藏窗口
   */
  public hideWindow(windowId: string): void {
    const windowInfo = this.windows.get(windowId)
    if (!windowInfo) {
      throw new Error(`窗口不存在: ${windowId}`)
    }

    windowInfo.window.hide()
    this.setState(windowId, WindowState.HIDDEN)
    
    this.log('info', `隐藏窗口: ${windowInfo.type} (${windowId})`)
  }

  /**
   * 关闭窗口
   */
  public closeWindow(windowId: string): void {
    const windowInfo = this.windows.get(windowId)
    if (!windowInfo) {
      return
    }

    this.setState(windowId, WindowState.CLOSING)
    windowInfo.window.close()
    
    this.log('info', `关闭窗口: ${windowInfo.type} (${windowId})`)
  }

  /**
   * 获取窗口信息
   */
  public getWindow(windowId: string): WindowInfo | undefined {
    return this.windows.get(windowId)
  }

  /**
   * 根据类型获取窗口
   */
  public getWindowByType(type: WindowType): WindowInfo | undefined {
    for (const windowInfo of this.windows.values()) {
      if (windowInfo.type === type) {
        return windowInfo
      }
    }
    return undefined
  }

  /**
   * 获取所有窗口
   */
  public getAllWindows(): WindowInfo[] {
    return Array.from(this.windows.values())
  }

  /**
   * 获取焦点窗口
   */
  public getFocusedWindow(): WindowInfo | undefined {
    return this.focusedWindowId ? this.windows.get(this.focusedWindowId) : undefined
  }

  /**
   * 销毁窗口管理器
   */
  public async destroy(): Promise<void> {
    this.log('info', '销毁窗口管理器...')
    
    // 关闭所有窗口
    const windowIds = Array.from(this.windows.keys())
    for (const windowId of windowIds) {
      this.closeWindow(windowId)
    }
    
    // 等待所有窗口关闭
    await this.waitForAllWindowsClosed()
    
    this.windows.clear()
    this.removeAllListeners()
    this.isInitialized = false
    
    this.log('info', '窗口管理器销毁完成')
  }

  /**
   * 创建主窗口
   */
  private async createMainWindow(): Promise<BrowserWindow> {
    const config: WindowConfig = {
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: true,
      center: true,
      resizable: true,
      maximizable: true,
      minimizable: true,
      closable: true,
      alwaysOnTop: false,
      skipTaskbar: false,
      frame: true,
      transparent: false,
      opacity: 1.0,
      backgroundColor: '#ffffff',
      titleBarStyle: 'default',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        webSecurity: true
      }
    }

    return this.createWindow(WindowType.MAIN, config)
  }

  /**
   * 创建搜索窗口
   */
  private async createSearchWindow(): Promise<BrowserWindow> {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize
    
    const config: WindowConfig = {
      width: 600,
      height: 400,
      x: Math.round((width - 600) / 2),
      y: Math.round((height - 400) / 3),
      show: true,
      center: false,
      resizable: true,
      maximizable: false,
      minimizable: false,
      closable: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      frame: false,
      transparent: true,
      opacity: 0.95,
      backgroundColor: '#00000000',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        webSecurity: true
      }
    }

    return this.createWindow(WindowType.SEARCH, config)
  }

  /**
   * 创建工具栏窗口
   */
  private async createToolbarWindow(): Promise<BrowserWindow> {
    const { width } = screen.getPrimaryDisplay().workAreaSize
    
    const config: WindowConfig = {
      width: 300,
      height: 50,
      x: Math.round((width - 300) / 2),
      y: 50,
      show: true,
      center: false,
      resizable: false,
      maximizable: false,
      minimizable: false,
      closable: false,
      alwaysOnTop: true,
      skipTaskbar: true,
      frame: false,
      transparent: true,
      opacity: 0.9,
      backgroundColor: '#00000000',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        webSecurity: true
      }
    }

    return this.createWindow(WindowType.TOOLBAR, config)
  }

  /**
   * 设置窗口事件
   */
  private setupWindowEvents(windowInfo: WindowInfo): void {
    const { window, id, type } = windowInfo

    window.on('ready-to-show', () => {
      this.setState(id, WindowState.READY)
      this.emit('windowReady', windowInfo)
    })

    window.on('show', () => {
      this.setState(id, WindowState.VISIBLE)
      this.updateLastActive(id)
      this.emit('windowShown', windowInfo)
    })

    window.on('hide', () => {
      this.setState(id, WindowState.HIDDEN)
      this.emit('windowHidden', windowInfo)
    })

    window.on('focus', () => {
      this.focusedWindowId = id
      this.updateLastActive(id)
      this.emit('windowFocused', windowInfo)
    })

    window.on('blur', () => {
      if (this.focusedWindowId === id) {
        this.focusedWindowId = undefined
      }
      this.emit('windowBlurred', windowInfo)
    })

    window.on('minimize', () => {
      this.setState(id, WindowState.MINIMIZED)
      this.emit('windowMinimized', windowInfo)
    })

    window.on('maximize', () => {
      this.setState(id, WindowState.MAXIMIZED)
      this.emit('windowMaximized', windowInfo)
    })

    window.on('unmaximize', () => {
      this.setState(id, WindowState.VISIBLE)
      this.emit('windowUnmaximized', windowInfo)
    })

    window.on('restore', () => {
      this.setState(id, WindowState.VISIBLE)
      this.emit('windowRestored', windowInfo)
    })

    window.on('closed', () => {
      this.setState(id, WindowState.CLOSED)
      this.windows.delete(id)
      
      if (this.focusedWindowId === id) {
        this.focusedWindowId = undefined
      }
      
      this.emit('windowClosed', windowInfo)
      this.log('info', `窗口已关闭: ${type} (${id})`)
    })
  }

  /**
   * 加载窗口内容
   */
  private async loadWindowContent(windowInfo: WindowInfo): Promise<void> {
    const { window, type } = windowInfo
    
    if (process.env.NODE_ENV === 'development') {
      // 开发模式：加载开发服务器
      const devUrl = this.getDevUrl(type)
      await window.loadURL(devUrl)
      window.webContents.openDevTools()
    } else {
      // 生产模式：加载本地文件
      const indexPath = this.getIndexPath(type)
      await window.loadFile(indexPath)
    }
  }

  /**
   * 获取开发服务器URL
   */
  private getDevUrl(type: WindowType): string {
    const baseUrl = 'http://localhost:3000'
    
    switch (type) {
      case WindowType.MAIN:
        return `${baseUrl}/`
      case WindowType.SEARCH:
        return `${baseUrl}/search`
      case WindowType.TOOLBAR:
        return `${baseUrl}/toolbar`
      case WindowType.SETTINGS:
        return `${baseUrl}/settings`
      default:
        return baseUrl
    }
  }

  /**
   * 获取本地文件路径
   */
  private getIndexPath(type: WindowType): string {
    const basePath = join(__dirname, '..', '..', '..', 'renderer')
    
    switch (type) {
      case WindowType.MAIN:
        return join(basePath, 'index.html')
      case WindowType.SEARCH:
        return join(basePath, 'search.html')
      case WindowType.TOOLBAR:
        return join(basePath, 'toolbar.html')
      case WindowType.SETTINGS:
        return join(basePath, 'settings.html')
      default:
        return join(basePath, 'index.html')
    }
  }

  /**
   * 设置默认窗口行为
   */
  private setupDefaultBehavior(): void {
    // 这里可以设置全局的窗口行为
  }

  /**
   * 设置窗口状态
   */
  private setState(windowId: string, state: WindowState): void {
    const windowInfo = this.windows.get(windowId)
    if (windowInfo) {
      const oldState = windowInfo.state
      windowInfo.state = state
      this.emit('windowStateChanged', { windowInfo, oldState, newState: state })
    }
  }

  /**
   * 更新最后活动时间
   */
  private updateLastActive(windowId: string): void {
    const windowInfo = this.windows.get(windowId)
    if (windowInfo) {
      windowInfo.lastActiveAt = new Date()
    }
  }

  /**
   * 生成窗口ID
   */
  private generateWindowId(type: WindowType): string {
    return `${type}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 等待所有窗口关闭
   */
  private async waitForAllWindowsClosed(): Promise<void> {
    return new Promise((resolve) => {
      const checkClosed = () => {
        if (this.windows.size === 0) {
          resolve()
        } else {
          setTimeout(checkClosed, 100)
        }
      }
      checkClosed()
    })
  }

  /**
   * 日志记录
   */
  private log(level: 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    console[level](`[WindowManager] ${message}`, ...args)
  }
}

export default WindowManager
