/**
 * @file clipboard_native_windows.cc
 * @brief 剪贴板原生实现 (Windows)
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include "../include/clipboard_native.h"
#include <windows.h>
#include <shlobj.h>
#include <shellapi.h>
#include <comdef.h>
#include <gdiplus.h>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include <atomic>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <queue>
#include <mutex>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "ole32.lib")
#pragma comment(lib, "oleaut32.lib")
#pragma comment(lib, "shell32.lib")
#pragma comment(lib, "gdiplus.lib")

using namespace Gdiplus;

namespace ClipboardNative {

/**
 * @class ClipboardChangeWorker
 * @brief 异步工作器，用于在主线程中调用 JavaScript 回调
 */
class ClipboardChangeWorker : public Napi::AsyncWorker {
public:
    ClipboardChangeWorker(Napi::Function& callback, const ClipboardData& data)
        : Napi::AsyncWorker(callback), data_(data) {}

protected:
    void Execute() override {
        // 在后台线程中执行，这里不需要做任何事情
        // 数据已经在构造函数中准备好了
    }

    void OnOK() override {
        Napi::Env env = Env();
        Napi::Object dataObj = Napi::Object::New(env);

        // 构建数据对象
        dataObj.Set("type", ContentTypeToString(data_.type));

        switch (data_.type) {
            case ContentType::TEXT:
                dataObj.Set("textData", data_.textData);
                break;
            case ContentType::IMAGE:
                dataObj.Set("imageData", data_.imageData);
                break;
            case ContentType::HTML:
                dataObj.Set("htmlData", data_.htmlData);
                break;
            case ContentType::FILES: {
                Napi::Array filesArray = Napi::Array::New(env, data_.filePaths.size());
                for (size_t i = 0; i < data_.filePaths.size(); i++) {
                    filesArray[i] = Napi::String::New(env, data_.filePaths[i]);
                }
                dataObj.Set("filePaths", filesArray);
                break;
            }
            default:
                break;
        }

        // 调用 JavaScript 回调
        Callback().Call({dataObj});
    }

private:
    ClipboardData data_;
};

/**
 * @class ClipboardWindows
 * @brief Windows 平台剪贴板实现
 */
class ClipboardWindows : public ClipboardInterface {
private:
    HWND hwnd_;
    Napi::Function callback_;
    std::atomic<bool> isWatching_;
    std::thread watchThread_;
    DWORD lastSequenceNumber_;
    std::mutex callbackMutex_;
    
    // GDI+ 初始化
    static ULONG_PTR gdiplusToken_;
    static bool gdiplusInitialized_;
    
public:
    ClipboardWindows() : hwnd_(nullptr), isWatching_(false), lastSequenceNumber_(0) {
        // 初始化 GDI+
        if (!gdiplusInitialized_) {
            GdiplusStartupInput gdiplusStartupInput;
            GdiplusStartup(&gdiplusToken_, &gdiplusStartupInput, nullptr);
            gdiplusInitialized_ = true;
        }
        
        // 创建隐藏窗口用于剪贴板操作
        hwnd_ = CreateWindowExW(0, L"STATIC", L"ClipboardWindow",
                               WS_POPUP, 0, 0, 0, 0,
                               nullptr, nullptr, GetModuleHandle(nullptr), nullptr);
        
        if (hwnd_) {
            lastSequenceNumber_ = GetClipboardSequenceNumber();
        }
    }
    
    ~ClipboardWindows() {
        StopWatching();
        if (hwnd_) {
            DestroyWindow(hwnd_);
        }
        if (gdiplusInitialized_) {
            GdiplusShutdown(gdiplusToken_);
            gdiplusInitialized_ = false;
        }
    }
    
    bool HasData() override {
        return IsClipboardFormatAvailable(CF_TEXT) || 
               IsClipboardFormatAvailable(CF_UNICODETEXT) ||
               IsClipboardFormatAvailable(CF_BITMAP) ||
               IsClipboardFormatAvailable(CF_DIB) ||
               IsClipboardFormatAvailable(CF_HDROP) ||
               IsClipboardFormatAvailable(RegisterClipboardFormatW(L"HTML Format"));
    }
    
    std::string GetText() override {
        if (!OpenClipboard(hwnd_)) {
            return "";
        }
        
        std::string result;
        HANDLE hData = GetClipboardData(CF_UNICODETEXT);
        if (hData) {
            wchar_t* pszText = static_cast<wchar_t*>(GlobalLock(hData));
            if (pszText) {
                // 转换为 UTF-8
                int utf8Length = WideCharToMultiByte(CP_UTF8, 0, pszText, -1, nullptr, 0, nullptr, nullptr);
                if (utf8Length > 0) {
                    std::vector<char> utf8Buffer(utf8Length);
                    WideCharToMultiByte(CP_UTF8, 0, pszText, -1, utf8Buffer.data(), utf8Length, nullptr, nullptr);
                    result = utf8Buffer.data();
                }
                GlobalUnlock(hData);
            }
        }
        
        CloseClipboard();
        return result;
    }
    
    bool SetText(const std::string& text) override {
        if (!OpenClipboard(hwnd_)) {
            return false;
        }
        
        EmptyClipboard();
        
        // 转换为 UTF-16
        int wideLength = MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, nullptr, 0);
        if (wideLength <= 0) {
            CloseClipboard();
            return false;
        }
        
        HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, wideLength * sizeof(wchar_t));
        if (!hMem) {
            CloseClipboard();
            return false;
        }
        
        wchar_t* pMem = static_cast<wchar_t*>(GlobalLock(hMem));
        if (pMem) {
            MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, pMem, wideLength);
            GlobalUnlock(hMem);
            
            bool success = SetClipboardData(CF_UNICODETEXT, hMem) != nullptr;
            CloseClipboard();
            return success;
        }
        
        GlobalFree(hMem);
        CloseClipboard();
        return false;
    }
    
    std::string GetImage() override {
        if (!OpenClipboard(hwnd_)) {
            return "";
        }
        
        std::string result;
        HANDLE hData = GetClipboardData(CF_DIB);
        if (hData) {
            BITMAPINFO* pBitmapInfo = static_cast<BITMAPINFO*>(GlobalLock(hData));
            if (pBitmapInfo) {
                // 创建 GDI+ Bitmap
                BYTE* pBits = reinterpret_cast<BYTE*>(pBitmapInfo) + pBitmapInfo->bmiHeader.biSize;
                if (pBitmapInfo->bmiHeader.biBitCount <= 8) {
                    pBits += (1 << pBitmapInfo->bmiHeader.biBitCount) * sizeof(RGBQUAD);
                }
                
                Bitmap bitmap(pBitmapInfo, pBits);
                if (bitmap.GetLastStatus() == Ok) {
                    result = BitmapToBase64(&bitmap);
                }
                
                GlobalUnlock(hData);
            }
        }
        
        CloseClipboard();
        return result;
    }
    
    bool SetImage(const std::string& base64Data) override {
        if (!IsValidBase64(base64Data)) {
            return false;
        }
        
        // 解码 Base64
        std::vector<BYTE> imageData = Base64Decode(base64Data);
        if (imageData.empty()) {
            return false;
        }
        
        // 创建内存流
        IStream* pStream = nullptr;
        HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, imageData.size());
        if (!hMem) {
            return false;
        }
        
        void* pData = GlobalLock(hMem);
        if (pData) {
            memcpy(pData, imageData.data(), imageData.size());
            GlobalUnlock(hMem);
            
            if (CreateStreamOnHGlobal(hMem, TRUE, &pStream) == S_OK) {
                Bitmap bitmap(pStream);
                if (bitmap.GetLastStatus() == Ok) {
                    HBITMAP hBitmap;
                    if (bitmap.GetHBITMAP(Color(255, 255, 255), &hBitmap) == Ok) {
                        if (OpenClipboard(hwnd_)) {
                            EmptyClipboard();
                            bool success = SetClipboardData(CF_BITMAP, hBitmap) != nullptr;
                            CloseClipboard();
                            pStream->Release();
                            return success;
                        }
                        DeleteObject(hBitmap);
                    }
                }
                pStream->Release();
            }
        }
        
        GlobalFree(hMem);
        return false;
    }
    
    std::string GetHtml() override {
        UINT htmlFormat = RegisterClipboardFormatW(L"HTML Format");
        if (!IsClipboardFormatAvailable(htmlFormat)) {
            return "";
        }
        
        if (!OpenClipboard(hwnd_)) {
            return "";
        }
        
        std::string result;
        HANDLE hData = GetClipboardData(htmlFormat);
        if (hData) {
            char* pData = static_cast<char*>(GlobalLock(hData));
            if (pData) {
                result = pData;
                GlobalUnlock(hData);
            }
        }
        
        CloseClipboard();
        return result;
    }
    
    bool SetHtml(const std::string& html) override {
        UINT htmlFormat = RegisterClipboardFormatW(L"HTML Format");
        
        // 构建 HTML 格式字符串
        std::string htmlFormatStr = BuildHtmlFormat(html);
        
        if (!OpenClipboard(hwnd_)) {
            return false;
        }
        
        EmptyClipboard();
        
        HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, htmlFormatStr.length() + 1);
        if (!hMem) {
            CloseClipboard();
            return false;
        }
        
        char* pMem = static_cast<char*>(GlobalLock(hMem));
        if (pMem) {
            strcpy_s(pMem, htmlFormatStr.length() + 1, htmlFormatStr.c_str());
            GlobalUnlock(hMem);
            
            bool success = SetClipboardData(htmlFormat, hMem) != nullptr;
            CloseClipboard();
            return success;
        }
        
        GlobalFree(hMem);
        CloseClipboard();
        return false;
    }
    
    std::vector<std::string> GetFiles() override {
        std::vector<std::string> files;
        
        if (!IsClipboardFormatAvailable(CF_HDROP)) {
            return files;
        }
        
        if (!OpenClipboard(hwnd_)) {
            return files;
        }
        
        HANDLE hData = GetClipboardData(CF_HDROP);
        if (hData) {
            HDROP hDrop = static_cast<HDROP>(GlobalLock(hData));
            if (hDrop) {
                UINT fileCount = DragQueryFile(hDrop, 0xFFFFFFFF, nullptr, 0);
                
                for (UINT i = 0; i < fileCount; i++) {
                    UINT pathLength = DragQueryFileW(hDrop, i, nullptr, 0);
                    if (pathLength > 0) {
                        std::vector<wchar_t> pathBuffer(pathLength + 1);
                        if (DragQueryFileW(hDrop, i, pathBuffer.data(), pathLength + 1)) {
                            // 转换为 UTF-8
                            int utf8Length = WideCharToMultiByte(CP_UTF8, 0, pathBuffer.data(), -1, nullptr, 0, nullptr, nullptr);
                            if (utf8Length > 0) {
                                std::vector<char> utf8Buffer(utf8Length);
                                WideCharToMultiByte(CP_UTF8, 0, pathBuffer.data(), -1, utf8Buffer.data(), utf8Length, nullptr, nullptr);
                                files.push_back(utf8Buffer.data());
                            }
                        }
                    }
                }
                
                GlobalUnlock(hData);
            }
        }
        
        CloseClipboard();
        return files;
    }
    
    bool SetFiles(const std::vector<std::string>& filePaths) override {
        if (filePaths.empty()) {
            return false;
        }
        
        // 验证文件存在
        for (const auto& path : filePaths) {
            if (!FileExists(path)) {
                return false;
            }
        }
        
        // 计算所需内存大小
        size_t totalSize = sizeof(DROPFILES);
        for (const auto& path : filePaths) {
            int wideLength = MultiByteToWideChar(CP_UTF8, 0, path.c_str(), -1, nullptr, 0);
            totalSize += wideLength * sizeof(wchar_t);
        }
        totalSize += sizeof(wchar_t); // 双重 null 终止符
        
        HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, totalSize);
        if (!hMem) {
            return false;
        }
        
        BYTE* pMem = static_cast<BYTE*>(GlobalLock(hMem));
        if (pMem) {
            DROPFILES* pDropFiles = reinterpret_cast<DROPFILES*>(pMem);
            pDropFiles->pFiles = sizeof(DROPFILES);
            pDropFiles->fWide = TRUE;
            
            wchar_t* pFiles = reinterpret_cast<wchar_t*>(pMem + sizeof(DROPFILES));
            
            for (const auto& path : filePaths) {
                int wideLength = MultiByteToWideChar(CP_UTF8, 0, path.c_str(), -1, pFiles, 
                    (totalSize - (reinterpret_cast<BYTE*>(pFiles) - pMem)) / sizeof(wchar_t));
                pFiles += wideLength;
            }
            *pFiles = L'\0'; // 双重 null 终止符
            
            GlobalUnlock(hMem);
            
            if (OpenClipboard(hwnd_)) {
                EmptyClipboard();
                bool success = SetClipboardData(CF_HDROP, hMem) != nullptr;
                CloseClipboard();
                return success;
            }
        }

        GlobalFree(hMem);
        return false;
    }

    bool Clear() override {
        if (!OpenClipboard(hwnd_)) {
            return false;
        }

        bool success = EmptyClipboard() != FALSE;
        CloseClipboard();
        return success;
    }

    ContentType GetContentType() override {
        if (IsClipboardFormatAvailable(CF_HDROP)) {
            return ContentType::FILES;
        }

        if (IsClipboardFormatAvailable(CF_BITMAP) || IsClipboardFormatAvailable(CF_DIB)) {
            return ContentType::IMAGE;
        }

        UINT htmlFormat = RegisterClipboardFormatW(L"HTML Format");
        if (IsClipboardFormatAvailable(htmlFormat)) {
            return ContentType::HTML;
        }

        if (IsClipboardFormatAvailable(CF_UNICODETEXT) || IsClipboardFormatAvailable(CF_TEXT)) {
            return ContentType::TEXT;
        }

        return ContentType::EMPTY;
    }

    bool StartWatching(Napi::Function callback) override {
        if (isWatching_) {
            return false;
        }

        callback_ = callback;
        isWatching_ = true;

        watchThread_ = std::thread([this]() {
            while (isWatching_) {
                DWORD currentSequenceNumber = GetClipboardSequenceNumber();
                if (currentSequenceNumber != lastSequenceNumber_) {
                    lastSequenceNumber_ = currentSequenceNumber;

                    // 创建剪贴板数据对象
                    ClipboardData data;
                    data.type = GetContentType();

                    switch (data.type) {
                        case ContentType::TEXT:
                            data.textData = GetText();
                            break;
                        case ContentType::IMAGE:
                            data.imageData = GetImage();
                            break;
                        case ContentType::HTML:
                            data.htmlData = GetHtml();
                            break;
                        case ContentType::FILES:
                            data.filePaths = GetFiles();
                            break;
                        default:
                            break;
                    }

                    // 使用异步工作器调用 JavaScript 回调
                    if (!callback_.IsEmpty()) {
                        std::lock_guard<std::mutex> lock(callbackMutex_);
                        auto worker = new ClipboardChangeWorker(callback_, data);
                        worker->Queue();
                    }
                }

                Sleep(100); // 100ms 检查间隔
            }
        });

        return true;
    }

    bool StopWatching() override {
        if (!isWatching_) {
            return false;
        }

        isWatching_ = false;

        if (watchThread_.joinable()) {
            watchThread_.join();
        }

        return true;
    }

private:
    /**
     * @brief 将 Bitmap 转换为 Base64 字符串
     */
    std::string BitmapToBase64(Bitmap* bitmap) {
        IStream* pStream = nullptr;
        if (CreateStreamOnHGlobal(nullptr, TRUE, &pStream) != S_OK) {
            return "";
        }

        CLSID pngClsid;
        GetEncoderClsid(L"image/png", &pngClsid);

        if (bitmap->Save(pStream, &pngClsid, nullptr) != Ok) {
            pStream->Release();
            return "";
        }

        HGLOBAL hMem;
        GetHGlobalFromStream(pStream, &hMem);

        SIZE_T size = GlobalSize(hMem);
        BYTE* pData = static_cast<BYTE*>(GlobalLock(hMem));

        std::string result;
        if (pData) {
            result = Base64Encode(pData, size);
            GlobalUnlock(hMem);
        }

        pStream->Release();
        return result;
    }

    /**
     * @brief Base64 编码
     */
    std::string Base64Encode(const BYTE* data, size_t length) {
        static const char base64_chars[] =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

        std::string result;
        int val = 0, valb = -6;
        for (size_t i = 0; i < length; i++) {
            val = (val << 8) + data[i];
            valb += 8;
            while (valb >= 0) {
                result.push_back(base64_chars[(val >> valb) & 0x3F]);
                valb -= 6;
            }
        }
        if (valb > -6) {
            result.push_back(base64_chars[((val << 8) >> (valb + 8)) & 0x3F]);
        }
        while (result.size() % 4) {
            result.push_back('=');
        }
        return result;
    }

    /**
     * @brief Base64 解码
     */
    std::vector<BYTE> Base64Decode(const std::string& base64) {
        static const int T[128] = {
            -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
            -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
            -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,62, -1,-1,-1,63,
            52,53,54,55, 56,57,58,59, 60,61,-1,-1, -1,-2,-1,-1,
            -1, 0, 1, 2,  3, 4, 5, 6,  7, 8, 9,10, 11,12,13,14,
            15,16,17,18, 19,20,21,22, 23,24,25,-1, -1,-1,-1,-1,
            -1,26,27,28, 29,30,31,32, 33,34,35,36, 37,38,39,40,
            41,42,43,44, 45,46,47,48, 49,50,51,-1, -1,-1,-1,-1
        };

        std::vector<BYTE> result;
        int val = 0, valb = -8;
        for (unsigned char c : base64) {
            if (T[c] == -1) break;
            if (T[c] == -2) break;
            val = (val << 6) + T[c];
            valb += 6;
            if (valb >= 0) {
                result.push_back(char((val >> valb) & 0xFF));
                valb -= 8;
            }
        }
        return result;
    }

    /**
     * @brief 构建 HTML 格式字符串
     */
    std::string BuildHtmlFormat(const std::string& html) {
        std::string prefix = "Version:0.9\r\nStartHTML:";
        std::string middle = "\r\nEndHTML:";
        std::string suffix = "\r\nStartFragment:";
        std::string end = "\r\nEndFragment:";
        std::string htmlStart = "\r\n<html><body>\r\n<!--StartFragment-->";
        std::string htmlEnd = "<!--EndFragment-->\r\n</body></html>";

        int startHTML = prefix.length() + 10 + middle.length() + 10 + suffix.length() + 10 + end.length() + 10;
        int endHTML = startHTML + htmlStart.length() + html.length() + htmlEnd.length();
        int startFragment = startHTML + htmlStart.length();
        int endFragment = startFragment + html.length();

        std::ostringstream oss;
        oss << prefix << std::setfill('0') << std::setw(10) << startHTML
            << middle << std::setfill('0') << std::setw(10) << endHTML
            << suffix << std::setfill('0') << std::setw(10) << startFragment
            << end << std::setfill('0') << std::setw(10) << endFragment
            << htmlStart << html << htmlEnd;

        return oss.str();
    }

    /**
     * @brief 获取编码器 CLSID
     */
    int GetEncoderClsid(const WCHAR* format, CLSID* pClsid) {
        UINT num = 0;
        UINT size = 0;

        ImageCodecInfo* pImageCodecInfo = nullptr;

        GetImageEncodersSize(&num, &size);
        if (size == 0) return -1;

        pImageCodecInfo = (ImageCodecInfo*)(malloc(size));
        if (pImageCodecInfo == nullptr) return -1;

        GetImageEncoders(num, size, pImageCodecInfo);

        for (UINT j = 0; j < num; ++j) {
            if (wcscmp(pImageCodecInfo[j].MimeType, format) == 0) {
                *pClsid = pImageCodecInfo[j].Clsid;
                free(pImageCodecInfo);
                return j;
            }
        }

        free(pImageCodecInfo);
        return -1;
    }
};

// 静态成员初始化
ULONG_PTR ClipboardWindows::gdiplusToken_ = 0;
bool ClipboardWindows::gdiplusInitialized_ = false;

// 实现工厂函数
std::unique_ptr<ClipboardInterface> CreateClipboard() {
    return std::make_unique<ClipboardWindows>();
}

// 实现工具函数
std::string ContentTypeToString(ContentType type) {
    switch (type) {
        case ContentType::TEXT: return "text";
        case ContentType::IMAGE: return "image";
        case ContentType::HTML: return "html";
        case ContentType::FILES: return "files";
        case ContentType::EMPTY: return "empty";
        default: return "empty";
    }
}

ContentType StringToContentType(const std::string& typeStr) {
    if (typeStr == "text") return ContentType::TEXT;
    if (typeStr == "image") return ContentType::IMAGE;
    if (typeStr == "html") return ContentType::HTML;
    if (typeStr == "files") return ContentType::FILES;
    return ContentType::EMPTY;
}

bool IsValidBase64(const std::string& base64) {
    if (base64.empty()) return false;

    // 简单的 Base64 验证
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

    for (char c : base64) {
        if (chars.find(c) == std::string::npos) {
            return false;
        }
    }

    return true;
}

bool FileExists(const std::string& filePath) {
    // 转换为宽字符
    int wideLength = MultiByteToWideChar(CP_UTF8, 0, filePath.c_str(), -1, nullptr, 0);
    if (wideLength <= 0) return false;

    std::vector<wchar_t> wideBuffer(wideLength);
    MultiByteToWideChar(CP_UTF8, 0, filePath.c_str(), -1, wideBuffer.data(), wideLength);

    DWORD attributes = GetFileAttributesW(wideBuffer.data());
    return (attributes != INVALID_FILE_ATTRIBUTES);
}

std::string GetFileExtension(const std::string& filePath) {
    size_t pos = filePath.find_last_of('.');
    if (pos != std::string::npos) {
        return filePath.substr(pos);
    }
    return "";
}

bool IsImageFile(const std::string& filePath) {
    std::string ext = GetFileExtension(filePath);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    return ext == ".png" || ext == ".jpg" || ext == ".jpeg" ||
           ext == ".gif" || ext == ".bmp" || ext == ".tiff" ||
           ext == ".webp" || ext == ".svg";
}

} // namespace ClipboardNative
