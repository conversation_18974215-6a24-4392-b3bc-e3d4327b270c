/**
 * @file clipboard_native.mm
 * @brief 剪贴板原生实现 (macOS)
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include "../include/clipboard_native.h"
#import <Foundation/Foundation.h>
#import <AppKit/NSPasteboard.h>
#import <AppKit/NSImage.h>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include <atomic>
#include <queue>
#include <mutex>

namespace ClipboardNative {

/**
 * @class ClipboardChangeWorker
 * @brief 异步工作器，用于在主线程中调用 JavaScript 回调
 */
class ClipboardChangeWorker : public Napi::AsyncWorker {
public:
    ClipboardChangeWorker(Napi::Function& callback, const ClipboardData& data)
        : Napi::AsyncWorker(callback), data_(data) {}

protected:
    void Execute() override {
        // 在后台线程中执行，这里不需要做任何事情
        // 数据已经在构造函数中准备好了
    }

    void OnOK() override {
        Napi::Env env = Env();
        Napi::Object dataObj = Napi::Object::New(env);

        // 构建数据对象
        dataObj.Set("type", ContentTypeToString(data_.type));

        switch (data_.type) {
            case ContentType::TEXT:
                dataObj.Set("textData", data_.textData);
                break;
            case ContentType::IMAGE:
                dataObj.Set("imageData", data_.imageData);
                break;
            case ContentType::HTML:
                dataObj.Set("htmlData", data_.htmlData);
                break;
            case ContentType::FILES: {
                Napi::Array filesArray = Napi::Array::New(env, data_.filePaths.size());
                for (size_t i = 0; i < data_.filePaths.size(); i++) {
                    filesArray[i] = Napi::String::New(env, data_.filePaths[i]);
                }
                dataObj.Set("filePaths", filesArray);
                break;
            }
            default:
                break;
        }

        // 调用 JavaScript 回调
        Callback().Call({dataObj});
    }

private:
    ClipboardData data_;
};

/**
 * @class ClipboardMacOS
 * @brief macOS 平台剪贴板实现
 */
class ClipboardMacOS : public ClipboardInterface {
private:
    NSPasteboard* pasteboard_;
    Napi::Function callback_;
    std::atomic<bool> isWatching_;
    std::thread watchThread_;
    NSInteger lastChangeCount_;
    std::queue<ClipboardData> pendingCallbacks_;
    std::mutex callbackMutex_;
    
public:
    ClipboardMacOS() : isWatching_(false), lastChangeCount_(0) {
        pasteboard_ = [NSPasteboard generalPasteboard];
        lastChangeCount_ = [pasteboard_ changeCount];
    }
    
    ~ClipboardMacOS() {
        StopWatching();
    }
    
    bool HasData() override {
        @autoreleasepool {
            NSArray* types = [pasteboard_ types];
            return types && [types count] > 0;
        }
    }
    
    std::string GetText() override {
        @autoreleasepool {
            NSString* text = nil;
            if (@available(macOS 10.13, *)) {
                text = [pasteboard_ stringForType:NSPasteboardTypeString];
            } else {
                text = [pasteboard_ stringForType:NSStringPboardType];
            }

            if (text) {
                return std::string([text UTF8String]);
            }
            return "";
        }
    }
    
    bool SetText(const std::string& text) override {
        @autoreleasepool {
            [pasteboard_ clearContents];
            NSString* nsText = [NSString stringWithUTF8String:text.c_str()];

            if (@available(macOS 10.13, *)) {
                return [pasteboard_ setString:nsText forType:NSPasteboardTypeString];
            } else {
                return [pasteboard_ setString:nsText forType:NSStringPboardType];
            }
        }
    }
    
    std::string GetImage() override {
        @autoreleasepool {
            NSImage* image = [[NSImage alloc] initWithPasteboard:pasteboard_];
            if (!image) {
                return "";
            }
            
            // 转换为 PNG 数据
            CGImageRef cgImage = [image CGImageForProposedRect:NULL context:nil hints:nil];
            if (!cgImage) {
                return "";
            }
            
            NSBitmapImageRep* bitmapRep = [[NSBitmapImageRep alloc] initWithCGImage:cgImage];
            NSData* pngData = [bitmapRep representationUsingType:NSBitmapImageFileTypePNG properties:@{}];
            
            if (!pngData) {
                return "";
            }
            
            // 转换为 Base64
            NSString* base64String = [pngData base64EncodedStringWithOptions:0];
            return std::string([base64String UTF8String]);
        }
    }
    
    bool SetImage(const std::string& base64Data) override {
        @autoreleasepool {
            if (!IsValidBase64(base64Data)) {
                return false;
            }
            
            NSString* base64String = [NSString stringWithUTF8String:base64Data.c_str()];
            NSData* imageData = [[NSData alloc] initWithBase64EncodedString:base64String options:0];
            
            if (!imageData) {
                return false;
            }
            
            NSImage* image = [[NSImage alloc] initWithData:imageData];
            if (!image) {
                return false;
            }
            
            [pasteboard_ clearContents];
            return [pasteboard_ writeObjects:@[image]];
        }
    }
    
    std::string GetHtml() override {
        @autoreleasepool {
            NSString* html = nil;
            if (@available(macOS 10.13, *)) {
                html = [pasteboard_ stringForType:NSPasteboardTypeHTML];
            } else {
                html = [pasteboard_ stringForType:NSHTMLPboardType];
            }

            if (html) {
                return std::string([html UTF8String]);
            }
            return "";
        }
    }
    
    bool SetHtml(const std::string& html) override {
        @autoreleasepool {
            [pasteboard_ clearContents];
            NSString* nsHtml = [NSString stringWithUTF8String:html.c_str()];

            if (@available(macOS 10.13, *)) {
                return [pasteboard_ setString:nsHtml forType:NSPasteboardTypeHTML];
            } else {
                return [pasteboard_ setString:nsHtml forType:NSHTMLPboardType];
            }
        }
    }
    
    std::vector<std::string> GetFiles() override {
        @autoreleasepool {
            std::vector<std::string> files;

            // 使用兼容的方式获取文件路径
            if (@available(macOS 10.13, *)) {
                NSArray* fileURLs = [pasteboard_ readObjectsForClasses:@[[NSURL class]] options:@{NSPasteboardURLReadingFileURLsOnlyKey: @YES}];

                for (NSURL* url in fileURLs) {
                    if ([url isFileURL]) {
                        NSString* path = [url path];
                        files.push_back(std::string([path UTF8String]));
                    }
                }
            } else {
                // 降级处理：使用传统方式
                NSArray* types = [pasteboard_ types];
                if ([types containsObject:NSFilenamesPboardType]) {
                    NSArray* filenames = [pasteboard_ propertyListForType:NSFilenamesPboardType];
                    for (NSString* filename in filenames) {
                        files.push_back(std::string([filename UTF8String]));
                    }
                }
            }

            return files;
        }
    }
    
    bool SetFiles(const std::vector<std::string>& filePaths) override {
        @autoreleasepool {
            NSMutableArray* urls = [[NSMutableArray alloc] init];
            
            for (const auto& path : filePaths) {
                if (!FileExists(path)) {
                    continue;
                }
                
                NSString* nsPath = [NSString stringWithUTF8String:path.c_str()];
                NSURL* url = [NSURL fileURLWithPath:nsPath];
                if (url) {
                    [urls addObject:url];
                }
            }
            
            if ([urls count] == 0) {
                return false;
            }
            
            [pasteboard_ clearContents];
            return [pasteboard_ writeObjects:urls];
        }
    }
    
    bool Clear() override {
        @autoreleasepool {
            [pasteboard_ clearContents];
            return true;
        }
    }
    
    ContentType GetContentType() override {
        @autoreleasepool {
            NSArray* types = [pasteboard_ types];

            if (!types || [types count] == 0) {
                return ContentType::EMPTY;
            }

            // 检查文件 - 使用兼容的方式
            if (@available(macOS 10.13, *)) {
                if ([types containsObject:NSPasteboardTypeFileURL]) {
                    return ContentType::FILES;
                }
            } else {
                if ([types containsObject:NSFilenamesPboardType]) {
                    return ContentType::FILES;
                }
            }

            // 检查图片
            if ([NSImage canInitWithPasteboard:pasteboard_]) {
                return ContentType::IMAGE;
            }

            // 检查 HTML
            if (@available(macOS 10.13, *)) {
                if ([types containsObject:NSPasteboardTypeHTML]) {
                    return ContentType::HTML;
                }
            } else {
                if ([types containsObject:NSHTMLPboardType]) {
                    return ContentType::HTML;
                }
            }

            // 检查文本
            if (@available(macOS 10.13, *)) {
                if ([types containsObject:NSPasteboardTypeString]) {
                    return ContentType::TEXT;
                }
            } else {
                if ([types containsObject:NSStringPboardType]) {
                    return ContentType::TEXT;
                }
            }

            return ContentType::EMPTY;
        }
    }
    
    bool StartWatching(Napi::Function callback) override {
        if (isWatching_) {
            return false;
        }
        
        callback_ = callback;
        isWatching_ = true;
        
        watchThread_ = std::thread([this]() {
            while (isWatching_) {
                @autoreleasepool {
                    NSInteger currentChangeCount = [pasteboard_ changeCount];
                    if (currentChangeCount != lastChangeCount_) {
                        lastChangeCount_ = currentChangeCount;
                        
                        // 创建剪贴板数据对象
                        ClipboardData data;
                        data.type = GetContentType();

                        switch (data.type) {
                            case ContentType::TEXT:
                                data.textData = GetText();
                                break;
                            case ContentType::IMAGE:
                                data.imageData = GetImage();
                                break;
                            case ContentType::HTML:
                                data.htmlData = GetHtml();
                                break;
                            case ContentType::FILES:
                                data.filePaths = GetFiles();
                                break;
                            default:
                                break;
                        }

                        // 使用异步工作器调用 JavaScript 回调
                        if (!callback_.IsEmpty()) {
                            std::lock_guard<std::mutex> lock(callbackMutex_);
                            auto worker = new ClipboardChangeWorker(callback_, data);
                            worker->Queue();
                        }
                    }
                }
                
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        });
        
        return true;
    }
    
    bool StopWatching() override {
        if (!isWatching_) {
            return false;
        }
        
        isWatching_ = false;
        
        if (watchThread_.joinable()) {
            watchThread_.join();
        }
        
        return true;
    }
};

// 实现工厂函数
std::unique_ptr<ClipboardInterface> CreateClipboard() {
    return std::make_unique<ClipboardMacOS>();
}

// 实现工具函数
std::string ContentTypeToString(ContentType type) {
    switch (type) {
        case ContentType::TEXT: return "text";
        case ContentType::IMAGE: return "image";
        case ContentType::HTML: return "html";
        case ContentType::FILES: return "files";
        case ContentType::EMPTY: return "empty";
        default: return "empty";
    }
}

ContentType StringToContentType(const std::string& typeStr) {
    if (typeStr == "text") return ContentType::TEXT;
    if (typeStr == "image") return ContentType::IMAGE;
    if (typeStr == "html") return ContentType::HTML;
    if (typeStr == "files") return ContentType::FILES;
    return ContentType::EMPTY;
}

bool IsValidBase64(const std::string& base64) {
    if (base64.empty()) return false;
    
    // 简单的 Base64 验证
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    
    for (char c : base64) {
        if (chars.find(c) == std::string::npos) {
            return false;
        }
    }
    
    return true;
}

bool FileExists(const std::string& filePath) {
    @autoreleasepool {
        NSString* path = [NSString stringWithUTF8String:filePath.c_str()];
        return [[NSFileManager defaultManager] fileExistsAtPath:path];
    }
}

std::string GetFileExtension(const std::string& filePath) {
    size_t pos = filePath.find_last_of('.');
    if (pos != std::string::npos) {
        return filePath.substr(pos);
    }
    return "";
}

bool IsImageFile(const std::string& filePath) {
    std::string ext = GetFileExtension(filePath);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    
    return ext == ".png" || ext == ".jpg" || ext == ".jpeg" || 
           ext == ".gif" || ext == ".bmp" || ext == ".tiff" || 
           ext == ".webp" || ext == ".svg";
}

} // namespace ClipboardNative
