/**
 * @file clipboard_native_linux.cc
 * @brief 剪贴板原生实现 (Linux)
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include "../include/clipboard_native.h"
#include <X11/Xlib.h>
#include <X11/Xatom.h>
#include <X11/extensions/Xfixes.h>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include <atomic>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <cstring>
#include <unistd.h>
#include <sys/stat.h>
#include <queue>
#include <mutex>

namespace ClipboardNative {

/**
 * @class ClipboardChangeWorker
 * @brief 异步工作器，用于在主线程中调用 JavaScript 回调
 */
class ClipboardChangeWorker : public Napi::AsyncWorker {
public:
    ClipboardChangeWorker(Napi::Function& callback, const ClipboardData& data)
        : Napi::AsyncWorker(callback), data_(data) {}

protected:
    void Execute() override {
        // 在后台线程中执行，这里不需要做任何事情
        // 数据已经在构造函数中准备好了
    }

    void OnOK() override {
        Napi::Env env = Env();
        Napi::Object dataObj = Napi::Object::New(env);

        // 构建数据对象
        dataObj.Set("type", ContentTypeToString(data_.type));

        switch (data_.type) {
            case ContentType::TEXT:
                dataObj.Set("textData", data_.textData);
                break;
            case ContentType::IMAGE:
                dataObj.Set("imageData", data_.imageData);
                break;
            case ContentType::HTML:
                dataObj.Set("htmlData", data_.htmlData);
                break;
            case ContentType::FILES: {
                Napi::Array filesArray = Napi::Array::New(env, data_.filePaths.size());
                for (size_t i = 0; i < data_.filePaths.size(); i++) {
                    filesArray[i] = Napi::String::New(env, data_.filePaths[i]);
                }
                dataObj.Set("filePaths", filesArray);
                break;
            }
            default:
                break;
        }

        // 调用 JavaScript 回调
        Callback().Call({dataObj});
    }

private:
    ClipboardData data_;
};

/**
 * @class ClipboardLinux
 * @brief Linux 平台剪贴板实现
 */
class ClipboardLinux : public ClipboardInterface {
private:
    Display* display_;
    Window window_;
    Atom clipboard_atom_;
    Atom targets_atom_;
    Atom text_atom_;
    Atom utf8_atom_;
    Atom html_atom_;
    Atom png_atom_;
    Atom uri_list_atom_;
    Napi::Function callback_;
    std::atomic<bool> isWatching_;
    std::thread watchThread_;
    std::mutex callbackMutex_;
    
public:
    ClipboardLinux() : display_(nullptr), window_(0), isWatching_(false) {
        // 初始化 X11 连接
        display_ = XOpenDisplay(nullptr);
        if (!display_) {
            throw std::runtime_error("Failed to open X11 display");
        }
        
        // 创建窗口
        int screen = DefaultScreen(display_);
        window_ = XCreateSimpleWindow(display_, RootWindow(display_, screen),
                                     0, 0, 1, 1, 0, 0, 0);
        
        // 获取原子
        clipboard_atom_ = XInternAtom(display_, "CLIPBOARD", False);
        targets_atom_ = XInternAtom(display_, "TARGETS", False);
        text_atom_ = XInternAtom(display_, "TEXT", False);
        utf8_atom_ = XInternAtom(display_, "UTF8_STRING", False);
        html_atom_ = XInternAtom(display_, "text/html", False);
        png_atom_ = XInternAtom(display_, "image/png", False);
        uri_list_atom_ = XInternAtom(display_, "text/uri-list", False);
    }
    
    ~ClipboardLinux() {
        StopWatching();
        if (display_) {
            if (window_) {
                XDestroyWindow(display_, window_);
            }
            XCloseDisplay(display_);
        }
    }
    
    bool HasData() override {
        if (!display_) return false;
        
        Window owner = XGetSelectionOwner(display_, clipboard_atom_);
        return owner != None;
    }
    
    std::string GetText() override {
        if (!display_) return "";
        
        // 请求剪贴板内容
        XConvertSelection(display_, clipboard_atom_, utf8_atom_, 
                         clipboard_atom_, window_, CurrentTime);
        XFlush(display_);
        
        // 等待 SelectionNotify 事件
        XEvent event;
        while (true) {
            XNextEvent(display_, &event);
            if (event.type == SelectionNotify) {
                break;
            }
        }
        
        if (event.xselection.property == None) {
            return "";
        }
        
        // 读取属性
        Atom actual_type;
        int actual_format;
        unsigned long nitems, bytes_after;
        unsigned char* data = nullptr;
        
        int result = XGetWindowProperty(display_, window_, clipboard_atom_,
                                       0, LONG_MAX, False, AnyPropertyType,
                                       &actual_type, &actual_format,
                                       &nitems, &bytes_after, &data);
        
        std::string text;
        if (result == Success && data) {
            text = std::string(reinterpret_cast<char*>(data), nitems);
            XFree(data);
        }
        
        return text;
    }
    
    bool SetText(const std::string& text) override {
        if (!display_) return false;
        
        // 设置窗口属性
        XChangeProperty(display_, window_, clipboard_atom_, utf8_atom_, 8,
                       PropModeReplace, 
                       reinterpret_cast<const unsigned char*>(text.c_str()),
                       text.length());
        
        // 获取剪贴板所有权
        XSetSelectionOwner(display_, clipboard_atom_, window_, CurrentTime);
        XFlush(display_);
        
        // 验证所有权
        Window owner = XGetSelectionOwner(display_, clipboard_atom_);
        return owner == window_;
    }
    
    std::string GetImage() override {
        if (!display_) return "";
        
        // 请求 PNG 格式的图片
        XConvertSelection(display_, clipboard_atom_, png_atom_, 
                         clipboard_atom_, window_, CurrentTime);
        XFlush(display_);
        
        // 等待 SelectionNotify 事件
        XEvent event;
        while (true) {
            XNextEvent(display_, &event);
            if (event.type == SelectionNotify) {
                break;
            }
        }
        
        if (event.xselection.property == None) {
            return "";
        }
        
        // 读取属性
        Atom actual_type;
        int actual_format;
        unsigned long nitems, bytes_after;
        unsigned char* data = nullptr;
        
        int result = XGetWindowProperty(display_, window_, clipboard_atom_,
                                       0, LONG_MAX, False, AnyPropertyType,
                                       &actual_type, &actual_format,
                                       &nitems, &bytes_after, &data);
        
        std::string base64;
        if (result == Success && data) {
            // 将二进制数据转换为 Base64
            base64 = Base64Encode(data, nitems);
            XFree(data);
        }
        
        return base64;
    }
    
    bool SetImage(const std::string& base64Data) override {
        if (!display_ || !IsValidBase64(base64Data)) {
            return false;
        }
        
        // 解码 Base64
        std::vector<unsigned char> imageData = Base64Decode(base64Data);
        if (imageData.empty()) {
            return false;
        }
        
        // 设置窗口属性
        XChangeProperty(display_, window_, clipboard_atom_, png_atom_, 8,
                       PropModeReplace, imageData.data(), imageData.size());
        
        // 获取剪贴板所有权
        XSetSelectionOwner(display_, clipboard_atom_, window_, CurrentTime);
        XFlush(display_);
        
        // 验证所有权
        Window owner = XGetSelectionOwner(display_, clipboard_atom_);
        return owner == window_;
    }
    
    std::string GetHtml() override {
        if (!display_) return "";
        
        // 请求 HTML 格式
        XConvertSelection(display_, clipboard_atom_, html_atom_, 
                         clipboard_atom_, window_, CurrentTime);
        XFlush(display_);
        
        // 等待 SelectionNotify 事件
        XEvent event;
        while (true) {
            XNextEvent(display_, &event);
            if (event.type == SelectionNotify) {
                break;
            }
        }
        
        if (event.xselection.property == None) {
            return "";
        }
        
        // 读取属性
        Atom actual_type;
        int actual_format;
        unsigned long nitems, bytes_after;
        unsigned char* data = nullptr;
        
        int result = XGetWindowProperty(display_, window_, clipboard_atom_,
                                       0, LONG_MAX, False, AnyPropertyType,
                                       &actual_type, &actual_format,
                                       &nitems, &bytes_after, &data);
        
        std::string html;
        if (result == Success && data) {
            html = std::string(reinterpret_cast<char*>(data), nitems);
            XFree(data);
        }
        
        return html;
    }
    
    bool SetHtml(const std::string& html) override {
        if (!display_) return false;
        
        // 设置窗口属性
        XChangeProperty(display_, window_, clipboard_atom_, html_atom_, 8,
                       PropModeReplace, 
                       reinterpret_cast<const unsigned char*>(html.c_str()),
                       html.length());
        
        // 获取剪贴板所有权
        XSetSelectionOwner(display_, clipboard_atom_, window_, CurrentTime);
        XFlush(display_);
        
        // 验证所有权
        Window owner = XGetSelectionOwner(display_, clipboard_atom_);
        return owner == window_;
    }
    
    std::vector<std::string> GetFiles() override {
        std::vector<std::string> files;
        
        if (!display_) return files;
        
        // 请求 URI 列表格式
        XConvertSelection(display_, clipboard_atom_, uri_list_atom_, 
                         clipboard_atom_, window_, CurrentTime);
        XFlush(display_);
        
        // 等待 SelectionNotify 事件
        XEvent event;
        while (true) {
            XNextEvent(display_, &event);
            if (event.type == SelectionNotify) {
                break;
            }
        }
        
        if (event.xselection.property == None) {
            return files;
        }
        
        // 读取属性
        Atom actual_type;
        int actual_format;
        unsigned long nitems, bytes_after;
        unsigned char* data = nullptr;
        
        int result = XGetWindowProperty(display_, window_, clipboard_atom_,
                                       0, LONG_MAX, False, AnyPropertyType,
                                       &actual_type, &actual_format,
                                       &nitems, &bytes_after, &data);
        
        if (result == Success && data) {
            std::string uriList(reinterpret_cast<char*>(data), nitems);
            XFree(data);
            
            // 解析 URI 列表
            std::istringstream iss(uriList);
            std::string line;
            while (std::getline(iss, line)) {
                if (line.substr(0, 7) == "file://") {
                    std::string path = line.substr(7);
                    // URL 解码
                    path = UrlDecode(path);
                    if (FileExists(path)) {
                        files.push_back(path);
                    }
                }
            }
        }
        
        return files;
    }
    
    bool SetFiles(const std::vector<std::string>& filePaths) override {
        if (!display_ || filePaths.empty()) {
            return false;
        }
        
        // 验证文件存在
        for (const auto& path : filePaths) {
            if (!FileExists(path)) {
                return false;
            }
        }
        
        // 构建 URI 列表
        std::string uriList;
        for (const auto& path : filePaths) {
            uriList += "file://" + UrlEncode(path) + "\n";
        }
        
        // 设置窗口属性
        XChangeProperty(display_, window_, clipboard_atom_, uri_list_atom_, 8,
                       PropModeReplace, 
                       reinterpret_cast<const unsigned char*>(uriList.c_str()),
                       uriList.length());
        
        // 获取剪贴板所有权
        XSetSelectionOwner(display_, clipboard_atom_, window_, CurrentTime);
        XFlush(display_);
        
        // 验证所有权
        Window owner = XGetSelectionOwner(display_, clipboard_atom_);
        return owner == window_;
    }

    bool Clear() override {
        if (!display_) return false;

        // 清除剪贴板所有权
        XSetSelectionOwner(display_, clipboard_atom_, None, CurrentTime);
        XFlush(display_);

        return true;
    }

    ContentType GetContentType() override {
        if (!display_) return ContentType::EMPTY;

        Window owner = XGetSelectionOwner(display_, clipboard_atom_);
        if (owner == None) {
            return ContentType::EMPTY;
        }

        // 请求支持的目标格式
        XConvertSelection(display_, clipboard_atom_, targets_atom_,
                         clipboard_atom_, window_, CurrentTime);
        XFlush(display_);

        // 等待 SelectionNotify 事件
        XEvent event;
        while (true) {
            XNextEvent(display_, &event);
            if (event.type == SelectionNotify) {
                break;
            }
        }

        if (event.xselection.property == None) {
            return ContentType::EMPTY;
        }

        // 读取支持的目标
        Atom actual_type;
        int actual_format;
        unsigned long nitems, bytes_after;
        Atom* targets = nullptr;

        int result = XGetWindowProperty(display_, window_, clipboard_atom_,
                                       0, LONG_MAX, False, XA_ATOM,
                                       &actual_type, &actual_format,
                                       &nitems, &bytes_after,
                                       reinterpret_cast<unsigned char**>(&targets));

        ContentType type = ContentType::EMPTY;
        if (result == Success && targets) {
            for (unsigned long i = 0; i < nitems; i++) {
                if (targets[i] == uri_list_atom_) {
                    type = ContentType::FILES;
                    break;
                } else if (targets[i] == png_atom_) {
                    type = ContentType::IMAGE;
                } else if (targets[i] == html_atom_ && type == ContentType::EMPTY) {
                    type = ContentType::HTML;
                } else if ((targets[i] == utf8_atom_ || targets[i] == text_atom_) &&
                          type == ContentType::EMPTY) {
                    type = ContentType::TEXT;
                }
            }
            XFree(targets);
        }

        return type;
    }

    bool StartWatching(Napi::Function callback) override {
        if (isWatching_ || !display_) {
            return false;
        }

        // 检查是否支持 XFixes 扩展
        int event_base, error_base;
        if (!XFixesQueryExtension(display_, &event_base, &error_base)) {
            return false;
        }

        callback_ = callback;
        isWatching_ = true;

        // 选择剪贴板事件
        XFixesSelectSelectionInput(display_, window_, clipboard_atom_,
                                  XFixesSetSelectionOwnerNotifyMask);

        watchThread_ = std::thread([this]() {
            while (isWatching_) {
                XEvent event;
                if (XPending(display_) > 0) {
                    XNextEvent(display_, &event);

                    if (event.type == XFixesSelectionNotify) {
                        // 创建剪贴板数据对象
                        ClipboardData data;
                        data.type = GetContentType();

                        switch (data.type) {
                            case ContentType::TEXT:
                                data.textData = GetText();
                                break;
                            case ContentType::IMAGE:
                                data.imageData = GetImage();
                                break;
                            case ContentType::HTML:
                                data.htmlData = GetHtml();
                                break;
                            case ContentType::FILES:
                                data.filePaths = GetFiles();
                                break;
                            default:
                                break;
                        }

                        // 使用异步工作器调用 JavaScript 回调
                        if (!callback_.IsEmpty()) {
                            std::lock_guard<std::mutex> lock(callbackMutex_);
                            auto worker = new ClipboardChangeWorker(callback_, data);
                            worker->Queue();
                        }
                    }
                }

                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        });

        return true;
    }

    bool StopWatching() override {
        if (!isWatching_) {
            return false;
        }

        isWatching_ = false;

        if (watchThread_.joinable()) {
            watchThread_.join();
        }

        return true;
    }

private:
    /**
     * @brief Base64 编码
     */
    std::string Base64Encode(const unsigned char* data, size_t length) {
        static const char base64_chars[] =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

        std::string result;
        int val = 0, valb = -6;
        for (size_t i = 0; i < length; i++) {
            val = (val << 8) + data[i];
            valb += 8;
            while (valb >= 0) {
                result.push_back(base64_chars[(val >> valb) & 0x3F]);
                valb -= 6;
            }
        }
        if (valb > -6) {
            result.push_back(base64_chars[((val << 8) >> (valb + 8)) & 0x3F]);
        }
        while (result.size() % 4) {
            result.push_back('=');
        }
        return result;
    }

    /**
     * @brief Base64 解码
     */
    std::vector<unsigned char> Base64Decode(const std::string& base64) {
        static const int T[128] = {
            -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
            -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
            -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,62, -1,-1,-1,63,
            52,53,54,55, 56,57,58,59, 60,61,-1,-1, -1,-2,-1,-1,
            -1, 0, 1, 2,  3, 4, 5, 6,  7, 8, 9,10, 11,12,13,14,
            15,16,17,18, 19,20,21,22, 23,24,25,-1, -1,-1,-1,-1,
            -1,26,27,28, 29,30,31,32, 33,34,35,36, 37,38,39,40,
            41,42,43,44, 45,46,47,48, 49,50,51,-1, -1,-1,-1,-1
        };

        std::vector<unsigned char> result;
        int val = 0, valb = -8;
        for (unsigned char c : base64) {
            if (T[c] == -1) break;
            if (T[c] == -2) break;
            val = (val << 6) + T[c];
            valb += 6;
            if (valb >= 0) {
                result.push_back(static_cast<unsigned char>((val >> valb) & 0xFF));
                valb -= 8;
            }
        }
        return result;
    }

    /**
     * @brief URL 编码
     */
    std::string UrlEncode(const std::string& str) {
        std::ostringstream encoded;
        encoded.fill('0');
        encoded << std::hex;

        for (char c : str) {
            if (isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~' || c == '/') {
                encoded << c;
            } else {
                encoded << '%' << std::setw(2) << static_cast<int>(static_cast<unsigned char>(c));
            }
        }

        return encoded.str();
    }

    /**
     * @brief URL 解码
     */
    std::string UrlDecode(const std::string& str) {
        std::string decoded;
        for (size_t i = 0; i < str.length(); i++) {
            if (str[i] == '%' && i + 2 < str.length()) {
                int value;
                std::istringstream iss(str.substr(i + 1, 2));
                if (iss >> std::hex >> value) {
                    decoded += static_cast<char>(value);
                    i += 2;
                } else {
                    decoded += str[i];
                }
            } else {
                decoded += str[i];
            }
        }
        return decoded;
    }
};

// 实现工厂函数
std::unique_ptr<ClipboardInterface> CreateClipboard() {
    return std::make_unique<ClipboardLinux>();
}

// 实现工具函数
std::string ContentTypeToString(ContentType type) {
    switch (type) {
        case ContentType::TEXT: return "text";
        case ContentType::IMAGE: return "image";
        case ContentType::HTML: return "html";
        case ContentType::FILES: return "files";
        case ContentType::EMPTY: return "empty";
        default: return "empty";
    }
}

ContentType StringToContentType(const std::string& typeStr) {
    if (typeStr == "text") return ContentType::TEXT;
    if (typeStr == "image") return ContentType::IMAGE;
    if (typeStr == "html") return ContentType::HTML;
    if (typeStr == "files") return ContentType::FILES;
    return ContentType::EMPTY;
}

bool IsValidBase64(const std::string& base64) {
    if (base64.empty()) return false;

    // 简单的 Base64 验证
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

    for (char c : base64) {
        if (chars.find(c) == std::string::npos) {
            return false;
        }
    }

    return true;
}

bool FileExists(const std::string& filePath) {
    struct stat buffer;
    return (stat(filePath.c_str(), &buffer) == 0);
}

std::string GetFileExtension(const std::string& filePath) {
    size_t pos = filePath.find_last_of('.');
    if (pos != std::string::npos) {
        return filePath.substr(pos);
    }
    return "";
}

bool IsImageFile(const std::string& filePath) {
    std::string ext = GetFileExtension(filePath);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    return ext == ".png" || ext == ".jpg" || ext == ".jpeg" ||
           ext == ".gif" || ext == ".bmp" || ext == ".tiff" ||
           ext == ".webp" || ext == ".svg";
}

} // namespace ClipboardNative
