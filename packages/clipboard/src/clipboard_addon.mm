/**
 * @file clipboard_addon.mm
 * @brief 剪贴板 Node.js 插件主入口文件 (跨平台)
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include <napi.h>
#include "../include/clipboard_native.h"
#include <memory>
#include <string>
#include <vector>

using namespace ClipboardNative;

// 全局剪贴板实例
static std::unique_ptr<ClipboardInterface> g_clipboard;

/**
 * @brief 初始化剪贴板实例
 */
void InitializeClipboard() {
    if (!g_clipboard) {
        g_clipboard = CreateClipboard();
    }
}

/**
 * @brief 检查剪贴板是否有数据
 * @param info N-API 调用信息
 * @return 布尔值
 */
Napi::Value HasData(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        InitializeClipboard();
        bool hasData = g_clipboard->HasData();
        return Napi::Boolean::New(env, hasData);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 获取剪贴板文本内容
 * @param info N-API 调用信息
 * @return 字符串
 */
Napi::Value GetText(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        InitializeClipboard();
        std::string text = g_clipboard->GetText();
        return Napi::String::New(env, text);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 设置剪贴板文本内容
 * @param info N-API 调用信息
 * @return 布尔值
 */
Napi::Value SetText(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected a string argument").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        InitializeClipboard();
        std::string text = info[0].As<Napi::String>().Utf8Value();
        bool result = g_clipboard->SetText(text);
        return Napi::Boolean::New(env, result);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 获取剪贴板图片数据
 * @param info N-API 调用信息
 * @return 字符串或 null
 */
Napi::Value GetImage(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        InitializeClipboard();
        std::string imageData = g_clipboard->GetImage();
        if (imageData.empty()) {
            return env.Null();
        }
        return Napi::String::New(env, imageData);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 设置剪贴板图片数据
 * @param info N-API 调用信息
 * @return 布尔值
 */
Napi::Value SetImage(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected a Base64 string argument").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        InitializeClipboard();
        std::string base64Data = info[0].As<Napi::String>().Utf8Value();
        bool result = g_clipboard->SetImage(base64Data);
        return Napi::Boolean::New(env, result);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 获取剪贴板HTML内容
 * @param info N-API 调用信息
 * @return 字符串
 */
Napi::Value GetHtml(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        InitializeClipboard();
        std::string html = g_clipboard->GetHtml();
        return Napi::String::New(env, html);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 设置剪贴板HTML内容
 * @param info N-API 调用信息
 * @return 布尔值
 */
Napi::Value SetHtml(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected an HTML string argument").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        InitializeClipboard();
        std::string html = info[0].As<Napi::String>().Utf8Value();
        bool result = g_clipboard->SetHtml(html);
        return Napi::Boolean::New(env, result);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 获取剪贴板文件路径列表
 * @param info N-API 调用信息
 * @return 数组
 */
Napi::Value GetFiles(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        InitializeClipboard();
        std::vector<std::string> files = g_clipboard->GetFiles();
        
        Napi::Array result = Napi::Array::New(env, files.size());
        for (size_t i = 0; i < files.size(); i++) {
            result[i] = Napi::String::New(env, files[i]);
        }
        
        return result;
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 设置剪贴板文件路径
 * @param info N-API 调用信息
 * @return 布尔值
 */
Napi::Value SetFiles(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsArray()) {
        Napi::TypeError::New(env, "Expected an array of file paths").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        InitializeClipboard();
        Napi::Array fileArray = info[0].As<Napi::Array>();
        std::vector<std::string> filePaths;
        
        for (uint32_t i = 0; i < fileArray.Length(); i++) {
            Napi::Value value = fileArray[i];
            if (value.IsString()) {
                filePaths.push_back(value.As<Napi::String>().Utf8Value());
            }
        }
        
        bool result = g_clipboard->SetFiles(filePaths);
        return Napi::Boolean::New(env, result);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 清空剪贴板
 * @param info N-API 调用信息
 * @return 布尔值
 */
Napi::Value Clear(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        InitializeClipboard();
        bool result = g_clipboard->Clear();
        return Napi::Boolean::New(env, result);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 获取剪贴板内容类型
 * @param info N-API 调用信息
 * @return 字符串
 */
Napi::Value GetContentType(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        InitializeClipboard();
        ContentType type = g_clipboard->GetContentType();
        std::string typeStr = ContentTypeToString(type);
        return Napi::String::New(env, typeStr);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 开始监听剪贴板变化
 * @param info N-API 调用信息
 * @return 布尔值
 */
Napi::Value WatchClipboard(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsFunction()) {
        Napi::TypeError::New(env, "Expected a callback function").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        InitializeClipboard();
        Napi::Function callback = info[0].As<Napi::Function>();
        bool result = g_clipboard->StartWatching(callback);
        return Napi::Boolean::New(env, result);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 停止监听剪贴板变化
 * @param info N-API 调用信息
 * @return 布尔值
 */
Napi::Value UnwatchClipboard(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        InitializeClipboard();
        bool result = g_clipboard->StopWatching();
        return Napi::Boolean::New(env, result);
    } catch (const std::exception& e) {
        Napi::TypeError::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 初始化模块
 * @param env N-API 环境
 * @param exports 导出对象
 * @return 导出对象
 */
Napi::Object Init(Napi::Env env, Napi::Object exports) {
    exports.Set(Napi::String::New(env, "hasData"), Napi::Function::New(env, HasData));
    exports.Set(Napi::String::New(env, "getText"), Napi::Function::New(env, GetText));
    exports.Set(Napi::String::New(env, "setText"), Napi::Function::New(env, SetText));
    exports.Set(Napi::String::New(env, "getImage"), Napi::Function::New(env, GetImage));
    exports.Set(Napi::String::New(env, "setImage"), Napi::Function::New(env, SetImage));
    exports.Set(Napi::String::New(env, "getHtml"), Napi::Function::New(env, GetHtml));
    exports.Set(Napi::String::New(env, "setHtml"), Napi::Function::New(env, SetHtml));
    exports.Set(Napi::String::New(env, "getFiles"), Napi::Function::New(env, GetFiles));
    exports.Set(Napi::String::New(env, "setFiles"), Napi::Function::New(env, SetFiles));
    exports.Set(Napi::String::New(env, "clear"), Napi::Function::New(env, Clear));
    exports.Set(Napi::String::New(env, "getContentType"), Napi::Function::New(env, GetContentType));
    exports.Set(Napi::String::New(env, "watchClipboard"), Napi::Function::New(env, WatchClipboard));
    exports.Set(Napi::String::New(env, "unwatchClipboard"), Napi::Function::New(env, UnwatchClipboard));
    
    return exports;
}

NODE_API_MODULE(clipboard, Init)
