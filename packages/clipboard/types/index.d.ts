/**
 * @file index.d.ts
 * @brief 剪贴板管理模块的TypeScript类型定义
 * <AUTHOR> Toolbox
 * @date 2024
 */

/// <reference types="node" />

/**
 * @type ClipboardContentType
 * @brief 剪贴板内容类型
 */
export type ClipboardContentType = 'text' | 'image' | 'html' | 'files' | 'empty';

/**
 * @interface ClipboardInfo
 * @brief 剪贴板信息接口
 */
export interface ClipboardInfo {
    /** 是否有数据 */
    hasData: boolean;
    
    /** 内容类型 */
    contentType: ClipboardContentType;
    
    /** 时间戳 */
    timestamp: number;
    
    /** 文本长度（仅当内容类型为text时） */
    textLength?: number;
    
    /** 预览内容（仅当内容类型为text或html时） */
    preview?: string;
    
    /** 是否有图片（仅当内容类型为image时） */
    hasImage?: boolean;
    
    /** 图片大小（仅当内容类型为image时） */
    imageSize?: number;
    
    /** HTML长度（仅当内容类型为html时） */
    htmlLength?: number;
    
    /** 文件数量（仅当内容类型为files时） */
    fileCount?: number;
    
    /** 文件列表（仅当内容类型为files时） */
    files?: string[];
}

/**
 * @interface ClipboardData
 * @brief 剪贴板数据结构
 */
export interface ClipboardData {
    /** 内容类型 */
    type: ClipboardContentType;

    /** 文本数据（当类型为text时） */
    textData?: string;

    /** 图片数据（当类型为image时，Base64格式） */
    imageData?: string;

    /** HTML数据（当类型为html时） */
    htmlData?: string;

    /** 文件路径列表（当类型为files时） */
    filePaths?: string[];
}

/**
 * @type ClipboardWatcherCallback
 * @brief 剪贴板监听器回调函数类型
 */
export type ClipboardWatcherCallback = (data: ClipboardData) => void;

/**
 * @interface ClipboardOptions
 * @brief 剪贴板操作选项
 */
export interface ClipboardOptions {
    /** 是否使用缓存 */
    useCache?: boolean;

    /** 超时时间（毫秒） */
    timeout?: number;

    /** 是否静默失败 */
    silent?: boolean;
}

/**
 * @class ClipboardError
 * @brief 剪贴板操作错误类
 */
export declare class ClipboardError extends Error {
    /** 错误代码 */
    code: string;

    /** 原始错误 */
    originalError?: Error;

    constructor(message: string, code?: string, originalError?: Error);
}

/**
 * @type ClipboardErrorCode
 * @brief 剪贴板错误代码
 */
export type ClipboardErrorCode =
    | 'NATIVE_MODULE_NOT_AVAILABLE'
    | 'INVALID_ARGUMENT'
    | 'OPERATION_FAILED'
    | 'PERMISSION_DENIED'
    | 'TIMEOUT'
    | 'UNSUPPORTED_FORMAT'
    | 'FILE_NOT_FOUND'
    | 'INVALID_BASE64';



/**
 * @class ClipboardManager
 * @brief 智能剪贴板管理器，支持跨平台的剪贴板操作
 */
export declare class ClipboardManager {
    /**
     * 检查剪贴板是否有内容
     * @returns 是否有内容
     */
    static hasData(): boolean;

    /**
     * 获取剪贴板文本内容
     * @returns 文本内容
     */
    static getText(): string;

    /**
     * 设置剪贴板文本内容
     * @param text 要设置的文本
     * @returns 是否成功
     */
    static setText(text: string): boolean;

    /**
     * 获取剪贴板图片数据 (Base64)
     * @returns Base64格式的图片数据，如果没有图片则返回null
     */
    static getImage(): string | null;

    /**
     * 设置剪贴板图片数据
     * @param base64Data Base64格式的图片数据
     * @returns 是否成功
     */
    static setImage(base64Data: string): boolean;

    /**
     * 获取剪贴板HTML内容
     * @returns HTML内容
     */
    static getHtml(): string;

    /**
     * 设置剪贴板HTML内容
     * @param html HTML内容
     * @returns 是否成功
     */
    static setHtml(html: string): boolean;

    /**
     * 获取剪贴板文件路径列表
     * @returns 文件路径数组
     */
    static getFiles(): string[];

    /**
     * 设置剪贴板文件路径
     * @param filePaths 文件路径数组
     * @returns 是否成功
     */
    static setFiles(filePaths: string[]): boolean;

    /**
     * 清空剪贴板
     * @returns 是否成功
     */
    static clear(): boolean;

    /**
     * 获取剪贴板内容类型
     * @returns 内容类型: 'text', 'image', 'html', 'files', 'empty'
     */
    static getContentType(): ClipboardContentType;

    /**
     * 监听剪贴板变化 (仅在支持的平台上)
     * @param callback 回调函数
     * @returns 是否成功开始监听
     */
    static watchClipboard(callback: ClipboardWatcherCallback): boolean;

    /**
     * 停止监听剪贴板变化
     * @returns 是否成功停止监听
     */
    static unwatchClipboard(): boolean;
}

// 导出单个静态方法
export declare const hasData: () => boolean;
export declare const getText: () => string;
export declare const setText: (text: string) => boolean;
export declare const getImage: () => string | null;
export declare const setImage: (base64Data: string) => boolean;
export declare const getHtml: () => string;
export declare const setHtml: (html: string) => boolean;
export declare const getFiles: () => string[];
export declare const setFiles: (filePaths: string[]) => boolean;
export declare const clear: () => boolean;
export declare const getContentType: () => ClipboardContentType;
export declare const watchClipboard: (callback: ClipboardWatcherCallback) => boolean;
export declare const unwatchClipboard: () => boolean;

/**
 * 默认导出（ES6模块兼容）
 */
export default ClipboardManager;
