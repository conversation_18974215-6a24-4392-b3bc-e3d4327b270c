/**
 * @file index.js
 * @brief 剪贴板管理模块的JavaScript接口
 * <AUTHOR> Toolbox
 * @date 2024
 */

import { createRequire } from 'node:module'
import fs from 'fs'
import path from 'path'

const require = createRequire(import.meta.url)

// 加载原生模块
let nativeModule
try {
  nativeModule = require('../build/Release/clipboard.node')
} catch (error) {
  console.error('Failed to load native clipboard module:', error.message)
  // 提供一个空的实现作为后备
  nativeModule = {
    hasData: () => { throw new Error('Native module not available') },
    getText: () => { throw new Error('Native module not available') },
    setText: () => { throw new Error('Native module not available') },
    getImage: () => { throw new Error('Native module not available') },
    setImage: () => { throw new Error('Native module not available') },
    getHtml: () => { throw new Error('Native module not available') },
    setHtml: () => { throw new Error('Native module not available') },
    getFiles: () => { throw new Error('Native module not available') },
    setFiles: () => { throw new Error('Native module not available') },
    clear: () => { throw new Error('Native module not available') },
    getContentType: () => { throw new Error('Native module not available') },
    watchClipboard: () => { throw new Error('Native module not available') },
    unwatchClipboard: () => { throw new Error('Native module not available') },
  }
}

/**
 * @class ClipboardManager
 * @brief 剪贴板管理器类，提供高级API
 */
class ClipboardManager {
  /**
   * @brief 构造函数
   */
  constructor() {
    this._cache = new Map()
    this._cacheTimeout = 5000 // 5秒缓存
    this._lastCacheTime = 0
    this._watchers = new Set()
    this._isWatching = false
  }
  /**
   * @brief 检查剪贴板是否有内容
   * @returns {Promise<boolean>} 是否有内容
   */
  async hasData() {
    try {
      return nativeModule.hasData()
    } catch (error) {
      console.error('Error checking clipboard data:', error)
      throw error
    }
  }

  /**
   * @brief 获取剪贴板文本内容
   * @param {boolean} useCache 是否使用缓存
   * @returns {Promise<string>} 文本内容
   */
  async getText(useCache = true) {
    try {
      const cacheKey = 'text'
      const now = Date.now()

      // 检查缓存
      if (useCache && this._cache.has(cacheKey)) {
        const cached = this._cache.get(cacheKey)
        if (now - cached.timestamp < this._cacheTimeout) {
          return cached.data
        }
      }

      const text = nativeModule.getText()

      // 更新缓存
      if (useCache) {
        this._cache.set(cacheKey, {
          data: text,
          timestamp: now,
        })
      }

      return text
    } catch (error) {
      console.error('Error getting clipboard text:', error)
      throw error
    }
  }

  /**
   * @brief 设置剪贴板文本内容
   * @param {string} text 要设置的文本
   * @returns {Promise<boolean>} 是否成功
   */
  async setText(text) {
    try {
      if (typeof text !== 'string') {
        throw new TypeError('Text must be a string')
      }

      const result = nativeModule.setText(text)

      // 清除相关缓存
      this._cache.delete('text')
      this._cache.delete('contentType')

      return result
    } catch (error) {
      console.error('Error setting clipboard text:', error)
      throw error
    }
  }

  /**
   * 获取剪贴板图片数据 (Base64)
   * @returns {string|null} Base64格式的图片数据，如果没有图片则返回null
   */
  static getImage() {
    return clipboard.getImage()
  }

  /**
   * 设置剪贴板图片数据
   * @param {string} base64Data Base64格式的图片数据
   * @returns {boolean} 是否成功
   */
  static setImage(base64Data) {
    if (typeof base64Data !== 'string') {
      throw new TypeError('图片数据必须是Base64字符串')
    }
    return clipboard.setImage(base64Data)
  }

  /**
   * 获取剪贴板HTML内容
   * @returns {string} HTML内容
   */
  static getHtml() {
    return clipboard.getHtml()
  }

  /**
   * 设置剪贴板HTML内容
   * @param {string} html HTML内容
   * @returns {boolean} 是否成功
   */
  static setHtml(html) {
    if (typeof html !== 'string') {
      throw new TypeError('HTML内容必须是字符串类型')
    }
    return clipboard.setHtml(html)
  }

  /**
   * 获取剪贴板文件路径列表
   * @returns {string[]} 文件路径数组
   */
  static getFiles() {
    return clipboard.getFiles()
  }

  /**
   * 设置剪贴板文件路径
   * @param {string[]} filePaths 文件路径数组
   * @returns {boolean} 是否成功
   */
  static setFiles(filePaths) {
    if (!Array.isArray(filePaths)) {
      throw new TypeError('文件路径必须是数组类型')
    }
    return clipboard.setFiles(filePaths)
  }

  /**
   * 清空剪贴板
   * @returns {boolean} 是否成功
   */
  static clear() {
    return clipboard.clear()
  }

  /**
   * 获取剪贴板内容类型
   * @returns {string} 内容类型: 'text', 'image', 'html', 'files', 'empty'
   */
  static getContentType() {
    return clipboard.getContentType()
  }

  /**
   * 监听剪贴板变化 (仅在支持的平台上)
   * @param {Function} callback 回调函数
   */
  static watchClipboard(callback) {
    if (typeof callback !== 'function') {
      throw new TypeError('回调函数必须是function类型')
    }
    return clipboard.watchClipboard(callback)
  }

  /**
   * 停止监听剪贴板变化
   */
  static unwatchClipboard() {
    return clipboard.unwatchClipboard()
  }
}

// 导出单个静态方法
export const hasData = ClipboardManager.hasData
export const getText = ClipboardManager.getText
export const setText = ClipboardManager.setText
export const getImage = ClipboardManager.getImage
export const setImage = ClipboardManager.setImage
export const getHtml = ClipboardManager.getHtml
export const setHtml = ClipboardManager.setHtml
export const getFiles = ClipboardManager.getFiles
export const setFiles = ClipboardManager.setFiles
export const clear = ClipboardManager.clear
export const getContentType = ClipboardManager.getContentType
export const watchClipboard = ClipboardManager.watchClipboard
export const unwatchClipboard = ClipboardManager.unwatchClipboard

export default ClipboardManager
