{
  "targets": [
    {
      "target_name": "clipboard",
      "include_dirs": [
        "<!@(node -p \"require('node-addon-api').include\")",
        "include"
      ],
      "dependencies": [
        "<!(node -p \"require('node-addon-api').gyp\")"
      ],
      "defines": [
        "NAPI_DISABLE_CPP_EXCEPTIONS"
      ],
      "cflags!": [
        "-fno-exceptions"
      ],
      "cflags_cc!": [
        "-fno-exceptions"
      ],
      "msvs_settings": {
        "VCCLCompilerTool": {
          "ExceptionHandling": 1,
          "AdditionalOptions": [ "/bigobj", "/std:c++17", "/utf-8" ],
          "LanguageStandard": "stdcpp17"
        }
      },
      "conditions": [
        ['OS=="mac"', {
          "sources": [
            "src/clipboard_addon.mm",
            "src/clipboard_native.mm"
          ],
          "libraries": [ "-framework AppKit", "-framework Foundation" ],
          "xcode_settings": {
            "GCC_ENABLE_CPP_EXCEPTIONS": "YES",
            "CLANG_CXX_LIBRARY": "libc++",
            "MACOSX_DEPLOYMENT_TARGET": "10.12",
            "ARCHS": ["x86_64"],
            "ONLY_ACTIVE_ARCH": "NO",
            "OTHER_CFLAGS": [
              "-fobjc-arc",
              "-Wno-deprecated-declarations"
            ],
            "OTHER_CPLUSPLUSFLAGS": ["-std=c++17", "-stdlib=libc++", "-Wno-deprecated-declarations", "-ObjC++"]
          }
        }],
        ['OS=="win"', {
          "sources": [
            "src/clipboard_addon_windows.cc",
            "src/clipboard_native_windows.cc"
          ],
          "conditions": [
            [ "target_arch=='ia32'", {
              "msvs_settings": {
                "VCCLCompilerTool": {
                  "AdditionalOptions": [ "/bigobj", "/std:c++17", "/utf-8" ],
                  "ExceptionHandling": 1,
                  "LanguageStandard": "stdcpp17"
                }
              }
            }],
            [ "target_arch=='x64'", {
              "msvs_settings": {
                "VCCLCompilerTool": {
                  "AdditionalOptions": [ "/bigobj", "/std:c++17", "/utf-8" ],
                  "ExceptionHandling": 1,
                  "LanguageStandard": "stdcpp17"
                }
              }
            }],
            [ "target_arch=='arm64'", {
              "msvs_settings": {
                "VCCLCompilerTool": {
                  "AdditionalOptions": [ "/bigobj", "/std:c++17", "/utf-8" ],
                  "ExceptionHandling": 1,
                  "LanguageStandard": "stdcpp17"
                }
              }
            }]
          ],
          "libraries": [
            "-luser32.lib",
            "-lole32.lib",
            "-loleaut32.lib",
            "-luuid.lib",
            "-lshell32.lib",
            "-lgdi32.lib",
            "-lgdiplus.lib"
          ]
        }],
        ['OS=="linux"', {
          "sources": [
            "src/clipboard_addon_linux.cc",
            "src/clipboard_native_linux.cc"
          ],
          "libraries": [ "-lX11", "-lXmu", "-lXfixes" ],
          "conditions": [
            [ "target_arch=='x64'", {
              "cflags": [ "-m64" ],
              "cflags_cc": [ "-std=c++17", "-m64" ],
              "ldflags": [ "-m64" ]
            }],
            [ "target_arch=='arm64'", {
              "cflags": [ "-march=armv8-a" ],
              "cflags_cc": [ "-std=c++17", "-march=armv8-a" ]
            }],
            [ "target_arch=='arm'", {
              "cflags": [ "-march=armv7-a", "-mfpu=neon" ],
              "cflags_cc": [ "-std=c++17", "-march=armv7-a", "-mfpu=neon" ]
            }]
          ]
        }]
      ]
    }
  ]
}