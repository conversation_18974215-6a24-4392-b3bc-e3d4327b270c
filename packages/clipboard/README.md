# 智能剪贴板管理器

一个跨平台的 Node.js 剪贴板管理模块，支持 macOS、Windows 和 Linux。

## 特性

- 🚀 **跨平台支持**: macOS、Windows、Linux
- 📝 **多种数据类型**: 文本、图片（Base64）、HTML、文件路径
- 👀 **实时监听**: 监听剪贴板变化（支持的平台）
- 🔧 **简单易用**: 静态方法调用，无需实例化
- 📦 **TypeScript 支持**: 完整的类型定义
- ⚡ **高性能**: 原生 C++ 实现
- 🎯 **灵活导入**: 支持整个类或单个方法导入

## 安装

```bash
npm install @smart-toolbox/clipboard
```

## 快速开始

### 方式1：导入整个类（推荐）

```javascript
import ClipboardManager from '@smart-toolbox/clipboard'

// 检查剪贴板是否有内容
const hasData = ClipboardManager.hasData()
console.log('剪贴板是否有内容:', hasData)

// 设置文本
ClipboardManager.setText('Hello, World!')

// 获取文本
const text = ClipboardManager.getText()
console.log('剪贴板文本:', text)

// 获取内容类型
const contentType = ClipboardManager.getContentType()
console.log('内容类型:', contentType) // 'text', 'image', 'html', 'files', 'empty'
```

### 方式2：导入单个方法

```javascript
import { hasData, setText, getText, getContentType } from '@smart-toolbox/clipboard'

// 检查剪贴板是否有内容
const hasClipboardData = hasData()
console.log('剪贴板是否有内容:', hasClipboardData)

// 设置文本
setText('Hello, World!')

// 获取文本
const text = getText()
console.log('剪贴板文本:', text)

// 获取内容类型
const contentType = getContentType()
console.log('内容类型:', contentType)
```

### 方式3：混合使用

```javascript
import ClipboardManager, { setText, getText } from '@smart-toolbox/clipboard'

// 使用单个方法
setText('Hello, World!')
const text = getText()

// 使用类方法
const contentType = ClipboardManager.getContentType()
const hasData = ClipboardManager.hasData()
```

## API 文档

### 可导出的单个方法

所有静态方法都可以单独导入使用：

```javascript
import { 
  hasData,
  getText,
  setText,
  getImage,
  setImage,
  getHtml,
  setHtml,
  getFiles,
  setFiles,
  clear,
  getContentType,
  watchClipboard,
  unwatchClipboard
} from '@smart-toolbox/clipboard'
```

### 基础操作

#### `hasData(): boolean` 或 `ClipboardManager.hasData(): boolean`
检查剪贴板是否有内容。

#### `clear(): boolean` 或 `ClipboardManager.clear(): boolean`
清空剪贴板。

#### `getContentType(): string` 或 `ClipboardManager.getContentType(): string`
获取剪贴板内容类型。返回值：`'text'` | `'image'` | `'html'` | `'files'` | `'empty'`

### 文本操作

#### `getText(): string` 或 `ClipboardManager.getText(): string`
获取剪贴板文本内容。

#### `setText(text: string): boolean` 或 `ClipboardManager.setText(text: string): boolean`
设置剪贴板文本内容。

```javascript
// 使用单个方法
import { setText, getText } from '@smart-toolbox/clipboard'
const success = setText('这是一段文本')
const text = getText()

// 使用类方法
import ClipboardManager from '@smart-toolbox/clipboard'
const success = ClipboardManager.setText('这是一段文本')
const text = ClipboardManager.getText()
```

### 图片操作

#### `getImage(): string | null` 或 `ClipboardManager.getImage(): string | null`
获取剪贴板图片数据（Base64 格式）。

#### `setImage(base64Data: string): boolean` 或 `ClipboardManager.setImage(base64Data: string): boolean`
设置剪贴板图片数据（Base64 格式）。

```javascript
// 使用单个方法
import { setImage, getImage } from '@smart-toolbox/clipboard'
const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA60e6kgAAAABJRU5ErkJggg=='
const success = setImage(base64Image)
const image = getImage()
```

### HTML 操作

#### `getHtml(): string` 或 `ClipboardManager.getHtml(): string`
获取剪贴板 HTML 内容。

#### `setHtml(html: string): boolean` 或 `ClipboardManager.setHtml(html: string): boolean`
设置剪贴板 HTML 内容。

```javascript
// 使用单个方法
import { setHtml, getHtml } from '@smart-toolbox/clipboard'
const html = '<h1>标题</h1><p>段落内容</p>'
setHtml(html)
const retrievedHtml = getHtml()
```

### 文件操作

#### `getFiles(): string[]` 或 `ClipboardManager.getFiles(): string[]`
获取剪贴板文件路径列表。

#### `setFiles(filePaths: string[]): boolean` 或 `ClipboardManager.setFiles(filePaths: string[]): boolean`
设置剪贴板文件路径。

```javascript
// 使用单个方法
import { setFiles, getFiles } from '@smart-toolbox/clipboard'
const files = ['/path/to/file1.txt', '/path/to/file2.jpg']
const success = setFiles(files)
const retrievedFiles = getFiles()
```

### 监听功能

#### `watchClipboard(callback: Function): boolean` 或 `ClipboardManager.watchClipboard(callback: Function): boolean`
开始监听剪贴板变化。

#### `unwatchClipboard(): boolean` 或 `ClipboardManager.unwatchClipboard(): boolean`
停止监听剪贴板变化。

```javascript
// 使用单个方法
import { watchClipboard, unwatchClipboard } from '@smart-toolbox/clipboard'

const success = watchClipboard((data) => {
  console.log('剪贴板变化:', data)
  console.log('内容类型:', data.type)
})

// 停止监听
setTimeout(() => {
  unwatchClipboard()
}, 10000)
```

## 错误处理

```javascript
try {
  // 使用单个方法
  import { setText } from '@smart-toolbox/clipboard'
  setText('测试文本')
  
  // 或使用类方法
  import ClipboardManager from '@smart-toolbox/clipboard'
  ClipboardManager.setText('测试文本')
} catch (error) {
  if (error.message.includes('Native module not available')) {
    console.error('原生模块未加载，请检查安装')
  } else {
    console.error('操作失败:', error.message)
  }
}
```

## 平台支持

| 平台 | 文本 | 图片 | HTML | 文件 | 监听 |
|------|------|------|------|------|------|
| macOS | ✅ | ✅ | ✅ | ✅ | ✅ |
| Windows | ✅ | ✅ | ✅ | ✅ | ✅ |
| Linux | ✅ | ✅ | ✅ | ✅ | ✅* |

*Linux 监听功能需要 X11 和 XFixes 扩展支持

## 构建

```bash
# 安装依赖
npm install

# 构建原生模块
npm run build:script

# 运行测试
npm test

# 运行示例
npm run example
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
