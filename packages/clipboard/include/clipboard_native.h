/**
 * @file clipboard_native.h
 * @brief 剪贴板原生模块公共头文件
 * <AUTHOR> Toolbox
 * @date 2024
 */

#ifndef CLIPBOARD_NATIVE_H
#define CLIPBOARD_NATIVE_H

#include <napi.h>
#include <string>
#include <vector>

/**
 * @namespace ClipboardNative
 * @brief 剪贴板原生操作命名空间
 */
namespace ClipboardNative {

/**
 * @enum ContentType
 * @brief 剪贴板内容类型枚举
 */
enum class ContentType {
    EMPTY = 0,
    TEXT = 1,
    IMAGE = 2,
    HTML = 3,
    FILES = 4
};

/**
 * @struct ClipboardData
 * @brief 剪贴板数据结构
 */
struct ClipboardData {
    ContentType type;
    std::string textData;
    std::string imageData;  // Base64 encoded
    std::string htmlData;
    std::vector<std::string> filePaths;
    
    ClipboardData() : type(ContentType::EMPTY) {}
};

/**
 * @class ClipboardInterface
 * @brief 剪贴板接口抽象类
 */
class ClipboardInterface {
public:
    virtual ~ClipboardInterface() = default;
    
    /**
     * @brief 检查剪贴板是否有数据
     * @return 是否有数据
     */
    virtual bool HasData() = 0;
    
    /**
     * @brief 获取剪贴板文本内容
     * @return 文本内容
     */
    virtual std::string GetText() = 0;
    
    /**
     * @brief 设置剪贴板文本内容
     * @param text 文本内容
     * @return 是否成功
     */
    virtual bool SetText(const std::string& text) = 0;
    
    /**
     * @brief 获取剪贴板图片数据（Base64格式）
     * @return Base64格式的图片数据，如果没有图片则返回空字符串
     */
    virtual std::string GetImage() = 0;
    
    /**
     * @brief 设置剪贴板图片数据
     * @param base64Data Base64格式的图片数据
     * @return 是否成功
     */
    virtual bool SetImage(const std::string& base64Data) = 0;
    
    /**
     * @brief 获取剪贴板HTML内容
     * @return HTML内容
     */
    virtual std::string GetHtml() = 0;
    
    /**
     * @brief 设置剪贴板HTML内容
     * @param html HTML内容
     * @return 是否成功
     */
    virtual bool SetHtml(const std::string& html) = 0;
    
    /**
     * @brief 获取剪贴板文件路径列表
     * @return 文件路径列表
     */
    virtual std::vector<std::string> GetFiles() = 0;
    
    /**
     * @brief 设置剪贴板文件路径
     * @param filePaths 文件路径列表
     * @return 是否成功
     */
    virtual bool SetFiles(const std::vector<std::string>& filePaths) = 0;
    
    /**
     * @brief 清空剪贴板
     * @return 是否成功
     */
    virtual bool Clear() = 0;
    
    /**
     * @brief 获取剪贴板内容类型
     * @return 内容类型
     */
    virtual ContentType GetContentType() = 0;
    
    /**
     * @brief 开始监听剪贴板变化
     * @param callback 回调函数
     * @return 是否成功
     */
    virtual bool StartWatching(Napi::Function callback) = 0;
    
    /**
     * @brief 停止监听剪贴板变化
     * @return 是否成功
     */
    virtual bool StopWatching() = 0;
};

/**
 * @brief 创建平台特定的剪贴板实例
 * @return 剪贴板接口指针
 */
std::unique_ptr<ClipboardInterface> CreateClipboard();

/**
 * @brief 将ContentType转换为字符串
 * @param type 内容类型
 * @return 类型字符串
 */
std::string ContentTypeToString(ContentType type);

/**
 * @brief 将字符串转换为ContentType
 * @param typeStr 类型字符串
 * @return 内容类型
 */
ContentType StringToContentType(const std::string& typeStr);

/**
 * @brief 验证Base64字符串格式
 * @param base64 Base64字符串
 * @return 是否为有效的Base64格式
 */
bool IsValidBase64(const std::string& base64);

/**
 * @brief 验证文件路径是否存在
 * @param filePath 文件路径
 * @return 文件是否存在
 */
bool FileExists(const std::string& filePath);

/**
 * @brief 获取文件扩展名
 * @param filePath 文件路径
 * @return 文件扩展名（包含点号）
 */
std::string GetFileExtension(const std::string& filePath);

/**
 * @brief 检查是否为图片文件
 * @param filePath 文件路径
 * @return 是否为图片文件
 */
bool IsImageFile(const std::string& filePath);

} // namespace ClipboardNative

#endif // CLIPBOARD_NATIVE_H
