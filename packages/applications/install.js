#!/usr/bin/env node
import { spawn } from 'child_process'
import { existsSync, mkdirSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 日志颜色
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
}

function logInfo(msg) {
  console.log(`${colors.cyan}[INFO]${colors.reset} ${msg}`)
}
function logSuccess(msg) {
  console.log(`${colors.green}✓${colors.reset} ${msg}`)
}
function logWarn(msg) {
  console.log(`${colors.yellow}[WARN]${colors.reset} ${msg}`)
}
function logError(msg) {
  console.error(`${colors.red}✗${colors.reset} ${msg}`)
}
function logStep(title) {
  console.log(`\n${colors.blue}${'='.repeat(40)}${colors.reset}`)
  console.log(`${colors.bold}${title}${colors.reset}`)
  console.log(`${colors.blue}${'='.repeat(40)}${colors.reset}`)
}

logStep('编译智能剪切板原生模块')

// 检查是否已经编译过
const buildPath = join(__dirname, 'build', 'Release', 'clipboard.node')
const buildDir = join(__dirname, 'build')

// 确保构建目录存在
if (!existsSync(buildDir)) {
  try {
    mkdirSync(buildDir, { recursive: true })
    logSuccess('创建构建目录')
  } catch (err) {
    logError('无法创建构建目录: ' + err.message)
    process.exit(1)
  }
}

if (existsSync(buildPath)) {
  logSuccess('原生模块已存在，跳过编译。')
  logInfo('如需重新编译，请运行: pnpm run build:clean')
  process.exit(0)
}

// 检查必要的编译工具
function checkBuildTools() {
  const platform = process.platform
  const arch = process.arch

  logInfo(`检测到平台: ${platform} (${arch})`)

  switch (platform) {
    case 'win32':
      logInfo('Windows 平台需要 Visual Studio Build Tools')
      logInfo('如果编译失败，请运行: npm install --global windows-build-tools')
      logInfo('或者安装 Visual Studio 2019/2022 Community Edition')
      break
    case 'darwin':
      logInfo('macOS 平台需要 Xcode Command Line Tools')
      logInfo('如果编译失败，请运行: xcode-select --install')
      break
    case 'linux':
      logInfo('Linux 平台需要 X11 开发库')
      logInfo('Ubuntu/Debian: sudo apt-get install build-essential libx11-dev libxmu-dev')
      logInfo('CentOS/RHEL: sudo yum groupinstall "Development Tools" && sudo yum install libX11-devel libXmu-devel')
      break
    default:
      logWarn(`未知平台: ${platform}，尝试通用编译方式`)
  }
}

checkBuildTools()

// 运行编译命令
logStep('开始编译原生模块')

const env = {
  ...process.env,
}

// 在非 Windows 平台上，我们倾向于使用 python3
if (process.platform !== 'win32') {
  env.NODE_GYP_FORCE_PYTHON = 'python3'
}

const nodeGyp = spawn('node-gyp', ['rebuild'], {
  stdio: 'inherit',
  shell: true,
  env,
})

nodeGyp.on('close', code => {
  if (code === 0) {
    logSuccess('智能剪切板原生模块编译成功！')
    logInfo('您现在可以使用: import ClipboardManager from "smart-clipboard";')
    logInfo('或者: const ClipboardManager = require("smart-clipboard");')
    console.log(`${colors.green}${'='.repeat(40)}${colors.reset}`)
  } else {
    logError('编译失败，退出码: ' + code)
    logError('请检查编译工具是否正确安装。')
    logInfo('尝试运行: pnpm run build:clean')
    process.exit(code)
  }
})

nodeGyp.on('error', err => {
  logError('编译过程中发生错误: ' + err.message)
  logError('请确保已安装 node-gyp 和相应的编译工具。')
  logInfo('安装 node-gyp: npm install -g node-gyp')
  process.exit(1)
})