/**
 * @file index.d.ts
 * @brief 应用程序管理模块的TypeScript类型定义
 * <AUTHOR> Toolbox
 * @date 2024
 */

/**
 * @interface ApplicationInfo
 * @brief 应用程序信息接口
 */
export interface ApplicationInfo {
    /** 应用程序名称 */
    name: string;
    
    /** Bundle标识符 */
    bundleId: string;
    
    /** 应用程序路径 */
    path: string;
    
    /** 应用程序版本 */
    version: string;
    
    /** 图标路径 */
    iconPath: string;
    
    /** 是否正在运行 */
    isRunning: boolean;
    
    /** 进程ID（如果正在运行） */
    processId: number;
    
    /** 显示名称 */
    displayName: string;
    
    /** 应用分类 */
    category: string;
    
    /** 最后修改时间（Unix时间戳） */
    lastModified: number;
    
    /** 文件大小（字节） */
    fileSize: number;
}

/**
 * @class ApplicationsManager
 * @brief 智能应用程序管理器
 */
export declare class ApplicationsManager {
    /**
     * 获取所有已安装的应用程序
     * @returns 应用程序信息数组
     */
    static getAllApplications(): ApplicationInfo[];

    /**
     * 获取正在运行的应用程序
     * @returns 正在运行的应用程序信息数组
     */
    static getRunningApplications(): ApplicationInfo[];

    /**
     * 根据Bundle ID获取应用信息
     * @param bundleId Bundle标识符
     * @returns 应用程序信息对象或null
     */
    static getApplicationByBundleId(bundleId: string): ApplicationInfo | null;

    /**
     * 根据应用名称搜索应用
     * @param name 应用名称（支持模糊匹配）
     * @returns 匹配的应用程序信息数组
     */
    static searchApplicationsByName(name: string): ApplicationInfo[];

    /**
     * 启动指定的应用程序
     * @param bundleId Bundle标识符
     * @returns 是否启动成功
     */
    static launchApplication(bundleId: string): boolean;

    /**
     * 终止指定的应用程序
     * @param bundleId Bundle标识符
     * @returns 是否终止成功
     */
    static terminateApplication(bundleId: string): boolean;

    /**
     * 检查应用是否正在运行
     * @param bundleId Bundle标识符
     * @returns 是否正在运行
     */
    static isApplicationRunning(bundleId: string): boolean;

    /**
     * 获取应用的详细信息
     * @param path 应用路径
     * @returns 应用程序详细信息对象或null
     */
    static getApplicationDetails(path: string): ApplicationInfo | null;
}

// 导出单个静态方法
export declare const getAllApplications: typeof ApplicationsManager.getAllApplications;
export declare const getRunningApplications: typeof ApplicationsManager.getRunningApplications;
export declare const getApplicationByBundleId: typeof ApplicationsManager.getApplicationByBundleId;
export declare const searchApplicationsByName: typeof ApplicationsManager.searchApplicationsByName;
export declare const launchApplication: typeof ApplicationsManager.launchApplication;
export declare const terminateApplication: typeof ApplicationsManager.terminateApplication;
export declare const isApplicationRunning: typeof ApplicationsManager.isApplicationRunning;
export declare const getApplicationDetails: typeof ApplicationsManager.getApplicationDetails;

export default ApplicationsManager;