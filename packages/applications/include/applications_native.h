/**
 * @file applications_native.h
 * @brief 系统应用获取模块的原生接口定义
 * <AUTHOR> Toolbox
 * @date 2024
 */

#ifndef APPLICATIONS_NATIVE_H
#define APPLICATIONS_NATIVE_H

#include <string>
#include <vector>
#include <memory>

/**
 * @struct ApplicationInfo
 * @brief 应用程序信息结构体
 */
struct ApplicationInfo {
    std::string name;           // 应用名称
    std::string bundleId;       // Bundle标识符
    std::string path;           // 应用路径
    std::string version;        // 版本号
    std::string iconPath;       // 图标路径
    bool isRunning;             // 是否正在运行
    int processId;              // 进程ID（如果正在运行）
    std::string displayName;    // 显示名称
    std::string category;       // 应用分类
    long long lastModified;     // 最后修改时间
    long long fileSize;         // 文件大小
};

/**
 * @class ApplicationsNative
 * @brief 原生应用获取类
 */
class ApplicationsNative {
public:
    /**
     * @brief 构造函数
     */
    ApplicationsNative();
    
    /**
     * @brief 析构函数
     */
    ~ApplicationsNative();
    
    /**
     * @brief 获取所有已安装的应用程序
     * @return 应用程序信息列表
     */
    virtual std::vector<ApplicationInfo> getAllApplications() = 0;

    /**
     * @brief 获取正在运行的应用程序
     * @return 正在运行的应用程序信息列表
     */
    virtual std::vector<ApplicationInfo> getRunningApplications() = 0;

    /**
     * @brief 根据Bundle ID获取应用信息
     * @param bundleId Bundle标识符
     * @return 应用程序信息，如果未找到则返回空的ApplicationInfo
     */
    virtual ApplicationInfo getApplicationByBundleId(const std::string& bundleId) = 0;

    /**
     * @brief 根据应用名称搜索应用
     * @param name 应用名称（支持模糊匹配）
     * @return 匹配的应用程序信息列表
     */
    virtual std::vector<ApplicationInfo> searchApplicationsByName(const std::string& name) = 0;

    /**
     * @brief 启动指定的应用程序
     * @param bundleId Bundle标识符
     * @return 是否启动成功
     */
    virtual bool launchApplication(const std::string& bundleId) = 0;

    /**
     * @brief 终止指定的应用程序
     * @param bundleId Bundle标识符
     * @return 是否终止成功
     */
    virtual bool terminateApplication(const std::string& bundleId) = 0;

    /**
     * @brief 检查应用是否正在运行
     * @param bundleId Bundle标识符
     * @return 是否正在运行
     */
    virtual bool isApplicationRunning(const std::string& bundleId) = 0;

    /**
     * @brief 获取应用的详细信息
     * @param path 应用路径
     * @return 应用程序详细信息
     */
    virtual ApplicationInfo getApplicationDetails(const std::string& path) = 0;
    
private:
    /**
     * @brief 从应用包中提取信息
     * @param appPath 应用路径
     * @return 应用程序信息
     */
    ApplicationInfo extractApplicationInfo(const std::string& appPath);
    
    /**
     * @brief 获取应用图标路径
     * @param appPath 应用路径
     * @return 图标路径
     */
    std::string getApplicationIconPath(const std::string& appPath);
    
    /**
     * @brief 检查路径是否为有效的应用包
     * @param path 路径
     * @return 是否为有效应用包
     */
    bool isValidApplicationBundle(const std::string& path);
};

/**
 * @brief 创建平台特定的应用程序管理实例
 * @return 应用程序管理实例的智能指针
 */
std::unique_ptr<ApplicationsNative> CreateApplicationsNative();

#endif // APPLICATIONS_NATIVE_H