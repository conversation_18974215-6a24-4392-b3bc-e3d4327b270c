/**
 * @file applications_native_linux.cc
 * @brief Linux平台应用程序管理的原生实现
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include "../include/applications_native.h"
#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
#include <filesystem>
#include <fstream>
#include <regex>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/wait.h>
#include <signal.h>
#include <dirent.h>
#include <pwd.h>

namespace fs = std::filesystem;

/**
 * @class ApplicationsNativeLinux
 * @brief Linux平台应用程序管理实现类
 */
class ApplicationsNativeLinux : public ApplicationsNative {
public:
    /**
     * @brief 构造函数
     */
    ApplicationsNativeLinux() {}
    
    /**
     * @brief 析构函数
     */
    virtual ~ApplicationsNativeLinux() {}
    
    /**
     * @brief 获取所有已安装的应用程序
     * @return 应用程序信息列表
     */
    std::vector<ApplicationInfo> GetAllApplications() override {
        std::vector<ApplicationInfo> applications;
        
        try {
            // 扫描系统应用程序目录
            ScanApplicationsDirectory("/usr/share/applications", applications);
            
            // 扫描用户应用程序目录
            std::string userAppsDir = GetUserApplicationsDirectory();
            if (!userAppsDir.empty()) {
                ScanApplicationsDirectory(userAppsDir, applications);
            }
            
            // 扫描Snap应用程序
            ScanSnapApplications(applications);
            
            // 扫描Flatpak应用程序
            ScanFlatpakApplications(applications);
            
            // 扫描AppImage应用程序
            ScanAppImageApplications(applications);
            
            // 去重和排序
            RemoveDuplicates(applications);
            
        } catch (const std::exception& e) {
            std::cerr << "获取应用程序列表时发生错误: " << e.what() << std::endl;
        }
        
        return applications;
    }
    
    /**
     * @brief 获取当前运行中的应用程序
     * @return 运行中的应用程序信息列表
     */
    std::vector<ApplicationInfo> GetRunningApplications() override {
        std::vector<ApplicationInfo> runningApps;
        
        try {
            DIR* procDir = opendir("/proc");
            if (procDir == nullptr) {
                return runningApps;
            }
            
            struct dirent* entry;
            while ((entry = readdir(procDir)) != nullptr) {
                // 检查是否为数字目录（进程ID）
                if (IsNumeric(entry->d_name)) {
                    ApplicationInfo appInfo = GetProcessInfo(std::stoi(entry->d_name));
                    if (!appInfo.name.empty() && !appInfo.path.empty()) {
                        runningApps.push_back(appInfo);
                    }
                }
            }
            
            closedir(procDir);
            
        } catch (const std::exception& e) {
            std::cerr << "获取运行中应用程序时发生错误: " << e.what() << std::endl;
        }
        
        return runningApps;
    }
    
    /**
     * @brief 根据应用程序名称获取应用信息
     * @param appName 应用程序名称
     * @return 应用程序信息，如果未找到则返回空信息
     */
    ApplicationInfo GetApplicationByName(const std::string& appName) override {
        auto allApps = GetAllApplications();
        
        for (const auto& app : allApps) {
            if (app.name == appName || app.displayName == appName) {
                return app;
            }
        }
        
        return ApplicationInfo();
    }
    
    /**
     * @brief 根据名称搜索应用程序
     * @param searchTerm 搜索关键词
     * @param caseSensitive 是否区分大小写
     * @param exactMatch 是否精确匹配
     * @return 匹配的应用程序列表
     */
    std::vector<ApplicationInfo> SearchApplicationsByName(
        const std::string& searchTerm, 
        bool caseSensitive, 
        bool exactMatch) override {
        
        std::vector<ApplicationInfo> results;
        auto allApps = GetAllApplications();
        
        std::string searchTermLower = searchTerm;
        if (!caseSensitive) {
            std::transform(searchTermLower.begin(), searchTermLower.end(), 
                         searchTermLower.begin(), ::tolower);
        }
        
        for (const auto& app : allApps) {
            std::string appName = app.name;
            std::string displayName = app.displayName;
            
            if (!caseSensitive) {
                std::transform(appName.begin(), appName.end(), appName.begin(), ::tolower);
                std::transform(displayName.begin(), displayName.end(), displayName.begin(), ::tolower);
            }
            
            bool matches = false;
            if (exactMatch) {
                matches = (appName == searchTermLower || displayName == searchTermLower);
            } else {
                matches = (appName.find(searchTermLower) != std::string::npos ||
                          displayName.find(searchTermLower) != std::string::npos);
            }
            
            if (matches) {
                results.push_back(app);
            }
        }
        
        return results;
    }
    
    /**
     * @brief 启动应用程序
     * @param appPath 应用程序路径或名称
     * @return 是否启动成功
     */
    bool LaunchApplication(const std::string& appPath) override {
        try {
            pid_t pid = fork();
            if (pid == 0) {
                // 子进程
                if (appPath.find(".desktop") != std::string::npos) {
                    // 启动.desktop文件
                    execlp("gtk-launch", "gtk-launch", fs::path(appPath).stem().c_str(), nullptr);
                } else {
                    // 直接启动可执行文件
                    execlp(appPath.c_str(), appPath.c_str(), nullptr);
                }
                exit(1); // 如果exec失败
            } else if (pid > 0) {
                // 父进程
                return true;
            } else {
                // fork失败
                return false;
            }
        } catch (const std::exception& e) {
            std::cerr << "启动应用程序失败: " << e.what() << std::endl;
            return false;
        }
    }
    
    /**
     * @brief 终止应用程序
     * @param appName 应用程序名称
     * @return 是否终止成功
     */
    bool TerminateApplication(const std::string& appName) override {
        try {
            std::string command = "pkill -f " + appName;
            int result = system(command.c_str());
            return result == 0;
        } catch (const std::exception& e) {
            std::cerr << "终止应用程序失败: " << e.what() << std::endl;
            return false;
        }
    }
    
    /**
     * @brief 检查应用程序是否正在运行
     * @param appName 应用程序名称
     * @return 是否正在运行
     */
    bool IsApplicationRunning(const std::string& appName) override {
        std::string command = "pgrep -f " + appName + " > /dev/null 2>&1";
        int result = system(command.c_str());
        return result == 0;
    }
    
    /**
     * @brief 获取应用程序详细信息
     * @param appPath 应用程序路径
     * @return 应用程序详细信息
     */
    ApplicationInfo GetApplicationDetails(const std::string& appPath) override {
        ApplicationInfo appInfo;
        
        try {
            if (!fs::exists(appPath)) {
                return appInfo;
            }
            
            if (appPath.ends_with(".desktop")) {
                appInfo = ParseDesktopFile(appPath);
            } else {
                appInfo.path = appPath;
                appInfo.name = fs::path(appPath).filename().string();
                appInfo.displayName = appInfo.name;
            }
            
            // 获取文件大小和修改时间
            struct stat fileStat;
            if (stat(appPath.c_str(), &fileStat) == 0) {
                appInfo.fileSize = fileStat.st_size;
                appInfo.lastModified = fileStat.st_mtime;
            }
            
            // 设置分类
            appInfo.category = DetermineCategory(appPath);
            
            // 检查是否正在运行
            appInfo.isRunning = IsApplicationRunning(appInfo.name);
            
        } catch (const std::exception& e) {
            std::cerr << "获取应用程序详细信息失败: " << e.what() << std::endl;
        }
        
        return appInfo;
    }
    
private:
    /**
     * @brief 扫描应用程序目录
     * @param directory 目录路径
     * @param applications 应用程序列表引用
     */
    void ScanApplicationsDirectory(const std::string& directory, std::vector<ApplicationInfo>& applications) {
        try {
            if (!fs::exists(directory)) {
                return;
            }
            
            for (const auto& entry : fs::directory_iterator(directory)) {
                if (entry.is_regular_file() && entry.path().extension() == ".desktop") {
                    ApplicationInfo appInfo = ParseDesktopFile(entry.path().string());
                    if (!appInfo.name.empty() && !appInfo.isHidden) {
                        applications.push_back(appInfo);
                    }
                }
            }
        } catch (const std::exception& e) {
            // 忽略访问权限错误
        }
    }
    
    /**
     * @brief 获取用户应用程序目录
     * @return 用户应用程序目录路径
     */
    std::string GetUserApplicationsDirectory() {
        const char* home = getenv("HOME");
        if (home) {
            return std::string(home) + "/.local/share/applications";
        }
        return "";
    }
    
    /**
     * @brief 扫描Snap应用程序
     * @param applications 应用程序列表引用
     */
    void ScanSnapApplications(std::vector<ApplicationInfo>& applications) {
        try {
            std::string snapDir = "/snap";
            if (fs::exists(snapDir)) {
                for (const auto& entry : fs::directory_iterator(snapDir)) {
                    if (entry.is_directory()) {
                        std::string snapName = entry.path().filename().string();
                        if (snapName != "bin" && snapName != "core" && snapName != "core18" && snapName != "core20") {
                            ApplicationInfo appInfo;
                            appInfo.name = snapName;
                            appInfo.displayName = snapName;
                            appInfo.path = entry.path().string();
                            appInfo.category = "Snap";
                            applications.push_back(appInfo);
                        }
                    }
                }
            }
        } catch (const std::exception& e) {
            // 忽略错误
        }
    }
    
    /**
     * @brief 扫描Flatpak应用程序
     * @param applications 应用程序列表引用
     */
    void ScanFlatpakApplications(std::vector<ApplicationInfo>& applications) {
        try {
            // 检查Flatpak是否安装
            if (system("which flatpak > /dev/null 2>&1") == 0) {
                // 使用flatpak命令获取已安装的应用程序
                FILE* pipe = popen("flatpak list --app --columns=name,application", "r");
                if (pipe) {
                    char buffer[1024];
                    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
                        std::string line(buffer);
                        // 解析Flatpak应用程序信息
                        ParseFlatpakLine(line, applications);
                    }
                    pclose(pipe);
                }
            }
        } catch (const std::exception& e) {
            // 忽略错误
        }
    }
    
    /**
     * @brief 扫描AppImage应用程序
     * @param applications 应用程序列表引用
     */
    void ScanAppImageApplications(std::vector<ApplicationInfo>& applications) {
        try {
            // 扫描常见的AppImage目录
            std::vector<std::string> appImageDirs = {
                GetUserHomeDirectory() + "/Applications",
                GetUserHomeDirectory() + "/AppImages",
                "/opt"
            };
            
            for (const auto& dir : appImageDirs) {
                if (fs::exists(dir)) {
                    for (const auto& entry : fs::recursive_directory_iterator(dir)) {
                        if (entry.is_regular_file()) {
                            std::string filename = entry.path().filename().string();
                            if (filename.ends_with(".AppImage")) {
                                ApplicationInfo appInfo;
                                appInfo.name = fs::path(filename).stem().string();
                                appInfo.displayName = appInfo.name;
                                appInfo.path = entry.path().string();
                                appInfo.category = "AppImage";
                                
                                // 获取文件信息
                                struct stat fileStat;
                                if (stat(appInfo.path.c_str(), &fileStat) == 0) {
                                    appInfo.fileSize = fileStat.st_size;
                                    appInfo.lastModified = fileStat.st_mtime;
                                }
                                
                                applications.push_back(appInfo);
                            }
                        }
                    }
                }
            }
        } catch (const std::exception& e) {
            // 忽略错误
        }
    }
    
    /**
     * @brief 解析.desktop文件
     * @param filePath .desktop文件路径
     * @return 应用程序信息
     */
    ApplicationInfo ParseDesktopFile(const std::string& filePath) {
        ApplicationInfo appInfo;
        
        try {
            std::ifstream file(filePath);
            if (!file.is_open()) {
                return appInfo;
            }
            
            std::string line;
            bool inDesktopEntry = false;
            
            while (std::getline(file, line)) {
                // 去除行首尾空白
                line.erase(0, line.find_first_not_of(" \t"));
                line.erase(line.find_last_not_of(" \t") + 1);
                
                if (line == "[Desktop Entry]") {
                    inDesktopEntry = true;
                    continue;
                } else if (line.starts_with("[") && line.ends_with("]")) {
                    inDesktopEntry = false;
                    continue;
                }
                
                if (!inDesktopEntry || line.empty() || line.starts_with("#")) {
                    continue;
                }
                
                size_t equalPos = line.find('=');
                if (equalPos == std::string::npos) {
                    continue;
                }
                
                std::string key = line.substr(0, equalPos);
                std::string value = line.substr(equalPos + 1);
                
                if (key == "Name") {
                    appInfo.displayName = value;
                    if (appInfo.name.empty()) {
                        appInfo.name = value;
                    }
                } else if (key == "Exec") {
                    appInfo.path = ExtractExecutablePath(value);
                } else if (key == "Icon") {
                    appInfo.iconPath = value;
                } else if (key == "Version") {
                    appInfo.version = value;
                } else if (key == "Categories") {
                    appInfo.category = ParseCategories(value);
                } else if (key == "NoDisplay" && value == "true") {
                    appInfo.isHidden = true;
                } else if (key == "Hidden" && value == "true") {
                    appInfo.isHidden = true;
                }
            }
            
            if (appInfo.name.empty()) {
                appInfo.name = fs::path(filePath).stem().string();
            }
            
            // 获取文件信息
            struct stat fileStat;
            if (stat(filePath.c_str(), &fileStat) == 0) {
                appInfo.fileSize = fileStat.st_size;
                appInfo.lastModified = fileStat.st_mtime;
            }
            
        } catch (const std::exception& e) {
            // 返回空的应用信息
            return ApplicationInfo();
        }
        
        return appInfo;
    }
    
    /**
     * @brief 从Exec字段提取可执行文件路径
     * @param execValue Exec字段的值
     * @return 可执行文件路径
     */
    std::string ExtractExecutablePath(const std::string& execValue) {
        // 移除参数，只保留可执行文件路径
        std::string exec = execValue;
        
        // 移除常见的参数占位符
        std::regex argRegex(R"(\s+%[a-zA-Z])");
        exec = std::regex_replace(exec, argRegex, "");
        
        // 获取第一个单词（可执行文件）
        size_t spacePos = exec.find(' ');
        if (spacePos != std::string::npos) {
            exec = exec.substr(0, spacePos);
        }
        
        return exec;
    }
    
    /**
     * @brief 解析Categories字段
     * @param categoriesValue Categories字段的值
     * @return 主要分类
     */
    std::string ParseCategories(const std::string& categoriesValue) {
        std::vector<std::string> categories;
        std::stringstream ss(categoriesValue);
        std::string category;
        
        while (std::getline(ss, category, ';')) {
            if (!category.empty()) {
                categories.push_back(category);
            }
        }
        
        // 返回第一个分类，如果没有则返回"Other"
        return categories.empty() ? "Other" : categories[0];
    }
    
    /**
     * @brief 解析Flatpak应用程序行
     * @param line Flatpak命令输出的行
     * @param applications 应用程序列表引用
     */
    void ParseFlatpakLine(const std::string& line, std::vector<ApplicationInfo>& applications) {
        // 简单的解析逻辑，实际可能需要更复杂的处理
        if (line.find("\t") != std::string::npos) {
            size_t tabPos = line.find('\t');
            std::string name = line.substr(0, tabPos);
            std::string appId = line.substr(tabPos + 1);
            
            ApplicationInfo appInfo;
            appInfo.name = name;
            appInfo.displayName = name;
            appInfo.bundleId = appId;
            appInfo.category = "Flatpak";
            applications.push_back(appInfo);
        }
    }
    
    /**
     * @brief 获取用户主目录
     * @return 用户主目录路径
     */
    std::string GetUserHomeDirectory() {
        const char* home = getenv("HOME");
        if (home) {
            return std::string(home);
        }
        
        struct passwd* pw = getpwuid(getuid());
        if (pw) {
            return std::string(pw->pw_dir);
        }
        
        return "/tmp";
    }
    
    /**
     * @brief 检查字符串是否为数字
     * @param str 字符串
     * @return 是否为数字
     */
    bool IsNumeric(const std::string& str) {
        return !str.empty() && std::all_of(str.begin(), str.end(), ::isdigit);
    }
    
    /**
     * @brief 从进程ID获取应用程序信息
     * @param pid 进程ID
     * @return 应用程序信息
     */
    ApplicationInfo GetProcessInfo(int pid) {
        ApplicationInfo appInfo;
        
        try {
            // 读取进程命令行
            std::string cmdlinePath = "/proc/" + std::to_string(pid) + "/cmdline";
            std::ifstream cmdlineFile(cmdlinePath);
            if (cmdlineFile.is_open()) {
                std::string cmdline;
                std::getline(cmdlineFile, cmdline);
                
                if (!cmdline.empty()) {
                    // 替换null字符为空格
                    std::replace(cmdline.begin(), cmdline.end(), '\0', ' ');
                    
                    // 获取第一个参数（可执行文件路径）
                    size_t spacePos = cmdline.find(' ');
                    std::string execPath = (spacePos != std::string::npos) ? 
                                         cmdline.substr(0, spacePos) : cmdline;
                    
                    appInfo.path = execPath;
                    appInfo.name = fs::path(execPath).filename().string();
                    appInfo.processId = pid;
                    appInfo.isRunning = true;
                }
            }
            
            // 读取进程状态
            std::string statPath = "/proc/" + std::to_string(pid) + "/stat";
            std::ifstream statFile(statPath);
            if (statFile.is_open()) {
                std::string statLine;
                std::getline(statFile, statLine);
                // 可以从这里解析更多进程信息
            }
            
        } catch (const std::exception& e) {
            // 返回空的应用信息
            return ApplicationInfo();
        }
        
        return appInfo;
    }
    
    /**
     * @brief 确定应用程序分类
     * @param filePath 文件路径
     * @return 应用程序分类
     */
    std::string DetermineCategory(const std::string& filePath) {
        std::string lowerPath = filePath;
        std::transform(lowerPath.begin(), lowerPath.end(), lowerPath.begin(), ::tolower);
        
        if (lowerPath.find("game") != std::string::npos) {
            return "Games";
        } else if (lowerPath.find("office") != std::string::npos ||
                   lowerPath.find("libreoffice") != std::string::npos) {
            return "Office";
        } else if (lowerPath.find("media") != std::string::npos ||
                   lowerPath.find("player") != std::string::npos ||
                   lowerPath.find("vlc") != std::string::npos) {
            return "AudioVideo";
        } else if (lowerPath.find("develop") != std::string::npos ||
                   lowerPath.find("code") != std::string::npos ||
                   lowerPath.find("editor") != std::string::npos) {
            return "Development";
        } else if (lowerPath.find("browser") != std::string::npos ||
                   lowerPath.find("firefox") != std::string::npos ||
                   lowerPath.find("chrome") != std::string::npos) {
            return "Network";
        } else {
            return "Other";
        }
    }
    
    /**
     * @brief 去除重复的应用程序
     * @param applications 应用程序列表引用
     */
    void RemoveDuplicates(std::vector<ApplicationInfo>& applications) {
        std::sort(applications.begin(), applications.end(), 
                 [](const ApplicationInfo& a, const ApplicationInfo& b) {
                     return a.name < b.name;
                 });
        
        applications.erase(
            std::unique(applications.begin(), applications.end(),
                       [](const ApplicationInfo& a, const ApplicationInfo& b) {
                           return a.name == b.name && a.path == b.path;
                       }),
            applications.end());
    }
};

// 全局实例
static ApplicationsNativeLinux g_applicationsNative;

// C接口实现
extern "C" {
    ApplicationsNative* GetApplicationsNativeInstance() {
        return &g_applicationsNative;
    }
}