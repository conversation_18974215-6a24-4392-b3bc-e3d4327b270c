/**
 * @file applications_addon_stub.cc
 * @brief 非macOS平台的存根实现
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include <napi.h>
#include <iostream>

/**
 * @brief 创建错误消息的辅助函数
 * @param env Napi环境
 * @param functionName 函数名称
 * @return 错误对象
 */
Napi::Error CreateUnsupportedError(Napi::Env env, const std::string& functionName) {
    return Napi::Error::New(env, functionName + " is only supported on macOS");
}

/**
 * @brief 获取所有已安装的应用程序（存根）
 * @param info 函数调用信息
 * @return 空数组
 */
Napi::Value GetAllApplications(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CreateUnsupportedError(env, "getAllApplications").ThrowAsJavaScriptException();
    return Napi::Array::New(env, 0);
}

/**
 * @brief 获取正在运行的应用程序（存根）
 * @param info 函数调用信息
 * @return 空数组
 */
Napi::Value GetRunningApplications(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CreateUnsupportedError(env, "getRunningApplications").ThrowAsJavaScriptException();
    return Napi::Array::New(env, 0);
}

/**
 * @brief 根据Bundle ID获取应用信息（存根）
 * @param info 函数调用信息
 * @return null
 */
Napi::Value GetApplicationByBundleId(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CreateUnsupportedError(env, "getApplicationByBundleId").ThrowAsJavaScriptException();
    return env.Null();
}

/**
 * @brief 根据应用名称搜索应用（存根）
 * @param info 函数调用信息
 * @return 空数组
 */
Napi::Value SearchApplicationsByName(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CreateUnsupportedError(env, "searchApplicationsByName").ThrowAsJavaScriptException();
    return Napi::Array::New(env, 0);
}

/**
 * @brief 启动指定的应用程序（存根）
 * @param info 函数调用信息
 * @return false
 */
Napi::Value LaunchApplication(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CreateUnsupportedError(env, "launchApplication").ThrowAsJavaScriptException();
    return Napi::Boolean::New(env, false);
}

/**
 * @brief 终止指定的应用程序（存根）
 * @param info 函数调用信息
 * @return false
 */
Napi::Value TerminateApplication(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CreateUnsupportedError(env, "terminateApplication").ThrowAsJavaScriptException();
    return Napi::Boolean::New(env, false);
}

/**
 * @brief 检查应用是否正在运行（存根）
 * @param info 函数调用信息
 * @return false
 */
Napi::Value IsApplicationRunning(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CreateUnsupportedError(env, "isApplicationRunning").ThrowAsJavaScriptException();
    return Napi::Boolean::New(env, false);
}

/**
 * @brief 获取应用的详细信息（存根）
 * @param info 函数调用信息
 * @return null
 */
Napi::Value GetApplicationDetails(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CreateUnsupportedError(env, "getApplicationDetails").ThrowAsJavaScriptException();
    return env.Null();
}

/**
 * @brief 初始化模块（存根）
 * @param env Napi环境
 * @param exports 导出对象
 * @return 导出对象
 */
Napi::Object Init(Napi::Env env, Napi::Object exports) {
    // 导出存根函数
    exports.Set(Napi::String::New(env, "getAllApplications"), 
               Napi::Function::New(env, GetAllApplications));
    exports.Set(Napi::String::New(env, "getRunningApplications"), 
               Napi::Function::New(env, GetRunningApplications));
    exports.Set(Napi::String::New(env, "getApplicationByBundleId"), 
               Napi::Function::New(env, GetApplicationByBundleId));
    exports.Set(Napi::String::New(env, "searchApplicationsByName"), 
               Napi::Function::New(env, SearchApplicationsByName));
    exports.Set(Napi::String::New(env, "launchApplication"), 
               Napi::Function::New(env, LaunchApplication));
    exports.Set(Napi::String::New(env, "terminateApplication"), 
               Napi::Function::New(env, TerminateApplication));
    exports.Set(Napi::String::New(env, "isApplicationRunning"), 
               Napi::Function::New(env, IsApplicationRunning));
    exports.Set(Napi::String::New(env, "getApplicationDetails"), 
               Napi::Function::New(env, GetApplicationDetails));
    
    std::cout << "Applications stub module initialized (non-macOS platform)" << std::endl;
    
    return exports;
}

// 注册Node.js模块
NODE_API_MODULE(applications, Init)