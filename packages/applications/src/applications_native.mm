/**
 * @file applications_native.mm
 * @brief macOS平台的应用程序获取原生实现
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include "../include/applications_native.h"
#include <Foundation/Foundation.h>
#include <AppKit/AppKit.h>
#include <CoreServices/CoreServices.h>
#include <iostream>
#include <algorithm>
#include <sys/stat.h>

/**
 * @brief ApplicationsNative类的构造函数
 */
ApplicationsNative::ApplicationsNative() {
    // 初始化日志
    NSLog(@"ApplicationsNative initialized");
}

/**
 * @brief ApplicationsNative类的析构函数
 */
ApplicationsNative::~ApplicationsNative() {
    NSLog(@"ApplicationsNative destroyed");
}

/**
 * @brief 获取所有已安装的应用程序
 * @return 应用程序信息列表
 */
std::vector<ApplicationInfo> ApplicationsNative::getAllApplications() {
    std::vector<ApplicationInfo> applications;
    
    @autoreleasepool {
        NSArray *applicationDirectories = @[
            @"/Applications",
            @"/System/Applications",
            @"/System/Library/CoreServices",
            [@"~/Applications" stringByExpandingTildeInPath]
        ];
        
        NSFileManager *fileManager = [NSFileManager defaultManager];
        
        for (NSString *directory in applicationDirectories) {
            NSError *error = nil;
            NSArray *contents = [fileManager contentsOfDirectoryAtPath:directory error:&error];
            
            if (error) {
                NSLog(@"Error reading directory %@: %@", directory, error.localizedDescription);
                continue;
            }
            
            for (NSString *item in contents) {
                if ([item hasSuffix:@".app"]) {
                    NSString *fullPath = [directory stringByAppendingPathComponent:item];
                    std::string appPath = [fullPath UTF8String];
                    
                    if (isValidApplicationBundle(appPath)) {
                        ApplicationInfo appInfo = extractApplicationInfo(appPath);
                        if (!appInfo.name.empty()) {
                            applications.push_back(appInfo);
                        }
                    }
                }
            }
        }
    }
    
    // 按名称排序
    std::sort(applications.begin(), applications.end(), 
              [](const ApplicationInfo& a, const ApplicationInfo& b) {
                  return a.name < b.name;
              });
    
    NSLog(@"Found %lu applications", applications.size());
    return applications;
}

/**
 * @brief 获取正在运行的应用程序
 * @return 正在运行的应用程序信息列表
 */
std::vector<ApplicationInfo> ApplicationsNative::getRunningApplications() {
    std::vector<ApplicationInfo> runningApps;
    
    @autoreleasepool {
        NSArray *runningApplications = [[NSWorkspace sharedWorkspace] runningApplications];
        
        for (NSRunningApplication *app in runningApplications) {
            if (app.bundleURL && app.bundleIdentifier) {
                ApplicationInfo appInfo;
                appInfo.name = app.localizedName ? [app.localizedName UTF8String] : "";
                appInfo.bundleId = [app.bundleIdentifier UTF8String];
                appInfo.path = [app.bundleURL.path UTF8String];
                appInfo.isRunning = true;
                appInfo.processId = app.processIdentifier;
                appInfo.displayName = appInfo.name;
                
                // 获取详细信息
                ApplicationInfo detailedInfo = getApplicationDetails(appInfo.path);
                if (!detailedInfo.version.empty()) {
                    appInfo.version = detailedInfo.version;
                    appInfo.iconPath = detailedInfo.iconPath;
                    appInfo.category = detailedInfo.category;
                }
                
                runningApps.push_back(appInfo);
            }
        }
    }
    
    NSLog(@"Found %lu running applications", runningApps.size());
    return runningApps;
}

/**
 * @brief 根据Bundle ID获取应用信息
 * @param bundleId Bundle标识符
 * @return 应用程序信息
 */
ApplicationInfo ApplicationsNative::getApplicationByBundleId(const std::string& bundleId) {
    ApplicationInfo appInfo;
    
    @autoreleasepool {
        NSString *bundleIdStr = [NSString stringWithUTF8String:bundleId.c_str()];
        NSString *appPath = [[NSWorkspace sharedWorkspace] absolutePathForAppBundleWithIdentifier:bundleIdStr];
        
        if (appPath) {
            appInfo = extractApplicationInfo([appPath UTF8String]);
            
            // 检查是否正在运行
            NSArray *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];
            for (NSRunningApplication *app in runningApps) {
                if ([app.bundleIdentifier isEqualToString:bundleIdStr]) {
                    appInfo.isRunning = true;
                    appInfo.processId = app.processIdentifier;
                    break;
                }
            }
        }
    }
    
    return appInfo;
}

/**
 * @brief 根据应用名称搜索应用
 * @param name 应用名称
 * @return 匹配的应用程序信息列表
 */
std::vector<ApplicationInfo> ApplicationsNative::searchApplicationsByName(const std::string& name) {
    std::vector<ApplicationInfo> allApps = getAllApplications();
    std::vector<ApplicationInfo> matchedApps;
    
    std::string searchName = name;
    std::transform(searchName.begin(), searchName.end(), searchName.begin(), ::tolower);
    
    for (const auto& app : allApps) {
        std::string appName = app.name;
        std::string displayName = app.displayName;
        
        std::transform(appName.begin(), appName.end(), appName.begin(), ::tolower);
        std::transform(displayName.begin(), displayName.end(), displayName.begin(), ::tolower);
        
        if (appName.find(searchName) != std::string::npos || 
            displayName.find(searchName) != std::string::npos) {
            matchedApps.push_back(app);
        }
    }
    
    NSLog(@"Found %lu applications matching '%s'", matchedApps.size(), name.c_str());
    return matchedApps;
}

/**
 * @brief 启动指定的应用程序
 * @param bundleId Bundle标识符
 * @return 是否启动成功
 */
bool ApplicationsNative::launchApplication(const std::string& bundleId) {
    @autoreleasepool {
        NSString *bundleIdStr = [NSString stringWithUTF8String:bundleId.c_str()];
        
        // 首先尝试获取应用程序的URL
        NSString *appPath = [[NSWorkspace sharedWorkspace] absolutePathForAppBundleWithIdentifier:bundleIdStr];
        if (!appPath) {
            NSLog(@"Failed to find application with bundle ID: %@", bundleIdStr);
            return false;
        }
        
        NSURL *appURL = [NSURL fileURLWithPath:appPath];
        if (!appURL) {
            NSLog(@"Failed to create URL for application path: %@", appPath);
            return false;
        }
        
        // 使用新的API启动应用程序
        if (@available(macOS 10.15, *)) {
            NSWorkspaceOpenConfiguration *configuration = [[NSWorkspaceOpenConfiguration alloc] init];
            configuration.activates = YES;
            
            __block BOOL success = NO;
            __block BOOL completed = NO;
            
            [[NSWorkspace sharedWorkspace] openApplicationAtURL:appURL
                                                   configuration:configuration
                                               completionHandler:^(NSRunningApplication * _Nullable app, NSError * _Nullable error) {
                if (error) {
                    NSLog(@"Failed to launch application %@: %@", bundleIdStr, error.localizedDescription);
                    success = NO;
                } else {
                    NSLog(@"Successfully launched application %@", bundleIdStr);
                    success = YES;
                }
                completed = YES;
            }];
            
            // 等待异步操作完成
            while (!completed) {
                [[NSRunLoop currentRunLoop] runMode:NSDefaultRunLoopMode beforeDate:[NSDate dateWithTimeIntervalSinceNow:0.1]];
            }
            
            return success;
        } else {
            // 对于较旧的系统，使用 launchApplication 方法
            NSError *error = nil;
            BOOL success = [[NSWorkspace sharedWorkspace] launchApplication:appPath];
            
            if (!success) {
                NSLog(@"Failed to launch application %@", bundleIdStr);
            } else {
                NSLog(@"Successfully launched application %@", bundleIdStr);
            }
            
            return success;
        }
    }
}

/**
 * @brief 终止指定的应用程序
 * @param bundleId Bundle标识符
 * @return 是否终止成功
 */
bool ApplicationsNative::terminateApplication(const std::string& bundleId) {
    @autoreleasepool {
        NSString *bundleIdStr = [NSString stringWithUTF8String:bundleId.c_str()];
        NSArray *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];
        
        for (NSRunningApplication *app in runningApps) {
            if ([app.bundleIdentifier isEqualToString:bundleIdStr]) {
                BOOL success = [app terminate];
                if (success) {
                    NSLog(@"Successfully terminated application %@", bundleIdStr);
                } else {
                    NSLog(@"Failed to terminate application %@", bundleIdStr);
                }
                return success;
            }
        }
        
        NSLog(@"Application %@ is not running", bundleIdStr);
        return false;
    }
}

/**
 * @brief 检查应用是否正在运行
 * @param bundleId Bundle标识符
 * @return 是否正在运行
 */
bool ApplicationsNative::isApplicationRunning(const std::string& bundleId) {
    @autoreleasepool {
        NSString *bundleIdStr = [NSString stringWithUTF8String:bundleId.c_str()];
        NSArray *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];
        
        for (NSRunningApplication *app in runningApps) {
            if ([app.bundleIdentifier isEqualToString:bundleIdStr]) {
                return true;
            }
        }
        
        return false;
    }
}

/**
 * @brief 获取应用的详细信息
 * @param path 应用路径
 * @return 应用程序详细信息
 */
ApplicationInfo ApplicationsNative::getApplicationDetails(const std::string& path) {
    return extractApplicationInfo(path);
}

/**
 * @brief 从应用包中提取信息
 * @param appPath 应用路径
 * @return 应用程序信息
 */
ApplicationInfo ApplicationsNative::extractApplicationInfo(const std::string& appPath) {
    ApplicationInfo appInfo;
    
    @autoreleasepool {
        NSString *path = [NSString stringWithUTF8String:appPath.c_str()];
        NSBundle *bundle = [NSBundle bundleWithPath:path];
        
        if (!bundle) {
            NSLog(@"Failed to create bundle for path: %@", path);
            return appInfo;
        }
        
        NSDictionary *infoPlist = bundle.infoDictionary;
        if (!infoPlist) {
            NSLog(@"Failed to read Info.plist for: %@", path);
            return appInfo;
        }
        
        // 基本信息
        appInfo.path = appPath;
        appInfo.bundleId = bundle.bundleIdentifier ? [bundle.bundleIdentifier UTF8String] : "";
        
        // 应用名称
        NSString *displayName = infoPlist[@"CFBundleDisplayName"];
        NSString *bundleName = infoPlist[@"CFBundleName"];
        NSString *executableName = infoPlist[@"CFBundleExecutable"];
        
        if (displayName) {
            appInfo.name = [displayName UTF8String];
            appInfo.displayName = [displayName UTF8String];
        } else if (bundleName) {
            appInfo.name = [bundleName UTF8String];
            appInfo.displayName = [bundleName UTF8String];
        } else if (executableName) {
            appInfo.name = [executableName UTF8String];
            appInfo.displayName = [executableName UTF8String];
        } else {
            // 从路径中提取名称
            NSString *fileName = [path.lastPathComponent stringByDeletingPathExtension];
            appInfo.name = [fileName UTF8String];
            appInfo.displayName = [fileName UTF8String];
        }
        
        // 版本信息
        NSString *version = infoPlist[@"CFBundleShortVersionString"];
        if (!version) {
            version = infoPlist[@"CFBundleVersion"];
        }
        appInfo.version = version ? [version UTF8String] : "";
        
        // 分类信息
        NSString *category = infoPlist[@"LSApplicationCategoryType"];
        appInfo.category = category ? [category UTF8String] : "";
        
        // 图标路径
        appInfo.iconPath = getApplicationIconPath(appPath);
        
        // 文件信息
        NSError *error = nil;
        NSDictionary *attributes = [[NSFileManager defaultManager] attributesOfItemAtPath:path error:&error];
        if (!error && attributes) {
            NSDate *modificationDate = attributes[NSFileModificationDate];
            NSNumber *fileSize = attributes[NSFileSize];
            
            if (modificationDate) {
                appInfo.lastModified = (long long)[modificationDate timeIntervalSince1970];
            }
            if (fileSize) {
                appInfo.fileSize = [fileSize longLongValue];
            }
        }
        
        // 检查是否正在运行
        if (!appInfo.bundleId.empty()) {
            appInfo.isRunning = isApplicationRunning(appInfo.bundleId);
            if (appInfo.isRunning) {
                NSArray *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];
                NSString *bundleIdStr = [NSString stringWithUTF8String:appInfo.bundleId.c_str()];
                for (NSRunningApplication *app in runningApps) {
                    if ([app.bundleIdentifier isEqualToString:bundleIdStr]) {
                        appInfo.processId = app.processIdentifier;
                        break;
                    }
                }
            }
        }
    }
    
    return appInfo;
}

/**
 * @brief 获取应用图标路径
 * @param appPath 应用路径
 * @return 图标路径
 */
std::string ApplicationsNative::getApplicationIconPath(const std::string& appPath) {
    @autoreleasepool {
        NSString *path = [NSString stringWithUTF8String:appPath.c_str()];
        NSBundle *bundle = [NSBundle bundleWithPath:path];
        
        if (!bundle) {
            return "";
        }
        
        NSDictionary *infoPlist = bundle.infoDictionary;
        NSString *iconFileName = infoPlist[@"CFBundleIconFile"];
        
        if (iconFileName) {
            // 如果没有扩展名，添加.icns
            if (![iconFileName.pathExtension isEqualToString:@"icns"]) {
                iconFileName = [iconFileName stringByAppendingPathExtension:@"icns"];
            }
            
            NSString *iconPath = [bundle pathForResource:iconFileName.stringByDeletingPathExtension 
                                                   ofType:@"icns"];
            if (iconPath) {
                return [iconPath UTF8String];
            }
        }
        
        // 尝试查找Resources目录中的图标文件
        NSString *resourcesPath = [bundle resourcePath];
        if (resourcesPath) {
            NSArray *iconNames = @[@"icon.icns", @"app.icns", @"AppIcon.icns"];
            for (NSString *iconName in iconNames) {
                NSString *iconPath = [resourcesPath stringByAppendingPathComponent:iconName];
                if ([[NSFileManager defaultManager] fileExistsAtPath:iconPath]) {
                    return [iconPath UTF8String];
                }
            }
        }
        
        return "";
    }
}

/**
 * @brief 检查路径是否为有效的应用包
 * @param path 路径
 * @return 是否为有效应用包
 */
bool ApplicationsNative::isValidApplicationBundle(const std::string& path) {
    @autoreleasepool {
        NSString *nsPath = [NSString stringWithUTF8String:path.c_str()];
        
        // 检查是否以.app结尾
        if (![nsPath hasSuffix:@".app"]) {
            return false;
        }
        
        // 检查是否为目录
        BOOL isDirectory;
        BOOL exists = [[NSFileManager defaultManager] fileExistsAtPath:nsPath isDirectory:&isDirectory];
        if (!exists || !isDirectory) {
            return false;
        }
        
        // 检查是否有Info.plist文件
        NSString *infoPlistPath = [nsPath stringByAppendingPathComponent:@"Contents/Info.plist"];
        if (![[NSFileManager defaultManager] fileExistsAtPath:infoPlistPath]) {
            return false;
        }
        
        return true;
    }
}