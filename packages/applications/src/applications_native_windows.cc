/**
 * @file applications_native_windows.cc
 * @brief Windows平台应用程序管理的原生实现
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include "../include/applications_native.h"
#include <windows.h>
#include <shlobj.h>
#include <shellapi.h>
#include <psapi.h>
#include <tlhelp32.h>
#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
#include <filesystem>
#include <fstream>
#include <regex>
#include <codecvt>
#include <locale>

namespace fs = std::filesystem;

// 添加字符串转换辅助函数
std::string WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::wstring StringToWString(const std::string& str) {
    if (str.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

/**
 * @class ApplicationsNativeWindows
 * @brief Windows平台应用程序管理实现类
 */
class ApplicationsNativeWindows : public ApplicationsNative {
public:
    /**
     * @brief 构造函数
     */
    ApplicationsNativeWindows() {}
    
    /**
     * @brief 析构函数
     */
    virtual ~ApplicationsNativeWindows() {}
    
    /**
     * @brief 获取所有已安装的应用程序
     * @return 应用程序信息列表
     */
    std::vector<ApplicationInfo> getAllApplications() override {
        std::vector<ApplicationInfo> applications;
        
        try {
            // 扫描开始菜单
            ScanStartMenu(applications);
            
            // 扫描Program Files目录
            ScanProgramFiles(applications);
            
            // 扫描注册表中的卸载信息
            ScanUninstallRegistry(applications);
            
            // 去重和排序
            RemoveDuplicates(applications);
            
        } catch (const std::exception& e) {
            std::cerr << "获取应用程序列表时发生错误: " << e.what() << std::endl;
        }
        
        return applications;
    }
    
    /**
     * @brief 获取当前运行中的应用程序
     * @return 运行中的应用程序信息列表
     */
    std::vector<ApplicationInfo> getRunningApplications() override {
        std::vector<ApplicationInfo> runningApps;
        
        try {
            HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hSnapshot == INVALID_HANDLE_VALUE) {
                return runningApps;
            }
            
            PROCESSENTRY32W pe32;
            pe32.dwSize = sizeof(PROCESSENTRY32W);
            
            if (Process32FirstW(hSnapshot, &pe32)) {
                do {
                    ApplicationInfo appInfo = GetProcessInfo(pe32);
                    if (!appInfo.name.empty() && !appInfo.path.empty()) {
                        runningApps.push_back(appInfo);
                    }
                } while (Process32NextW(hSnapshot, &pe32));
            }
            
            CloseHandle(hSnapshot);
            
        } catch (const std::exception& e) {
            std::cerr << "获取运行中应用程序时发生错误: " << e.what() << std::endl;
        }
        
        return runningApps;
    }
    
    /**
     * @brief 根据应用程序名称获取应用信息
     * @param appName 应用程序名称
     * @return 应用程序信息，如果未找到则返回空信息
     */
    ApplicationInfo getApplicationByBundleId(const std::string& bundleId) override {
        auto allApps = getAllApplications();
        
        for (const auto& app : allApps) {
            if (app.name == bundleId || app.displayName == bundleId) {
                return app;
            }
        }
        
        return ApplicationInfo();
    }
    
    /**
     * @brief 根据名称搜索应用程序
     * @param searchTerm 搜索关键词
     * @param caseSensitive 是否区分大小写
     * @param exactMatch 是否精确匹配
     * @return 匹配的应用程序列表
     */
    std::vector<ApplicationInfo> searchApplicationsByName(const std::string& name) override {
        
        std::vector<ApplicationInfo> results;
        auto allApps = getAllApplications();

        std::string searchTermLower = name;
        std::transform(searchTermLower.begin(), searchTermLower.end(),
                     searchTermLower.begin(), ::tolower);

        for (const auto& app : allApps) {
            std::string appName = app.name;
            std::string displayName = app.displayName;

            std::transform(appName.begin(), appName.end(), appName.begin(), ::tolower);
            std::transform(displayName.begin(), displayName.end(), displayName.begin(), ::tolower);
            
            if (appName.find(searchTermLower) != std::string::npos ||
                displayName.find(searchTermLower) != std::string::npos) {
                results.push_back(app);
            }
        }
        
        return results;
    }
    
    /**
     * @brief 启动应用程序
     * @param appPath 应用程序路径或名称
     * @return 是否启动成功
     */
    bool launchApplication(const std::string& bundleId) override {
        try {
            HINSTANCE result = ShellExecuteA(NULL, "open", bundleId.c_str(), NULL, NULL, SW_SHOWNORMAL);
            return (int)result > 32;
        } catch (const std::exception& e) {
            std::cerr << "启动应用程序失败: " << e.what() << std::endl;
            return false;
        }
    }
    
    /**
     * @brief 终止应用程序
     * @param appName 应用程序名称
     * @return 是否终止成功
     */
    bool terminateApplication(const std::string& bundleId) override {
        try {
            HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hSnapshot == INVALID_HANDLE_VALUE) {
                return false;
            }
            
            PROCESSENTRY32W pe32;
            pe32.dwSize = sizeof(PROCESSENTRY32W);
            bool found = false;
            
            if (Process32FirstW(hSnapshot, &pe32)) {
                do {
                    std::string processName = WStringToString(pe32.szExeFile);
                    if (processName == bundleId || processName.find(bundleId) != std::string::npos) {
                        HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, pe32.th32ProcessID);
                        if (hProcess != NULL) {
                            if (TerminateProcess(hProcess, 0)) {
                                found = true;
                            }
                            CloseHandle(hProcess);
                        }
                    }
                } while (Process32NextW(hSnapshot, &pe32));
            }
            
            CloseHandle(hSnapshot);
            return found;
            
        } catch (const std::exception& e) {
            std::cerr << "终止应用程序失败: " << e.what() << std::endl;
            return false;
        }
    }
    
    /**
     * @brief 检查应用程序是否正在运行
     * @param appName 应用程序名称
     * @return 是否正在运行
     */
    bool isApplicationRunning(const std::string& bundleId) override {
        auto runningApps = getRunningApplications();
        
        for (const auto& app : runningApps) {
            if (app.name.find(bundleId) != std::string::npos ||
                app.displayName.find(bundleId) != std::string::npos) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * @brief 获取应用程序详细信息
     * @param appPath 应用程序路径
     * @return 应用程序详细信息
     */
    ApplicationInfo getApplicationDetails(const std::string& path) override {
        ApplicationInfo appInfo;
        
        try {
            if (!fs::exists(path)) {
                return appInfo;
            }
            
            appInfo.path = path;
            appInfo.name = fs::path(path).filename().string();

            // 获取文件版本信息
            GetFileVersionInfo(path, appInfo);

            // 获取文件大小和修改时间
            if (fs::exists(path)) {
                auto fileSize = fs::file_size(path);
                auto fileTime = fs::last_write_time(path);

                appInfo.fileSize = static_cast<long long>(fileSize);

                // 转换文件时间
                auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    fileTime - fs::file_time_type::clock::now() + std::chrono::system_clock::now());
                auto time_t_val = std::chrono::system_clock::to_time_t(sctp);
                appInfo.lastModified = static_cast<long long>(time_t_val);
            }

            // 设置分类
            appInfo.category = DetermineCategory(path);
            
        } catch (const std::exception& e) {
            std::cerr << "获取应用程序详细信息失败: " << e.what() << std::endl;
        }
        
        return appInfo;
    }
    
private:
    /**
     * @brief 扫描开始菜单中的应用程序
     * @param applications 应用程序列表引用
     */
    void ScanStartMenu(std::vector<ApplicationInfo>& applications) {
        // 扫描用户开始菜单
        ScanDirectory(GetStartMenuPath(false), applications);
        
        // 扫描系统开始菜单
        ScanDirectory(GetStartMenuPath(true), applications);
    }
    
    /**
     * @brief 扫描Program Files目录
     * @param applications 应用程序列表引用
     */
    void ScanProgramFiles(std::vector<ApplicationInfo>& applications) {
        // 扫描Program Files
        ScanDirectory("C:\\Program Files", applications);
        
        // 扫描Program Files (x86)
        ScanDirectory("C:\\Program Files (x86)", applications);
    }
    
    /**
     * @brief 扫描注册表中的卸载信息
     * @param applications 应用程序列表引用
     */
    void ScanUninstallRegistry(std::vector<ApplicationInfo>& applications) {
        // 实现注册表扫描逻辑
        // 这里可以扫描HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall
    }
    
    /**
     * @brief 获取开始菜单路径
     * @param allUsers 是否获取所有用户的开始菜单
     * @return 开始菜单路径
     */
    std::string GetStartMenuPath(bool allUsers) {
        PWSTR path = nullptr;
        HRESULT hr = SHGetKnownFolderPath(
            allUsers ? FOLDERID_CommonStartMenu : FOLDERID_StartMenu,
            0, NULL, &path);
        
        std::string result;
        if (SUCCEEDED(hr) && path != nullptr) {
            result = WStringToString(path);
            CoTaskMemFree(path); // 重要：释放内存
        }
        
        return result;
    }
    
    /**
     * @brief 扫描指定目录中的应用程序
     * @param directory 目录路径
     * @param applications 应用程序列表引用
     */
    void ScanDirectory(const std::string& directory, std::vector<ApplicationInfo>& applications) {
        try {
            if (!fs::exists(directory) || !fs::is_directory(directory)) {
                return;
            }
            
            for (const auto& entry : fs::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file()) {
                    std::string extension = entry.path().extension().string();
                    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
                    
                    if (extension == ".exe" || extension == ".lnk") {
                        ApplicationInfo appInfo;
                        appInfo.path = entry.path().string();
                        appInfo.name = entry.path().stem().string();
                        GetFileVersionInfo(appInfo.path, appInfo);
                        applications.push_back(appInfo);
                    }
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "扫描目录失败 " << directory << ": " << e.what() << std::endl;
        }
    }
    
    /**
     * @brief 从文件路径提取应用程序信息
     * @param filePath 文件路径
     * @return 应用程序信息
     */
    ApplicationInfo ExtractApplicationInfo(const std::string& filePath) {
        ApplicationInfo appInfo;
        
        try {
            appInfo.path = filePath;
            appInfo.name = fs::path(filePath).stem().string();
            appInfo.displayName = appInfo.name;
            
            // 获取文件版本信息
            GetFileVersionInfo(filePath, appInfo);
            
            // 获取文件大小和修改时间
            if (fs::exists(filePath)) {
                auto fileSize = fs::file_size(filePath);
                auto fileTime = fs::last_write_time(filePath);
                
                appInfo.fileSize = static_cast<long long>(fileSize);
                
                auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    fileTime - fs::file_time_type::clock::now() + std::chrono::system_clock::now());
                auto time_t = std::chrono::system_clock::to_time_t(sctp);
                appInfo.lastModified = static_cast<long long>(time_t);
            }
            
            // 设置分类
            appInfo.category = DetermineCategory(filePath);
            
            // 检查是否正在运行
            appInfo.isRunning = isApplicationRunning(appInfo.name);
            
        } catch (const std::exception& e) {
            // 返回空的应用信息
            return ApplicationInfo();
        }
        
        return appInfo;
    }
    
    /**
     * @brief 获取文件版本信息
     * @param filePath 文件路径
     * @param appInfo 应用程序信息引用
     */
    void GetFileVersionInfo(const std::string& filePath, ApplicationInfo& appInfo) {
        DWORD dwSize = ::GetFileVersionInfoSizeA(filePath.c_str(), NULL);
        if (dwSize == 0) {
            return;
        }

        std::vector<BYTE> buffer(dwSize);
        if (!::GetFileVersionInfoA(filePath.c_str(), 0, dwSize, buffer.data())) {
            return;
        }
        
        VS_FIXEDFILEINFO* pFileInfo = nullptr;
        UINT uLen = 0;
        if (VerQueryValueA(buffer.data(), "\\", (LPVOID*)&pFileInfo, &uLen)) {
            if (pFileInfo != nullptr) {
                appInfo.version = std::to_string(HIWORD(pFileInfo->dwFileVersionMS)) + "." +
                                std::to_string(LOWORD(pFileInfo->dwFileVersionMS)) + "." +
                                std::to_string(HIWORD(pFileInfo->dwFileVersionLS)) + "." +
                                std::to_string(LOWORD(pFileInfo->dwFileVersionLS));
            }
        }
        
        // 获取产品名称
        LPVOID lpBuffer = nullptr;
        if (VerQueryValueA(buffer.data(), "\\StringFileInfo\\040904b0\\ProductName", &lpBuffer, &uLen)) {
            if (lpBuffer != nullptr) {
                appInfo.displayName = std::string((char*)lpBuffer);
            }
        }
    }
    
    /**
     * @brief 从进程信息获取应用程序信息
     * @param pe32 进程信息
     * @return 应用程序信息
     */
    ApplicationInfo GetProcessInfo(const PROCESSENTRY32W& pe32) {
        ApplicationInfo appInfo;
        
        try {
            // 获取进程句柄
            HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pe32.th32ProcessID);
            if (hProcess == NULL) {
                return appInfo; // 返回空的应用信息
            }
            
            // 获取进程路径
            WCHAR processPath[MAX_PATH] = {0};
            DWORD pathSize = MAX_PATH;
            if (QueryFullProcessImageNameW(hProcess, 0, processPath, &pathSize)) {
                appInfo.path = WStringToString(processPath);
                
                // 从路径中提取文件名作为应用名称
                fs::path p(processPath);
                appInfo.name = WStringToString(p.filename().wstring());
                
                // 获取版本信息
                GetFileVersionInfo(appInfo.path, appInfo);
            }
            
            CloseHandle(hProcess);
            
        } catch (const std::exception& e) {
            std::cerr << "获取进程信息失败: " << e.what() << std::endl;
        }
        
        return appInfo;
    }
    
    /**
     * @brief 确定应用程序分类
     * @param filePath 文件路径
     * @return 应用程序分类
     */
    std::string DetermineCategory(const std::string& filePath) {
        std::string lowerPath = filePath;
        std::transform(lowerPath.begin(), lowerPath.end(), lowerPath.begin(), ::tolower);
        
        if (lowerPath.find("game") != std::string::npos) {
            return "Games";
        } else if (lowerPath.find("office") != std::string::npos ||
                   lowerPath.find("word") != std::string::npos ||
                   lowerPath.find("excel") != std::string::npos) {
            return "Productivity";
        } else if (lowerPath.find("media") != std::string::npos ||
                   lowerPath.find("player") != std::string::npos) {
            return "Entertainment";
        } else if (lowerPath.find("develop") != std::string::npos ||
                   lowerPath.find("code") != std::string::npos) {
            return "Developer Tools";
        } else {
            return "Other";
        }
    }
    
    /**
     * @brief 去除重复的应用程序
     * @param applications 应用程序列表引用
     */
    void RemoveDuplicates(std::vector<ApplicationInfo>& applications) {
        std::sort(applications.begin(), applications.end(), 
                 [](const ApplicationInfo& a, const ApplicationInfo& b) {
                     return a.name < b.name;
                 });
        
        applications.erase(
            std::unique(applications.begin(), applications.end(),
                       [](const ApplicationInfo& a, const ApplicationInfo& b) {
                           return a.name == b.name && a.path == b.path;
                       }),
            applications.end());
    }
};

// 全局实例
static ApplicationsNativeWindows g_applicationsNative;

// C接口实现
extern "C" {
    ApplicationsNative* GetApplicationsNativeInstance() {
        return &g_applicationsNative;
    }
}

// 基类实现
ApplicationsNative::ApplicationsNative() {}
ApplicationsNative::~ApplicationsNative() {}

// 创建平台特定的实例
std::unique_ptr<ApplicationsNative> CreateApplicationsNative() {
    return std::make_unique<ApplicationsNativeWindows>();
}
