/**
 * @file applications_addon.mm
 * @brief Node.js插件主接口文件，连接原生代码和JavaScript
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include <napi.h>
#include "../include/applications_native.h"
#include <iostream>
#include <memory>

// 全局应用管理器实例
static std::unique_ptr<ApplicationsNative> g_applicationsNative;

/**
 * @brief 将ApplicationInfo结构体转换为JavaScript对象
 * @param env Napi环境
 * @param appInfo 应用信息结构体
 * @return JavaScript对象
 */
Napi::Object ApplicationInfoToJS(Napi::Env env, const ApplicationInfo& appInfo) {
    Napi::Object obj = Napi::Object::New(env);
    
    obj.Set("name", Napi::String::New(env, appInfo.name));
    obj.Set("bundleId", Napi::String::New(env, appInfo.bundleId));
    obj.Set("path", Napi::String::New(env, appInfo.path));
    obj.Set("version", Napi::String::New(env, appInfo.version));
    obj.Set("iconPath", Napi::String::New(env, appInfo.iconPath));
    obj.Set("isRunning", Napi::Boolean::New(env, appInfo.isRunning));
    obj.Set("processId", Napi::Number::New(env, appInfo.processId));
    obj.Set("displayName", Napi::String::New(env, appInfo.displayName));
    obj.Set("category", Napi::String::New(env, appInfo.category));
    obj.Set("lastModified", Napi::Number::New(env, appInfo.lastModified));
    obj.Set("fileSize", Napi::Number::New(env, appInfo.fileSize));
    
    return obj;
}

/**
 * @brief 获取所有已安装的应用程序
 * @param info 函数调用信息
 * @return JavaScript数组，包含所有应用信息
 */
Napi::Value GetAllApplications(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "Applications native module not initialized")
                .ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::vector<ApplicationInfo> applications = g_applicationsNative->getAllApplications();
        Napi::Array result = Napi::Array::New(env, applications.size());
        
        for (size_t i = 0; i < applications.size(); i++) {
            result[i] = ApplicationInfoToJS(env, applications[i]);
        }
        
        return result;
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("Error getting all applications: ") + e.what())
            .ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 获取正在运行的应用程序
 * @param info 函数调用信息
 * @return JavaScript数组，包含正在运行的应用信息
 */
Napi::Value GetRunningApplications(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "Applications native module not initialized")
                .ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::vector<ApplicationInfo> applications = g_applicationsNative->getRunningApplications();
        Napi::Array result = Napi::Array::New(env, applications.size());
        
        for (size_t i = 0; i < applications.size(); i++) {
            result[i] = ApplicationInfoToJS(env, applications[i]);
        }
        
        return result;
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("Error getting running applications: ") + e.what())
            .ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 根据Bundle ID获取应用信息
 * @param info 函数调用信息，第一个参数为bundleId字符串
 * @return JavaScript对象，包含应用信息
 */
Napi::Value GetApplicationByBundleId(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string argument (bundleId)")
            .ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "Applications native module not initialized")
                .ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string bundleId = info[0].As<Napi::String>().Utf8Value();
        ApplicationInfo appInfo = g_applicationsNative->getApplicationByBundleId(bundleId);
        
        if (appInfo.name.empty()) {
            return env.Null();
        }
        
        return ApplicationInfoToJS(env, appInfo);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("Error getting application by bundle ID: ") + e.what())
            .ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 根据应用名称搜索应用
 * @param info 函数调用信息，第一个参数为应用名称字符串
 * @return JavaScript数组，包含匹配的应用信息
 */
Napi::Value SearchApplicationsByName(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string argument (name)")
            .ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "Applications native module not initialized")
                .ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string name = info[0].As<Napi::String>().Utf8Value();
        std::vector<ApplicationInfo> applications = g_applicationsNative->searchApplicationsByName(name);
        Napi::Array result = Napi::Array::New(env, applications.size());
        
        for (size_t i = 0; i < applications.size(); i++) {
            result[i] = ApplicationInfoToJS(env, applications[i]);
        }
        
        return result;
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("Error searching applications by name: ") + e.what())
            .ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 启动指定的应用程序
 * @param info 函数调用信息，第一个参数为bundleId字符串
 * @return JavaScript布尔值，表示是否启动成功
 */
Napi::Value LaunchApplication(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string argument (bundleId)")
            .ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "Applications native module not initialized")
                .ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string bundleId = info[0].As<Napi::String>().Utf8Value();
        bool success = g_applicationsNative->launchApplication(bundleId);
        
        return Napi::Boolean::New(env, success);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("Error launching application: ") + e.what())
            .ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 终止指定的应用程序
 * @param info 函数调用信息，第一个参数为bundleId字符串
 * @return JavaScript布尔值，表示是否终止成功
 */
Napi::Value TerminateApplication(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string argument (bundleId)")
            .ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "Applications native module not initialized")
                .ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string bundleId = info[0].As<Napi::String>().Utf8Value();
        bool success = g_applicationsNative->terminateApplication(bundleId);
        
        return Napi::Boolean::New(env, success);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("Error terminating application: ") + e.what())
            .ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 检查应用是否正在运行
 * @param info 函数调用信息，第一个参数为bundleId字符串
 * @return JavaScript布尔值，表示是否正在运行
 */
Napi::Value IsApplicationRunning(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string argument (bundleId)")
            .ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "Applications native module not initialized")
                .ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string bundleId = info[0].As<Napi::String>().Utf8Value();
        bool isRunning = g_applicationsNative->isApplicationRunning(bundleId);
        
        return Napi::Boolean::New(env, isRunning);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("Error checking if application is running: ") + e.what())
            .ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 获取应用的详细信息
 * @param info 函数调用信息，第一个参数为应用路径字符串
 * @return JavaScript对象，包含应用详细信息
 */
Napi::Value GetApplicationDetails(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string argument (path)")
            .ThrowAsJavaScriptException();
        return env.Null();
    }
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "Applications native module not initialized")
                .ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string path = info[0].As<Napi::String>().Utf8Value();
        ApplicationInfo appInfo = g_applicationsNative->getApplicationDetails(path);
        
        if (appInfo.name.empty()) {
            return env.Null();
        }
        
        return ApplicationInfoToJS(env, appInfo);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("Error getting application details: ") + e.what())
            .ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 初始化模块
 * @param env Napi环境
 * @param exports 导出对象
 * @return 导出对象
 */
Napi::Object Init(Napi::Env env, Napi::Object exports) {
    try {
        // 初始化原生应用管理器
        g_applicationsNative = std::make_unique<ApplicationsNative>();
        
        // 导出函数
        exports.Set(Napi::String::New(env, "getAllApplications"), 
                   Napi::Function::New(env, GetAllApplications));
        exports.Set(Napi::String::New(env, "getRunningApplications"), 
                   Napi::Function::New(env, GetRunningApplications));
        exports.Set(Napi::String::New(env, "getApplicationByBundleId"), 
                   Napi::Function::New(env, GetApplicationByBundleId));
        exports.Set(Napi::String::New(env, "searchApplicationsByName"), 
                   Napi::Function::New(env, SearchApplicationsByName));
        exports.Set(Napi::String::New(env, "launchApplication"), 
                   Napi::Function::New(env, LaunchApplication));
        exports.Set(Napi::String::New(env, "terminateApplication"), 
                   Napi::Function::New(env, TerminateApplication));
        exports.Set(Napi::String::New(env, "isApplicationRunning"), 
                   Napi::Function::New(env, IsApplicationRunning));
        exports.Set(Napi::String::New(env, "getApplicationDetails"), 
                   Napi::Function::New(env, GetApplicationDetails));
        
        std::cout << "Applications native module initialized successfully" << std::endl;
        
        return exports;
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("Failed to initialize applications module: ") + e.what())
            .ThrowAsJavaScriptException();
        return exports;
    }
}

// 注册Node.js模块
NODE_API_MODULE(applications, Init)