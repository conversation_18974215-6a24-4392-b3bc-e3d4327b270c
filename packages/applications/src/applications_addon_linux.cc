/**
 * @file applications_addon_linux.cc
 * @brief Linux平台Node.js插件主接口文件
 * <AUTHOR> Toolbox
 * @date 2024
 */

#include <napi.h>
#include "../include/applications_native.h"
#include <vector>
#include <string>
#include <iostream>

// 全局原生实例
static ApplicationsNative* g_applicationsNative = nullptr;

/**
 * @brief 将ApplicationInfo结构体转换为JavaScript对象
 * @param env N-API环境
 * @param appInfo 应用程序信息结构体
 * @return JavaScript对象
 */
Napi::Object ApplicationInfoToJS(Napi::Env env, const ApplicationInfo& appInfo) {
    Napi::Object obj = Napi::Object::New(env);
    
    try {
        obj.Set("name", Napi::String::New(env, appInfo.name));
        obj.Set("bundleId", Napi::String::New(env, appInfo.bundleId));
        obj.Set("path", Napi::String::New(env, appInfo.path));
        obj.Set("version", Napi::String::New(env, appInfo.version));
        obj.Set("iconPath", Napi::String::New(env, appInfo.iconPath));
        obj.Set("isRunning", Napi::Boolean::New(env, appInfo.isRunning));
        obj.Set("processId", Napi::Number::New(env, appInfo.processId));
        obj.Set("displayName", Napi::String::New(env, appInfo.displayName));
        obj.Set("category", Napi::String::New(env, appInfo.category));
        obj.Set("lastModified", Napi::Number::New(env, static_cast<double>(appInfo.lastModified)));
        obj.Set("fileSize", Napi::Number::New(env, static_cast<double>(appInfo.fileSize)));
        obj.Set("isHidden", Napi::Boolean::New(env, appInfo.isHidden));
    } catch (const std::exception& e) {
        std::cerr << "转换ApplicationInfo到JS对象时发生错误: " << e.what() << std::endl;
    }
    
    return obj;
}

/**
 * @brief 获取所有应用程序
 * @param info 函数调用信息
 * @return 应用程序列表
 */
Napi::Value GetAllApplications(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "原生模块未初始化").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        auto applications = g_applicationsNative->GetAllApplications();
        Napi::Array result = Napi::Array::New(env, applications.size());
        
        for (size_t i = 0; i < applications.size(); i++) {
            result[i] = ApplicationInfoToJS(env, applications[i]);
        }
        
        return result;
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("获取应用程序列表失败: ") + e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 获取运行中的应用程序
 * @param info 函数调用信息
 * @return 运行中的应用程序列表
 */
Napi::Value GetRunningApplications(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "原生模块未初始化").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        auto applications = g_applicationsNative->GetRunningApplications();
        Napi::Array result = Napi::Array::New(env, applications.size());
        
        for (size_t i = 0; i < applications.size(); i++) {
            result[i] = ApplicationInfoToJS(env, applications[i]);
        }
        
        return result;
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("获取运行中应用程序失败: ") + e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 根据应用程序名称获取应用信息
 * @param info 函数调用信息
 * @return 应用程序信息或null
 */
Napi::Value GetApplicationByName(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "需要应用程序名称参数").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "原生模块未初始化").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string appName = info[0].As<Napi::String>().Utf8Value();
        ApplicationInfo appInfo = g_applicationsNative->GetApplicationByName(appName);
        
        if (appInfo.name.empty()) {
            return env.Null();
        }
        
        return ApplicationInfoToJS(env, appInfo);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("获取应用程序信息失败: ") + e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 根据名称搜索应用程序
 * @param info 函数调用信息
 * @return 匹配的应用程序列表
 */
Napi::Value SearchApplicationsByName(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "需要搜索关键词参数").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "原生模块未初始化").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string searchTerm = info[0].As<Napi::String>().Utf8Value();
        bool caseSensitive = false;
        bool exactMatch = false;
        
        // 解析可选参数
        if (info.Length() > 1 && info[1].IsObject()) {
            Napi::Object options = info[1].As<Napi::Object>();
            
            if (options.Has("caseSensitive") && options.Get("caseSensitive").IsBoolean()) {
                caseSensitive = options.Get("caseSensitive").As<Napi::Boolean>().Value();
            }
            
            if (options.Has("exactMatch") && options.Get("exactMatch").IsBoolean()) {
                exactMatch = options.Get("exactMatch").As<Napi::Boolean>().Value();
            }
        }
        
        auto applications = g_applicationsNative->SearchApplicationsByName(searchTerm, caseSensitive, exactMatch);
        Napi::Array result = Napi::Array::New(env, applications.size());
        
        for (size_t i = 0; i < applications.size(); i++) {
            result[i] = ApplicationInfoToJS(env, applications[i]);
        }
        
        return result;
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("搜索应用程序失败: ") + e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 启动应用程序
 * @param info 函数调用信息
 * @return 是否启动成功
 */
Napi::Value LaunchApplication(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "需要应用程序路径或名称参数").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "原生模块未初始化").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string appPath = info[0].As<Napi::String>().Utf8Value();
        bool success = g_applicationsNative->LaunchApplication(appPath);
        
        return Napi::Boolean::New(env, success);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("启动应用程序失败: ") + e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 终止应用程序
 * @param info 函数调用信息
 * @return 是否终止成功
 */
Napi::Value TerminateApplication(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "需要应用程序名称参数").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "原生模块未初始化").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string appName = info[0].As<Napi::String>().Utf8Value();
        bool success = g_applicationsNative->TerminateApplication(appName);
        
        return Napi::Boolean::New(env, success);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("终止应用程序失败: ") + e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 检查应用程序是否正在运行
 * @param info 函数调用信息
 * @return 是否正在运行
 */
Napi::Value IsApplicationRunning(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "需要应用程序名称参数").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "原生模块未初始化").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string appName = info[0].As<Napi::String>().Utf8Value();
        bool isRunning = g_applicationsNative->IsApplicationRunning(appName);
        
        return Napi::Boolean::New(env, isRunning);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("检查应用程序运行状态失败: ") + e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 获取应用程序详细信息
 * @param info 函数调用信息
 * @return 应用程序详细信息
 */
Napi::Value GetApplicationDetails(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    try {
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "需要应用程序路径参数").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        if (!g_applicationsNative) {
            Napi::TypeError::New(env, "原生模块未初始化").ThrowAsJavaScriptException();
            return env.Null();
        }
        
        std::string appPath = info[0].As<Napi::String>().Utf8Value();
        ApplicationInfo appInfo = g_applicationsNative->GetApplicationDetails(appPath);
        
        if (appInfo.name.empty()) {
            return env.Null();
        }
        
        return ApplicationInfoToJS(env, appInfo);
    } catch (const std::exception& e) {
        Napi::Error::New(env, std::string("获取应用程序详细信息失败: ") + e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

/**
 * @brief 模块初始化函数
 * @param env N-API环境
 * @param exports 导出对象
 * @return 导出对象
 */
Napi::Object Init(Napi::Env env, Napi::Object exports) {
    try {
        // 获取原生实例
        extern ApplicationsNative* GetApplicationsNativeInstance();
        g_applicationsNative = GetApplicationsNativeInstance();
        
        if (!g_applicationsNative) {
            Napi::Error::New(env, "无法初始化原生模块").ThrowAsJavaScriptException();
            return exports;
        }
        
        // 导出函数
        exports.Set(Napi::String::New(env, "getAllApplications"), 
                   Napi::Function::New(env, GetAllApplications));
        exports.Set(Napi::String::New(env, "getRunningApplications"), 
                   Napi::Function::New(env, GetRunningApplications));
        exports.Set(Napi::String::New(env, "getApplicationByName"), 
                   Napi::Function::New(env, GetApplicationByName));
        exports.Set(Napi::String::New(env, "searchApplicationsByName"), 
                   Napi::Function::New(env, SearchApplicationsByName));
        exports.Set(Napi::String::New(env, "launchApplication"), 
                   Napi::Function::New(env, LaunchApplication));
        exports.Set(Napi::String::New(env, "terminateApplication"), 
                   Napi::Function::New(env, TerminateApplication));
        exports.Set(Napi::String::New(env, "isApplicationRunning"), 
                   Napi::Function::New(env, IsApplicationRunning));
        exports.Set(Napi::String::New(env, "getApplicationDetails"), 
                   Napi::Function::New(env, GetApplicationDetails));
        
        std::cout << "Linux应用程序管理模块初始化成功" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "模块初始化失败: " << e.what() << std::endl;
        Napi::Error::New(env, std::string("模块初始化失败: ") + e.what()).ThrowAsJavaScriptException();
    }
    
    return exports;
}

// 注册模块
NODE_API_MODULE(applications, Init)