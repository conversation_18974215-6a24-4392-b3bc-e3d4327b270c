{"name": "@smart/applications", "version": "1.0.0", "description": "Native addon for getting system applications on macOS", "main": "js/index.js", "type": "module", "author": "Smart Toolbox", "scripts": {"clean": "node -e \"require('fs').rmSync('build', { recursive: true, force: true })\"", "build": "node-gyp configure && node-gyp build", "build:clean": "npm run clean && npm run build", "rebuild": "npm run clean && npm run build", "install": "node install.js"}, "license": "MIT", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^8.3.0"}, "devDependencies": {"node-gyp": "^11.2.0", "mocha": "^11.7.1", "@types/node": "^24.1.0", "typescript": "^5.8.3", "ts-node": "^10.9.2"}, "gypfile": true, "keywords": ["electron", "native", "applications", "macos", "system"]}