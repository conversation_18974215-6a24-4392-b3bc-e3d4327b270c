/**
 * @file index.js
 * @brief 应用程序管理模块的JavaScript接口
 * <AUTHOR> Toolbox
 * @date 2024
 */

import { createRequire } from 'node:module'

const require = createRequire(import.meta.url)

const applications = require('../build/Release/applications.node')


/**
 * 智能应用程序管理器
 * 支持跨平台的应用程序管理操作
 */
class ApplicationsManager {
  /**
   * 获取所有已安装的应用程序
   * @returns {Array} 应用程序信息数组
   */
  static getAllApplications() {
    return applications.getAllApplications()
  }

  /**
   * 获取正在运行的应用程序
   * @returns {Array} 正在运行的应用程序信息数组
   */
  static getRunningApplications() {
    return applications.getRunningApplications()
  }

  /**
   * 根据Bundle ID获取应用信息
   * @param {string} bundleId Bundle标识符
   * @returns {Object|null} 应用程序信息对象或null
   */
  static getApplicationByBundleId(bundleId) {
    if (!bundleId || typeof bundleId !== 'string') {
      throw new TypeError('Bundle ID必须是非空字符串')
    }
    return applications.getApplicationByBundleId(bundleId)
  }

  /**
   * 根据应用名称搜索应用
   * @param {string} name 应用名称（支持模糊匹配）
   * @returns {Array} 匹配的应用程序信息数组
   */
  static searchApplicationsByName(name) {
    if (!name || typeof name !== 'string') {
      throw new TypeError('应用名称必须是非空字符串')
    }
    return applications.searchApplicationsByName(name)
  }

  /**
   * 启动指定的应用程序
   * @param {string} bundleId Bundle标识符
   * @returns {boolean} 是否启动成功
   */
  static launchApplication(bundleId) {
    if (!bundleId || typeof bundleId !== 'string') {
      throw new TypeError('Bundle ID必须是非空字符串')
    }
    return applications.launchApplication(bundleId)
  }

  /**
   * 终止指定的应用程序
   * @param {string} bundleId Bundle标识符
   * @returns {boolean} 是否终止成功
   */
  static terminateApplication(bundleId) {
    if (!bundleId || typeof bundleId !== 'string') {
      throw new TypeError('Bundle ID必须是非空字符串')
    }
    return applications.terminateApplication(bundleId)
  }

  /**
   * 检查应用是否正在运行
   * @param {string} bundleId Bundle标识符
   * @returns {boolean} 是否正在运行
   */
  static isApplicationRunning(bundleId) {
    if (!bundleId || typeof bundleId !== 'string') {
      throw new TypeError('Bundle ID必须是非空字符串')
    }
    return applications.isApplicationRunning(bundleId)
  }

  /**
   * 获取应用的详细信息
   * @param {string} path 应用路径
   * @returns {Object|null} 应用程序详细信息对象或null
   */
  static getApplicationDetails(path) {
    if (!path || typeof path !== 'string') {
      throw new TypeError('应用路径必须是非空字符串')
    }
    return applications.getApplicationDetails(path)
  }

}

// 导出单个静态方法
export const getAllApplications = ApplicationsManager.getAllApplications
export const getRunningApplications = ApplicationsManager.getRunningApplications
export const getApplicationByBundleId = ApplicationsManager.getApplicationByBundleId
export const searchApplicationsByName = ApplicationsManager.searchApplicationsByName
export const launchApplication = ApplicationsManager.launchApplication
export const terminateApplication = ApplicationsManager.terminateApplication
export const isApplicationRunning = ApplicationsManager.isApplicationRunning
export const getApplicationDetails = ApplicationsManager.getApplicationDetails

export default ApplicationsManager
