# Applications Module

一个用于获取和管理系统原生应用程序的 Node.js 原生模块

## 特性

- 🚀 **高性能**: 使用 C++/Objective-C++ 原生实现，性能优异
- 📱 **完整功能**: 支持获取、搜索、启动、终止应用程序
- 🔍 **智能搜索**: 支持模糊匹配和精确匹配
- 📊 **统计信息**: 提供应用程序统计和分类信息
- 💾 **缓存机制**: 内置智能缓存，提升性能
- 🛡️ **类型安全**: 完整的 TypeScript 类型定义
- 🔧 **易于使用**: 简洁的 API 设计

## 系统要求

- **操作系统**: macOS 10.12 或更高版本
- **Node.js**: 14.0 或更高版本
- **Xcode**: 最新版本（用于编译）

## 安装

### 自动安装

```bash
npm install
npm run build
```

### 平台特定要求

#### macOS

- Xcode Command Line Tools: `xcode-select --install`
- Node.js 和 npm
- Python (用于 node-gyp)

#### Windows

- Visual Studio Build Tools 或 Visual Studio
- Node.js 和 npm
- Python (用于 node-gyp)

#### Linux

- 构建工具: `sudo apt-get install build-essential`
- GTK 开发库: `sudo apt-get install libgtk-3-dev`
- Node.js 和 npm
- Python (用于 node-gyp)

### 构建脚本

```bash
# 完整构建
./scripts/build.sh

# 仅清理
./scripts/build.sh --clean-only

# 跳过测试
./scripts/build.sh --skip-tests

# 详细输出
./scripts/build.sh --verbose
```

## 快速开始

### JavaScript 使用

```javascript
const applications = require('./js/index.js')

// 获取所有应用程序
async function getAllApps() {
  try {
    const apps = await applications.getAllApplications()
    console.log(`找到 ${apps.length} 个应用程序`)
    apps.forEach(app => {
      console.log(`${app.name} (${app.bundleId})`)
    })
  } catch (error) {
    console.error('获取应用程序失败:', error)
  }
}

// 搜索应用程序
async function searchApps() {
  try {
    const results = await applications.searchApplicationsByName('Safari')
    console.log('搜索结果:', results)
  } catch (error) {
    console.error('搜索失败:', error)
  }
}

// 启动应用程序
async function launchApp() {
  try {
    const success = await applications.launchApplication('com.apple.Safari')
    console.log('启动结果:', success)
  } catch (error) {
    console.error('启动失败:', error)
  }
}

getAllApps()
searchApps()
launchApp()
```

### TypeScript 使用

```typescript
import applications, { ApplicationInfo, SearchOptions } from './types/index'

// 获取正在运行的应用程序
async function getRunningApps(): Promise<ApplicationInfo[]> {
  try {
    const runningApps = await applications.getRunningApplications()
    console.log(`正在运行 ${runningApps.length} 个应用程序`)
    return runningApps
  } catch (error) {
    console.error('获取运行中应用程序失败:', error)
    return []
  }
}

// 高级搜索
async function advancedSearch(): Promise<void> {
  const options: SearchOptions = {
    caseSensitive: false,
    exactMatch: true,
  }

  try {
    const results = await applications.searchApplicationsByName(
      'Finder',
      options
    )
    console.log('精确搜索结果:', results)
  } catch (error) {
    console.error('搜索失败:', error)
  }
}

// 使用管理器实例
async function useManager(): Promise<void> {
  const manager = applications.manager

  // 设置缓存超时为1分钟
  manager.setCacheTimeout(60000)

  // 获取统计信息
  const stats = await manager.getApplicationStatistics()
  console.log('应用程序统计:', stats)

  // 获取最近修改的应用
  const recent = await manager.getRecentlyModifiedApplications(5)
  console.log('最近修改的应用:', recent)
}
```

## API 文档

### 核心方法

#### `getAllApplications(useCache?: boolean): Promise<ApplicationInfo[]>`

获取所有已安装的应用程序。

**参数:**

- `useCache` (可选): 是否使用缓存，默认为 `true`

**返回:** 应用程序信息数组

#### `getRunningApplications(): Promise<ApplicationInfo[]>`

获取正在运行的应用程序。

**返回:** 正在运行的应用程序信息数组

#### `getApplicationByBundleId(bundleId: string): Promise<ApplicationInfo | null>`

根据 Bundle ID 获取应用信息。

**参数:**

- `bundleId`: Bundle 标识符

**返回:** 应用程序信息对象或 `null`

#### `searchApplicationsByName(name: string, options?: SearchOptions): Promise<ApplicationInfo[]>`

根据应用名称搜索应用。

**参数:**

- `name`: 应用名称（支持模糊匹配）
- `options` (可选): 搜索选项
  - `caseSensitive`: 是否区分大小写
  - `exactMatch`: 是否精确匹配

**返回:** 匹配的应用程序信息数组

#### `launchApplication(bundleId: string): Promise<boolean>`

启动指定的应用程序。

**参数:**

- `bundleId`: Bundle 标识符

**返回:** 是否启动成功

#### `terminateApplication(bundleId: string): Promise<boolean>`

终止指定的应用程序。

**参数:**

- `bundleId`: Bundle 标识符

**返回:** 是否终止成功

#### `isApplicationRunning(bundleId: string): Promise<boolean>`

检查应用是否正在运行。

**参数:**

- `bundleId`: Bundle 标识符

**返回:** 是否正在运行

#### `getApplicationDetails(path: string): Promise<ApplicationInfo | null>`

获取应用的详细信息。

**参数:**

- `path`: 应用路径

**返回:** 应用程序详细信息对象或 `null`

### 扩展方法

#### `getApplicationsByCategory(category: string): Promise<ApplicationInfo[]>`

根据分类获取应用程序。

#### `getRecentlyModifiedApplications(limit?: number): Promise<ApplicationInfo[]>`

获取最近修改的应用程序。

#### `getApplicationStatistics(): Promise<ApplicationStatistics>`

获取应用程序统计信息。

### 工具方法

#### `clearCache(): void`

清除缓存。

#### `setCacheTimeout(timeout: number): void`

设置缓存超时时间（毫秒）。

## 数据结构

### ApplicationInfo

```typescript
interface ApplicationInfo {
  name: string // 应用程序名称
  bundleId: string // Bundle标识符
  path: string // 应用程序路径
  version: string // 应用程序版本
  iconPath: string // 图标路径
  isRunning: boolean // 是否正在运行
  processId: number // 进程ID（如果正在运行）
  displayName: string // 显示名称
  category: string // 应用分类
  lastModified: number // 最后修改时间（Unix时间戳）
  fileSize: number // 文件大小（字节）
}
```

### ApplicationStatistics

```typescript
interface ApplicationStatistics {
  totalApplications: number // 总应用程序数量
  runningApplications: number // 正在运行的应用程序数量
  categories: Record<string, number> // 按分类统计
  totalSize: number // 总文件大小
  averageSize: number // 平均文件大小
}
```

## 错误处理

模块提供了完善的错误处理机制：

```javascript
try {
  const apps = await applications.getAllApplications()
  // 处理成功结果
} catch (error) {
  if (error.message.includes('Native module not available')) {
    console.error('原生模块不可用，可能是平台不支持')
  } else {
    console.error('其他错误:', error.message)
  }
}
```

## 性能优化

### 缓存机制

模块内置了智能缓存机制，可以显著提升性能：

```javascript
// 使用缓存（默认）
const apps1 = await applications.getAllApplications(true)

// 强制刷新
const apps2 = await applications.getAllApplications(false)

// 自定义缓存超时
applications.setCacheTimeout(60000) // 1分钟

// 清除缓存
applications.clearCache()
```

### 最佳实践

1. **合理使用缓存**: 对于不经常变化的数据（如已安装应用列表），建议使用缓存
2. **及时清理**: 在应用安装/卸载后，及时清除缓存
3. **错误处理**: 始终包装 try-catch 进行错误处理
4. **资源管理**: 避免频繁调用，合理控制调用频率

## 构建和开发

### 构建脚本

```bash
# 清理构建文件
npm run clean

# 构建
npm run build

# 重新构建
npm run rebuild

# 安装（包含构建）
npm run install
```

### 目录结构

```
packages/applications/
├── package.json              # 包配置
├── binding.gyp              # 构建配置
├── README.md                # 文档
├── src/                     # 原生代码
│   ├── applications_native.h
│   ├── applications_native.mm
│   ├── applications_addon.mm
│   └── applications_addon_stub.cc
├── js/                      # JavaScript接口
│   └── index.js
└── types/                   # TypeScript类型
    └── index.d.ts
```

## 技术实现

### 架构设计

- **原生层**: 平台特定的 C++ 实现，直接调用系统 API
- **绑定层**: Node.js N-API 绑定，提供统一的 JavaScript 接口
- **JavaScript层**: 高级封装，提供易用的跨平台 API

### 平台技术栈

#### macOS

- **API**: NSWorkspace, NSRunningApplication, NSBundle
- **语言**: Objective-C++
- **特性**: 完整的应用信息获取，包括沙盒应用

#### Windows

- **API**: Shell32, PSAPI, Version API
- **语言**: C++
- **特性**: 注册表扫描，开始菜单解析，进程管理

#### Linux

- **API**: 文件系统，/proc，D-Bus
- **语言**: C++
- **特性**: .desktop 文件解析，多包管理器支持 (APT, Snap, Flatpak)

### 共同特性

- **缓存机制**: 智能缓存，提升性能
- **异步处理**: 非阻塞操作，避免界面卡顿
- **错误处理**: 完善的错误处理和日志记录
- **类型安全**: TypeScript 类型定义

## 平台支持

- ✅ **macOS**: 完整支持，使用原生 Cocoa API
- ✅ **Windows**: 完整支持，使用 Windows API
- ✅ **Linux**: 完整支持，使用系统调用和文件系统API

### 平台特性对比

| 功能           | macOS | Windows | Linux |
| -------------- | ----- | ------- | ----- |
| 获取所有应用   | ✅    | ✅      | ✅    |
| 获取运行中应用 | ✅    | ✅      | ✅    |
| 应用搜索       | ✅    | ✅      | ✅    |
| 启动应用       | ✅    | ✅      | ✅    |
| 终止应用       | ✅    | ✅      | ✅    |
| 应用图标       | ✅    | ✅      | ✅    |
| 应用分类       | ✅    | ✅      | ✅    |
| 文件信息       | ✅    | ✅      | ✅    |

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0

- 初始版本
- 支持 macOS 平台
- 完整的应用程序管理功能
- TypeScript 类型支持
- 缓存机制
- 详细文档
