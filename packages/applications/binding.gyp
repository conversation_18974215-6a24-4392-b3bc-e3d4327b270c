{
  "targets": [
    {
      "target_name": "applications",
      "include_dirs": [
        "<!@(node -p \"require('node-addon-api').include\")",
        "include"
      ],
      "dependencies": [
        "<!(node -p \"require('node-addon-api').gyp\")"
      ],
      "defines": [
        "NAPI_DISABLE_CPP_EXCEPTIONS"
      ],
      "cflags!": [
        "-fno-exceptions"
      ],
      "cflags_cc!": [
        "-fno-exceptions"
      ],
      "msvs_settings": {
        "VCCLCompilerTool": {
          "ExceptionHandling": 1,
          "AdditionalOptions": [ "/bigobj", "/std:c++17", "/utf-8" ],
          "LanguageStandard": "stdcpp17"
        }
      },
      "conditions": [
        ['OS=="mac"', {
          "sources": [
            "src/applications_addon.mm",
            "src/applications_native.mm"
          ],
          "xcode_settings": {
            "GCC_ENABLE_CPP_EXCEPTIONS": "YES",
            "CLANG_CXX_LIBRARY": "libc++",
            "MACOSX_DEPLOYMENT_TARGET": "10.15",
            "OTHER_CFLAGS": [
              "-fobjc-arc"
            ],
            "OTHER_CPLUSPLUSFLAGS": ["-std=c++17", "-stdlib=libc++"],
            "OTHER_LDFLAGS": [
              "-framework Foundation",
              "-framework AppKit"
            ]
          }
        }],
        ['OS=="win"', {
          "sources": [
            "src/applications_addon_windows.cc",
            "src/applications_native_windows.cc"
          ],
          "conditions": [
            [ "target_arch=='ia32'", {
              "msvs_settings": {
                "VCCLCompilerTool": {
                  "AdditionalOptions": [ "/bigobj", "/std:c++17", "/utf-8" ],
                  "ExceptionHandling": 1,
                  "LanguageStandard": "stdcpp17"
                }
              }
            }],
            [ "target_arch=='x64'", {
              "msvs_settings": {
                "VCCLCompilerTool": {
                  "AdditionalOptions": [ "/bigobj", "/std:c++17", "/utf-8" ],
                  "ExceptionHandling": 1,
                  "LanguageStandard": "stdcpp17"
                }
              }
            }],
            [ "target_arch=='arm64'", {
              "msvs_settings": {
                "VCCLCompilerTool": {
                  "AdditionalOptions": [ "/bigobj", "/std:c++17", "/utf-8" ],
                  "ExceptionHandling": 1,
                  "LanguageStandard": "stdcpp17"
                }
              }
            }]
          ],
          "libraries": [
            "-lshell32.lib",
            "-lpsapi.lib",
            "-lversion.lib"
          ]
        }],
        ['OS=="linux"', {
          "sources": [
            "src/applications_addon_linux.cc",
            "src/applications_native_linux.cc"
          ],
          "cflags_cc": ["-std=c++17"],
          "libraries": ["-lpthread"]
        }]
      ]
    }
  ]
}