# 中文乱码解决方案

## 问题描述

在 Windows 系统中运行 Smart Toolbox 时，日志输出出现中文乱码问题。这是由于 Windows 控制台默认使用 GBK 编码，而 Node.js 应用输出的是 UTF-8 编码导致的。

## 解决方案

### 1. 编码工具类 (`EncodingUtils`)

创建了专门的编码工具类来处理编码问题：

- **位置**: `src/main/shared/utils/encoding-utils.ts`
- **功能**:
  - 自动检测并设置正确的编码
  - 提供安全的控制台输出方法
  - 支持编码转换和检查

### 2. 日志管理器优化

修改了 `LogManager` 类：

- 在构造函数中自动设置编码
- 文件写入时明确指定 UTF-8 编码
- 控制台输出使用安全的编码方法

### 3. 应用程序集成

在 `Application` 类中：

- 初始化时自动设置编码
- 使用安全的日志输出方法

### 4. 启动脚本优化

提供了多种启动方式：

- **标准启动**: `npm run start`
- **UTF-8 启动**: `npm run start:utf8`
- **批处理启动**: `scripts/start-with-encoding.bat`

## 使用方法

### 方法一：使用 UTF-8 启动脚本

```bash
npm run start:utf8
```

### 方法二：使用批处理文件（Windows）

```bash
scripts/start-with-encoding.bat
```

### 方法三：手动设置环境变量

```bash
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8
set LC_ALL=zh_CN.UTF-8
npm run start
```

## 技术细节

### 编码设置

1. **进程编码**:
   - 设置 `process.stdout.setEncoding('utf8')`
   - 设置 `process.stderr.setEncoding('utf8')`

2. **环境变量**:
   - `PYTHONIOENCODING=utf-8`
   - `LANG=zh_CN.UTF-8`
   - `LC_ALL=zh_CN.UTF-8`

3. **控制台代码页**:
   - Windows: 尝试设置为 65001 (UTF-8)
   - 自动检测 VSCode 终端环境

### 文件编码

- 所有日志文件使用 UTF-8 编码写入
- 明确指定 `{ encoding: 'utf8' }` 参数

## 测试验证

运行编码测试脚本：

```bash
node scripts/test-encoding.cjs
```

测试内容包括：
- 中文字符显示
- 环境变量检查
- 编码支持检查
- 文件读写测试

## 兼容性

- ✅ Windows 10/11
- ✅ VSCode 终端
- ✅ PowerShell
- ✅ CMD
- ✅ Git Bash

## 注意事项

1. **VSCode 终端**: 通常已经支持 UTF-8，无需额外设置
2. **传统 CMD**: 可能需要手动运行 `chcp 65001`
3. **PowerShell**: 建议使用 PowerShell 7+
4. **文件编辑器**: 确保源文件保存为 UTF-8 编码

## 故障排除

### 问题：仍然出现乱码

1. 检查终端是否支持 UTF-8
2. 手动运行 `chcp 65001`
3. 使用 `npm run start:utf8` 启动
4. 检查文件编码是否为 UTF-8

### 问题：环境变量未生效

1. 重启终端
2. 使用批处理文件启动
3. 检查系统环境变量设置

### 问题：日志文件乱码

1. 确认文件编码设置
2. 使用支持 UTF-8 的文本编辑器打开
3. 检查 `writeFileSync` 的编码参数
