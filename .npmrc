# For China 🇨🇳 developers
registry=https://registry.npmmirror.com/
electron_mirror=https://npmmirror.com/mirrors/electron/
electron_builder_binaries_mirror=https://npmmirror.com/mirrors/electron-builder-binaries/
sqlite3_binary_host_mirror=https://npmmirror.com/mirrors/sqlite3/
sass_binary_site=https://npmmirror.com/mirrors/node-sass/
chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver/
operadriver_cdnurl=https://npmmirror.com/mirrors/operadriver/
fse_binary_host_mirror=https://npmmirror.com/mirrors/fsevents/
node-linker=hoisted


# ========================================
# pnpm 核心配置
# ========================================

# 严格的对等依赖检查 - 设为 false 避免版本冲突
strict-peer-dependencies=false

# 自动安装对等依赖
auto-install-peers=true

# 提升依赖到根目录，减少重复安装
shamefully-hoist=true

# 依赖链接方式：hoisted(提升) | isolated(隔离) | pnp(即插即用)
node-linker=hoisted

side-effects-cache=false

# ========================================
# 安装配置
# ========================================

# 安装时是否显示进度条
progress=true

# 安装时是否显示详细信息 
# 日志级别：error, warn, info, http, verbose, silly
loglevel=info

# 安装时是否使用颜色输出
color=true

# 安装时是否显示时间戳
timing=true

# 安装时是否显示网络请求信息
network-timeout=60000

# 安装时是否显示网络请求重试次数
network-concurrency=16

# 安装时是否显示网络请求重试延迟
network-retry-mintimeout=2000

# 安装时是否显示网络请求重试最大延迟
network-retry-maxtimeout=60000

# ========================================
# 依赖管理配置
# ========================================

# 是否自动安装对等依赖
auto-install-peers=true

# 是否严格检查对等依赖版本
strict-peer-dependencies=false

# 是否提升对等依赖
hoist-pattern[]=*

# 是否提升开发依赖
hoist-pattern[]=*eslint*
hoist-pattern[]=*prettier*
hoist-pattern[]=*typescript*

# 是否提升测试相关依赖
hoist-pattern[]=*jest*
hoist-pattern[]=*test*
hoist-pattern[]=*spec*

# ========================================
# 工作区配置
# ========================================

# 链接工作区包
link-workspace-packages=true

# 工作区包优先级
prefer-workspace-packages=true

# 是否共享工作区依赖
shared-workspace-lockfile=true

# 是否安装工作区根依赖
include-workspace-root=true

# ========================================
# 安全配置
# ========================================

# 是否启用审计
audit=false

# 是否启用安全更新
security-update=false

# 是否启用安全检查
security-check=false

# ========================================
# 性能配置
# ========================================

# 并行安装数量
child-concurrency=16

# 网络并发数
network-concurrency=16

# 是否启用并行安装
parallel=true

# 是否启用增量安装
frozen-lockfile=false

# 是否启用预构建
prefer-offline=true

# ========================================
# 开发配置
# ========================================

# 是否启用开发模式
dev=false

# 是否安装可选依赖
optional=true

# 是否安装开发依赖
include=dev

# 是否安装对等依赖
include=peer

# ========================================
# 发布配置
# ========================================

# 发布时是否启用访问令牌
access=public

# 发布时是否启用标签
tag=latest

# 发布时是否启用 Git 标签
git-checks=true

# ========================================
# 脚本配置
# ========================================

# 是否启用脚本
enable-pre-post-scripts=true

# 是否启用生命周期脚本
enable-scripts=true

# 是否启用二进制脚本
enable-binary-scripts=true

# ========================================
# 其他配置
# ========================================

# 是否启用交互式提示
interactive=false

# 是否启用确认提示
confirm=false

# 是否启用自动确认
yes=false

# 是否启用静默模式
silent=false

# 是否启用详细输出
verbose=false

# 是否启用调试模式
debug=false

# 是否启用跟踪模式
trace=false

# ========================================
# Electron 特定配置
# ========================================

# Electron 镜像地址 - 使用淘宝镜像
ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/
# 阿里云采用双份拷贝策略,即x.x版本的electron存储时既有 vx.x也有x.x
ELECTRON_CUSTOM_DIR={{ version }}
# # Electron 构建工具镜像
ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/

# ========================================
# 网络配置
# ========================================
# 代理设置（如果需要）
# proxy=http://127.0.0.1:7897
# https-proxy=http://127.0.0.1:7897

# ========================================
# 缓存配置
# ========================================

# 是否启用缓存
cache=true

# 缓存过期时间（毫秒）
cache-max=Infinity

# 缓存最小时间（毫秒）
cache-min=0
