/**
 * 窗口服务类型定义文件
 */

// ============================================================================
// 窗口信息类型
// ============================================================================

/**
 * 窗口信息接口
 */
interface WindowInfo {
  /** 窗口ID */
  id: number;
  /** 窗口标题 */
  title: string;
  /** 窗口是否可见 */
  visible: boolean;
  /** 窗口是否最小化 */
  minimized: boolean;
  /** 窗口是否最大化 */
  maximized: boolean;
  /** 窗口是否全屏 */
  fullscreen: boolean;
  /** 窗口是否聚焦 */
  focused: boolean;
  /** 窗口位置 */
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  /** 窗口类型 */
  type?: string;
  /** 窗口URL */
  url?: string;
}

/**
 * 窗口创建选项
 */
interface WindowCreateOptions {
  /** 窗口宽度 */
  width?: number;
  /** 窗口高度 */
  height?: number;
  /** 窗口X坐标 */
  x?: number;
  /** 窗口Y坐标 */
  y?: number;
  /** 最小宽度 */
  minWidth?: number;
  /** 最小高度 */
  minHeight?: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 最大高度 */
  maxHeight?: number;
  /** 是否可调整大小 */
  resizable?: boolean;
  /** 是否显示 */
  show?: boolean;
  /** 窗口标题 */
  title?: string;
  /** 窗口图标 */
  icon?: string;
  /** 是否置顶 */
  alwaysOnTop?: boolean;
  /** 是否全屏 */
  fullscreen?: boolean;
  /** 是否模态窗口 */
  modal?: boolean;
  /** 父窗口ID */
  parent?: number;
  /** 窗口类型 */
  type?: string;
  /** 加载的URL或路由 */
  url?: string;
}

/**
 * 窗口操作结果
 */
interface WindowOperationResult {
  /** 操作是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 返回数据 */
  data?: any;
}

/**
 * 窗口位置和大小
 */
interface WindowBounds {
  /** X坐标 */
  x: number;
  /** Y坐标 */
  y: number;
  /** 宽度 */
  width: number;
  /** 高度 */
  height: number;
}

/**
 * 窗口状态
 */
interface WindowState {
  /** 是否可见 */
  visible: boolean;
  /** 是否最小化 */
  minimized: boolean;
  /** 是否最大化 */
  maximized: boolean;
  /** 是否全屏 */
  fullscreen: boolean;
  /** 是否聚焦 */
  focused: boolean;
  /** 是否置顶 */
  alwaysOnTop: boolean;
}

// ============================================================================
// 窗口事件类型
// ============================================================================

/**
 * 窗口事件类型
 */
type WindowEventType = 
  | 'ready-to-show'
  | 'show'
  | 'hide'
  | 'focus'
  | 'blur'
  | 'minimize'
  | 'maximize'
  | 'unmaximize'
  | 'restore'
  | 'resize'
  | 'move'
  | 'close'
  | 'closed';

/**
 * 窗口事件数据
 */
interface WindowEventData {
  /** 窗口ID */
  windowId: number;
  /** 事件类型 */
  type: WindowEventType;
  /** 事件数据 */
  data?: any;
}

// ============================================================================
// 窗口管理器类型
// ============================================================================

/**
 * 窗口管理器状态
 */
interface WindowManagerStatus {
  /** 是否已初始化 */
  initialized: boolean;
  /** 活跃窗口数量 */
  activeWindows: number;
  /** 主窗口ID */
  mainWindowId?: number;
  /** 所有窗口ID列表 */
  windowIds: number[];
}

/**
 * 窗口过滤选项
 */
interface WindowFilterOptions {
  /** 窗口类型 */
  type?: string;
  /** 是否可见 */
  visible?: boolean;
  /** 是否最小化 */
  minimized?: boolean;
  /** 是否聚焦 */
  focused?: boolean;
}
