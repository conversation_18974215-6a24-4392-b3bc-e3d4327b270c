interface DbDoc {
  _id: string
  _rev?: string
  [key: string]: any
}

interface DbResult {
  id: string
  rev?: string
  ok?: boolean
  error?: boolean
  name?: string
  message?: string
  [key: string]: any
}

interface DatabaseApi {
  put(doc: DbDoc): Promise<DbResult>
  get(id: string): Promise<DbDoc | null>
  remove(docOrId: DbDoc | string): Promise<DbResult>
  bulkDocs(docs: DbDoc[]): Promise<DbResult[]>
  allDocs(idStartsWithOrIds?: string | string[]): Promise<DbDoc[]>
}
