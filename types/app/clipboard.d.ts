type ClipboardContentType = 'text' | 'image' | 'files' | 'html' | 'empty'

interface ClipboardData {
  text?: string
  image?: Buffer
  files?: string[]
  html?: string
  contentType: Array<ClipboardContentType>
}

interface ClipboardApi {
  /**
   * 监听剪贴板变化
   * @param callback 回调函数
   * @returns 取消监听的函数
   */
  watchHandler: (callback: (data: ClipboardData) => void) => () => void
  /**
   * 检查剪贴板是否有数据
   */
  has: () => Promise<boolean>
  /**
   * 设置剪贴板数据
   */
  set: (data: ClipboardData) => void
  /**
   * 获取剪贴板数据
   */
  get: () => Promise<ClipboardData>
  /**
   * 清空剪贴板数据
   */
  clear: () => void
}
