// theme
type Theme = 'system' | 'light' | 'dark'

interface Appearance {
  theme: Theme
  fontFamily: string
}

interface Base {
  // 数据路径
  dataPath: string
}

interface CommonApi {
  debug: () => void
  isMac: () => boolean
  isLinux: () => boolean
  isWindows: () => boolean
  isDev: () => boolean
  isProd: () => boolean
  setTheme(theme: Theme): void
  isDarkTheme(): Promise<boolean>

  // file
  selectFile(options: Electron.OpenDialogOptions): Promise<string[] | undefined>
  saveFile(data: string | Buffer): Promise<string>
}
