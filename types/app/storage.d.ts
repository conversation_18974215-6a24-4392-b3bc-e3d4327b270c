type SchemaType = {
  // 外观
  appearance: Appearance
  // 基础
  base: Base
}

type DotNotationKeyOf<T extends Record<string, any>> = {
  [K in keyof Required<T>]: K extends string
    ? Required<T>[K] extends Record<string, any>
      ? K | `${K}.${DotNotationKeyOf<Required<T>[K]>}`
      : K
    : never
}[keyof T]

type DotNotationValueOf<
  T extends Record<string, any>,
  K extends DotNotationKeyOf<T>,
> = K extends `${infer Head}.${infer Tail}`
  ? Head extends keyof T
    ? T[Head] extends Record<string, any>
      ? Tail extends DotNotationKeyOf<T[Head]>
        ? DotNotationValueOf<T[Head], Tail>
        : never
      : Required<T>[Head] extends Record<string, any>
        ? Tail extends DotNotationKeyOf<Required<T>[Head]>
          ? DotNotationValueOf<Required<T>[Head], Tail> | undefined
          : never
        : never
    : never
  : K extends keyof T
    ? T[K]
    : never

interface StorageApi {
  /**
   * 设置值
   * @param key 键
   * @param value 值
   */
  set: (
    key: DotNotationKeyOf<SchemaType>,
    value: DotNotationValueOf<SchemaType, typeof key>
  ) => void
  /**
   * 获取值
   * @param key 键
   * @param defaultValue 默认值
   */
  get: (
    key: DotNotationKeyOf<SchemaType>,
    defaultValue: any
  ) => Promise<any>
  /**
   * 检查是否存在某个键
   */
  has: (key: DotNotationKeyOf<SchemaType>) => Promise<boolean>
  /**
   * 删除值
   * @param key 键
   */
  delete: (key: DotNotationKeyOf<SchemaType>) => void
  /**
   * 清空所有存储
   */
  clear: () => void
  /**
   * 重置存储
   */
  reset: () => void
}
