type ProviderType = 'openai' | 'azure'
type ModelTool = 'function' | 'deep'


interface Model {
  id: string
  name: string
  group: string
  tools: string[]
  enabled: boolean
  description: string
}

interface ModelProvider {
  id: string
  name: string
  icon: string
  webSite: string
  baseUrl: string
  apiKey: string
  type: ProviderType
  enabled: boolean
  defaultHeaders?: Record<string, string>
  models: Model[]
}
