import { fileURLToPath } from 'url'
import { defineConfig, type Plugin, type ResolvedConfig } from 'vite'

function restart(): Plugin {
  let config: ResolvedConfig
  return {
    name: 'electron-forge-plugin-vite-restart',
    configResolved(_config) {
      config = _config
    },
    closeBundle() {
      if (config.mode === 'production') {
        // https://github.com/electron/forge/blob/98b621dcace753c1bd33aeb301c64d03335abfdc/packages/plugin/vite/src/ViteConfig.ts#L36-L41
        return
      }
      process.stdin.emit('data', 'rs')
    },
  }
}
// https://vitejs.dev/config
export default defineConfig({
  plugins: [restart()],
  build: {
    lib: {
      entry: 'src/main/index.ts',
      formats: ['es'],
      fileName: () => 'main.js',
    },
    rollupOptions: {
      external: [
        'electron',
        'electron-squirrel-startup',
        'sqlite3',
        'typeorm',
        'pg-hstore',
        '@smart/clipboard',
        '@smart/applications',
      ],
    },
    target: 'node24',
    outDir: '.vite/build',
    emptyOutDir: false,
  },
  resolve: {
    alias: {
      '@main': fileURLToPath(new URL('../src/main', import.meta.url)),
      '@preload': fileURLToPath(new URL('../src/preload', import.meta.url)),
      '@shared': fileURLToPath(new URL('../src/shared', import.meta.url)),
    },
  },
})
