import { fileURLToPath } from 'node:url'
import { defineConfig } from 'vite'

process['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'
// https://vitejs.dev/config
export default defineConfig({
  build: {
    lib: {
      entry: 'src/preload/index.ts',
      formats: ['es'],
      fileName: () => 'index.js',
    },
    rollupOptions: {
      external: ['electron'],
    },
    target: 'node24',
    outDir: '.vite/build/preload',
    emptyOutDir: false,
  },
  resolve: {
    alias: {
      '@main': fileURLToPath(new URL('../src/main', import.meta.url)),
      '@preload': fileURLToPath(new URL('../src/preload', import.meta.url)),
      '@shared': fileURLToPath(new URL('../src/shared', import.meta.url)),
    },
  },
})
