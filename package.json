{"name": "smart-toolbox", "productName": "smart-toolbox", "version": "1.0.0", "description": "My Electron application description", "main": ".vite/build/main.js", "type": "module", "scripts": {"prepare": "node build/install.cjs", "dev": "vite", "build": "vite build", "start": "electron-forge start", "start:utf8": "cross-env PYTHONIOENCODING=utf-8 LANG=zh_CN.UTF-8 LC_ALL=zh_CN.UTF-8 electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "keywords": [], "author": {"name": "xfo79k", "email": "<EMAIL>"}, "license": "MIT", "pnpm": {"onlyBuiltDependencies": ["electron"]}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron-forge/plugin-vite": "^7.8.1", "@electron/fuses": "^1.8.0", "@eslint/js": "^9.30.1", "@hookform/resolvers": "^5.1.1", "@iconify/json": "^2.2.360", "@iconify/tailwind": "^1.2.0", "@iconify/tailwind4": "^1.0.6", "@nut-tree/nut-js": "^4.2.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.124.0", "@tanstack/router-plugin": "^1.124.0", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "ajv": "^8.17.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "electron": "37.2.0", "embla-carousel-react": "^8.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "moment": "^2.30.1", "motion": "^12.23.0", "next-themes": "^0.4.6", "prettier": "^3.6.2", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-markdown": "10", "react-resizable-panels": "^3.0.3", "react-router": "^7.6.3", "react-toastify": "^11.0.5", "remark-gfm": "4", "sequelize": "^6.37.7", "shiki": "3.7.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.4", "typeorm": "^0.3.25", "typescript": "^5.0.0", "typescript-eslint": "^8.35.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0", "zod": "^3.25.71", "zustand": "^5.0.6"}, "dependencies": {"@smart/applications": "workspace:*", "@smart/clipboard": "workspace:*", "@tanstack/react-router-devtools": "^1.124.0", "dayjs": "^1.11.13", "electron-squirrel-startup": "^1.0.1", "electron-store": "^10.1.0", "openai": "^5.8.2", "pg-hstore": "^2.3.4", "sqlite3": "^5.1.7", "uiohook-napi": "^1.5.4"}}