/**
 * ESLint 9 Flat Config for React + TypeScript + Prettier
 *
 * 这是基于ESLint 9的新配置系统（Flat Config）的配置文件
 *
 * 特性：
 * - ✅ 使用单引号，结尾不要分号
 * - ✅ 允许使用any类型
 * - ✅ 完全兼容Prettier（无冲突规则）
 * - ✅ 支持React 19 + TypeScript
 * - ✅ 包含React Hooks和无障碍检查
 * - ✅ 使用typescript-eslint的最新版本
 */
import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import react from 'eslint-plugin-react'
import jsxA11y from 'eslint-plugin-jsx-a11y'
import tseslint from 'typescript-eslint'
import prettierConfig from 'eslint-config-prettier'

export default tseslint.config(
  // 忽略文件
  {
    ignores: ['node_modules/**', 'dist/**', '.vite/**', 'out/**', '*.d.ts'],
  },

  // 基础JavaScript配置
  js.configs.recommended,

  // TypeScript配置
  ...tseslint.configs.recommended,

  // Prettier配置 - 关闭与prettier冲突的规则
  prettierConfig,

  // 全局配置
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2020,
      },
      ecmaVersion: 2020,
      sourceType: 'module',
    },
  },

  // React 相关配置
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      react,
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'jsx-a11y': jsxA11y,
    },
    languageOptions: {
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
    rules: {
      // React 规则
      'react/react-in-jsx-scope': 'off', // React 17+ 不需要导入 React
      'react/prop-types': 'off', // TypeScript 已经提供类型检查
      'react/jsx-uses-react': 'off',
      'react/jsx-uses-vars': 'error',
      'react/jsx-key': 'error',
      'react/jsx-no-duplicate-props': 'error',
      'react/jsx-no-undef': 'error',
      'react/no-unescaped-entities': 'warn',

      // React Hooks 规则
      ...reactHooks.configs.recommended.rules,

      // 可访问性规则
      'jsx-a11y/alt-text': 'warn',
      'jsx-a11y/anchor-has-content': 'warn',
      'jsx-a11y/click-events-have-key-events': 'warn',
      'jsx-a11y/no-static-element-interactions': 'warn',
      

      // TypeScript 规则
      '@typescript-eslint/no-explicit-any': 'off', // 允许使用any
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-unused-vars': 'off',

      // 通用规则
      'no-console': 'off',
      'no-debugger': 'warn',
      'no-unused-vars': 'off', // 使用 TypeScript 版本
      'prefer-const': 'warn',
      'no-var': 'warn',
    },
  },

  // 配置文件特殊规则
  {
    files: ['*.config.{js,ts,mjs}', 'forge.config.ts', 'vite.*.config.ts'],
    rules: {
      'no-console': 'off',
      '@typescript-eslint/no-var-requires': 'off',
    },
  }
)
